spring:
  datasource:
    druid:
#      #Oracle
      driver-class-name: oracle.jdbc.OracleDriver
      url: **************************************
      username: luzhou_management_center
      password: luzhou_management_center
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  #RabbitMQ配置
  rabbitmq:
    host: **********
    port: 5672
    username: xygk
    password: xygk@123
    virtual-host: /
  redis:
    database: 0
    host: **********
    port: 6379
    password:    # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
#模板地址
expFile:
  attendanceRecord: D:/secondCenter/template/AttendanceRecord.xlsx    #考勤表导出模板文件路径
  payrollRegister: D:/secondCenter/template/PayrollRegister.xlsx    #工资发放花名册导出模板文件路径
  workerRecord: D:/secondCenter/template/WorkerRecord.xlsx   #备案表导出模板文件路径
  projectLedger: D:/secondCenter/template/ProjectLedger.xlsx    #项目台账导出模板文件路径
  contractExport: D:/secondCenter/template/建筑工人简易劳动合同书.docx
  #  contractExport: /home/<USER>/导出模板/四川省建筑工人简易劳动合同书.docx
  salaryadvanceExport: D:/secondCenter/template/salaryadvance.xlsx #工资预发模板
  salaryExport: D:/secondCenter/template/工资导出模板.xlsx #宜宾工资导出模板
  billboardExport: D:/secondCenter/template/四川省建筑施工现场农民工维权告示牌.xlsx #四川省建筑施工现场农民工维权告示牌
  promiseExport: D:/secondCenter/template/建筑工人安全承诺书.docx #建筑工人安全承诺书
  teamPromiseExport: D:/secondCenter/template/工地班组安全承诺书.docx #工地班组安全承诺书
  attendancetjExport: D:/secondCenter/template/项目考勤统计导出模板.xlsx #项目考勤统计
  rosterExport: D:/secondCenter/template/项目工人花名册导出模板.xlsx #项目工人花名册统计
  pa02ImpUrl: D:/secondCenter/template/工资专户流水导入模板.xlsx #工资专户流水导入模板
  pa03ImpUrl: D:/secondCenter/template/工资代发明细导入模板.xlsx #工资代发明细导入模板
  importBankDataExport: D:/secondCenter/template/农民工工资银行卡导入模板.xlsx #农民工工资银行卡导入模板
  importWorkerContactExport: D:/secondCenter/template/农民工工人合同导入模板.xlsx #农民工工人合同导入模板
  pw02ImpUrl: D:/secondCenter/template/工资表导入模板.xlsx #工资表导入模板