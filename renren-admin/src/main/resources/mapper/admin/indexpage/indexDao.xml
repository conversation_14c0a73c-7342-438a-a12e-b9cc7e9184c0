<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.admin.indexpage.dao.indexDao">
    <resultMap id="ProjectDTO" type="io.renren.modules.admin.indexpage.dto.ProjectDTO">
        <result property="name" column="name"/>
        <result property="owner" column="owner"/>
        <result property="generalUnit" column="generalunit"/>
        <result property="address" column="address"/>
        <result property="region" column="region"/>
        <result property="constructionPermit" column="constructionPermit"/>
        <result property="code" column="CODE"/>
        <result property="state" column="state"/>
    </resultMap>

    <resultMap id="PersonNumberDTO" type="io.renren.modules.admin.indexpage.dto.PersonNumberDTO">
        <result property="workerNum" column="workernum"/>
        <result property="bePresentNum" column="bepresentnum"/>
        <result property="tmNum" column="tmnum"/>
        <result property="managementNum" column="managementnum"/>
    </resultMap>

    <select id="getProjectInfo" resultType="io.renren.modules.admin.indexpage.dto.ProjectDTO" resultMap="ProjectDTO">
        select t.name,
               (select y.dict_label
                  from sys_dict_type x, sys_dict_data y
                 where x.id = y.dict_type_id
                   and x.dict_type = 'PRJSTATUS'
                   and y.dict_value = t.prjstatus) as prjstatus,
               t.address,
               (select x.builderlicensenum from b_pj02 x where x.pj0101 = t.pj0101) as constructionPermit,
               (select y.dict_label
                  from sys_dict_type x, sys_dict_data y
                 where x.id = y.dict_type_id
                   and x.dict_type = 'VIRAREACODE'
                   and y.dict_value = a.areacode) as virareacode,
               (select x.corpname
                  from b_cp01 x, b_cp02 y
                 where x.cp0101 = y.cp0101
                   and y.pj0101 = t.pj0101
                   and y.corptype = '7') as supervisionUnit,
               (select x.corpname
                  from b_cp01 x, b_cp02 y
                 where x.cp0101 = y.cp0101
                   and y.pj0101 = t.pj0101
                   and y.corptype = '8'
                   and y.in_or_out = '1'
                   and rownum &lt; 2) as owner,
               (select x.corpname
                  from b_cp01 x, b_cp02 y
                 where x.cp0101 = y.cp0101
                   and y.pj0101 = t.pj0101
                   and y.corptype = '9'
                   and y.in_or_out = '1'
                   and rownum &lt; 2) as generalUnit
          from b_pj01 t, sys_dept a
         where t.dept_id = a.id
         and t.pj0101= #{pj0101}
    </select>

    <select id="getPersonNumber" resultType="io.renren.modules.admin.indexpage.dto.PersonNumberDTO"
            resultMap="PersonNumberDTO">
        SELECT k.* , (k.workerNum + k.managementNum) as bepresentnum FROM (
        select
        (SELECT count(*) FROM b_ps02 a where a.pj0101 = p.pj0101 and  a.IN_OR_OUT = 1)
        workernum,
        (SELECT COUNT(*) FROM b_tm01 d where d.pj0101 = p.pj0101 and  d.IN_OR_OUT = 1)
        tmnum,
        (SELECT COUNT(*) FROM b_ps04 e, b_ps03 z where e.ps0301 = z.ps0301 and e.pj0101 = p.pj0101 and  e.IN_OR_OUT = 1)
        managementnum
        from r_pj01_dept t, b_pj01 p
        where t.pj0101 = p.pj0101
        and t.DEPT_ID = #{deptId})k

    </select>

    <select id="getAttendanceNumber" resultType="io.renren.modules.admin.indexpage.dto.AttendanceNumberDTO">
        SELECT (select count(1)
                  from b_tj04 x
                 where x.pj0101 = p.pj0101
                   and x.person_type = '1'
                   and x.kqstatus = '1'
                   and to_char(x.kqday, 'yyyy-MM-dd') =
                       to_char(sysdate, 'yyyy-MM-dd')) as workerAttendanceNum,
               (select count(1)
                  from b_tj04 x
                 where x.pj0101 = p.pj0101
                   and x.person_type = '2'
                   and x.kqstatus = '1'
                   and to_char(x.kqday, 'yyyy-MM-dd') =
                       to_char(sysdate, 'yyyy-MM-dd')) as managementAttendanceNum
          FROM r_pj01_dept t, b_pj01 p
         where p.pj0101 = t.pj0101
           and t.dept_id = #{deptId}
    </select>

    <select id="getTotalPayment" resultType="java.math.BigDecimal">
        SELECT
        ( select nvl(sum(c.accountnum),0) from b_pj15 c where c.pj0101 = p.pj0101 and c.flag = '1' and c.issuedetail =
        '1')
        FROM r_pj01_dept t,b_pj01 p where p.pj0101 = t.pj0101 and t.dept_id = #{deptId}
    </select>

    <select id="getParticipatingUnits" resultType="io.renren.modules.admin.indexpage.dto.ParticipatingUnitsDTO">
        select c1.corpname as name , y.dict_label as type,

        (SELECT COUNT(distinct t.ps0201)
        FROM b_ps02 t,b_tm01 m
        where t.tm0101 = m.tm0101 and m.cp0201 = c2.cp0201 and t.pj0101 = p.pj0101
        and t.IN_OR_OUT = 1)
        +
        (SELECT count(distinct s4.ps0401)
        from b_ps04 s4
        WHERE s4.cp0201 = c2.cp0201
        and s4.in_or_out = 1) as personNum

        from b_cp01 c1,b_cp02 c2 ,r_pj01_dept t,b_pj01 p ,SYS_DICT_DATA y,sys_dict_type d
        where c1.cp0101 = c2.cp0101 and c2.pj0101 = p. pj0101 and p.pj0101 = t.pj0101 and y.dict_type_id = d.id
        and c2.CORPTYPE = y.dict_value and d.dict_type = 'CORPTYPE' and t.dept_id = #{deptId}
    </select>

    <select id="getManagerAttendance" resultType="io.renren.modules.admin.indexpage.dto.ManagerAttendanceDTO">
        select (select y.dict_label
                from SYS_DICT_DATA y, sys_dict_type d
                where y.dict_type_id = d.id
                  and d.dict_type = 'CORPTYPE'
                  and y.dict_value = t.corptype) || '-' ||
               (select y.dict_label
                from SYS_DICT_DATA y, sys_dict_type d
                where y.dict_type_id = d.id
                  and d.dict_type = 'JOBTYPE'
                  and y.dict_value = t.jobtype) as managerType,
               t.name as managerName,
               (SELECT COUNT(*)
                FROM b_kq02 q
                where q.USER_ID = t.ps0401
                  and to_char(q.checkdate, 'yyyy-mm-dd') =
                      to_char(sysdate, 'yyyy-mm-dd')) as managerAttendance

        from (SELECT p2.corptype, p3.jobtype, p1.name, p3.ps0401
              FROM b_ps04 p3, b_ps03 z, b_cp02 p2, b_ps01 p1, b_pj01 p, r_pj01_dept r
              where r.pj0101 = p.pj0101
                and p3.ps0301 = z.ps0301
                and z.ps0101 = p1.ps0101
                and p3.pj0101 = p.pj0101
                and p3.cp0201 = p2.cp0201
                and p3.in_or_out = '1'
                and r.dept_id = #{deptId}) t
    </select>

    <select id="getFileInfoDTO" resultType="io.renren.modules.admin.indexpage.dto.FileInfoDTO">


    SELECT t3.archivesname as name ,decode(q.total,0,0,null,0,1) as state FROM (
        SELECT count(p2.pj0201)as total,p2.ot0301 FROM b_pj02 p2 ,r_pj01_dept t,b_pj01 p
        where p2.pj0101 = p.pj0101 and p.pj0101 = t.pj0101
        and to_char(p2.archivesdate,'yyyymm')= to_char(ADD_MONTHS(SYSDATE,-1),'yyyymm')
        and t.DEPT_ID = #{deptId} group by p2.ot0301
	)q right join B_OT03 t3 on t3.ot0301 = q.ot0301 where t3.updatetype = 1
	 union
	SELECT t3.archivesname as name ,decode(q.total,0,0,null,0,1) as state FROM (
        SELECT count(p2.pj0201)as total,p2.ot0301 FROM b_pj02 p2 ,r_pj01_dept t,b_pj01 p
        where p2.pj0101 = p.pj0101 and p.pj0101 = t.pj0101
        and t.DEPT_ID = #{deptId} group by p2.ot0301
	)q right join B_OT03 t3 on t3.ot0301 = q.ot0301 where t3.updatetype = 0
	union
	SELECT t3.archivesname as name,decode(q.total,0,0,null,0,1) as state FROM (
        SELECT count(p2.pj0201)as total,p2.ot0301 FROM b_pj02 p2 ,r_pj01_dept t,b_pj01 p
        where p2.pj0101 = p.pj0101 and p.pj0101 = t.pj0101 and to_char(p2.archivesdate,'q')=to_char(sysdate,'q')
        and to_char(p2.archivesdate,'yyyy')=to_char(sysdate,'yyyy')
        and t.DEPT_ID = #{deptId} group by p2.ot0301
	)q right join B_OT03 t3 on t3.ot0301 = q.ot0301 where t3.updatetype = 2


    </select>
    <select id="getMonthAttendance" resultType="io.renren.modules.admin.indexpage.dto.MonthAttendanceDTO">
        SELECT to_char(d.time, 'yyyy/MM/dd') as time,
               nvl(b.AttendanceNum, 0) as AttendanceNum
          FROM (select trunc(sysdate - 15) + rownum as time
                  from dual
                connect by 15 >= rownum) d
          left join (SELECT count(1) as AttendanceNum,
                            to_char(k.kqday, 'yyyymmdd') as time
                       FROM b_tj04 k, r_pj01_dept t, b_pj01 p
                      where k.pj0101 = p.pj0101
                        and p.pj0101 = t.pj0101
                        and k.kqstatus = '1'
                        and k.kqday >= (sysdate - 15)
                        and t.dept_id = #{deptId}
                      group by to_char(k.kqday, 'yyyymmdd')
                      order by to_char(k.kqday, 'yyyymmdd')) b
            on to_char(d.time, 'yyyymmdd') = b.time
         order by d.time
    </select>

    <select id="getEquipmentInfo" resultType="io.renren.modules.admin.indexpage.dto.EquipmentInfoDTO">
        select h.*,
               case
                 when h.linedate > 1 or h.linedate is null then
                  '0'
                 else
                  '1'
               end as linestatus
          from (select t.sn,
                       a.name as supplier,
                       ((sysdate - x.create_date) * 24) as linedate
                  from SUP_DEVICE t
                  left join (select t.sn, max(t.create_date) as create_date
                              from sup_log_operation t
                             where t.create_date > sysdate - 1 / 24
                             group by t.sn) x
                    on t.sn = x.sn, sup_supplier a
                 where t.supplier_id = a.id
                   and t.pj0101 = #{pj0101}) h
    </select>

</mapper>