<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.tjattendanceinfo.dao.TjAttendanceInfoDao">

    <select id="getTjAttendanceInfoList"
            resultType="io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoPageExportDTO">

        select t.id,
               t.pj0101,
               m.teamname,
               t.user_id,
               t.person_type,
               t.workername,
               t.jobtype,
               t.in_or_out,
               t.exittime,
               t.kqmonth,
               t.kqdays,
               t.day1,
               t.day2,
               t.day3,
               t.day4,
               t.day5,
               t.day6,
               t.day7,
               t.day8,
               t.day9,
               t.day10,
               t.day11,
               t.day12,
               t.day13,
               t.day14,
               t.day15,
               t.day16,
               t.day17,
               t.day18,
               t.day19,
               t.day20,
               t.day21,
               t.day22,
               t.day23,
               t.day24,
               t.day25,
               t.day26,
               t.day27,
               t.day28,
               t.day29,
               t.day30,
               t.day31,
               t.tjdate,
               t.entrytime,
               c.corpname,
               t.idcardnumber,
               t.kqhours,
               t.address,
               t.cellphone,
               t.urgentlinkmanphone,
               t.payrollbankname,
               t.payrollbankcardnumber
        from b_tj_attendance_info t
                 left join b_cp01 c
                           on t.cp0101 = c.cp0101
                 left join b_tm01 m
                           on t.tm0101 = m.tm0101
        where t.pj0101 = #{pj0101}
          and t.kqmonth = #{tjMonth}
        <if test="tm0101 != null and tm0101 != ''" >
            and t.tm0101 = #{tm0101}
        </if>
        <if test="personType != null and personType != ''" >
            and t.person_type = #{personType}
        </if>
        <if test="inOrOut != null and inOrOut != ''" >
            and t.in_or_out = #{inOrOut}
        </if>
        order by t.tm0101, t.in_or_out asc, t.id desc
    </select>

    <select id="generateTjAttendanceData" statementType="CALLABLE">
        {call PC_TJ_ATTENDANCE_INFO(#{Cpj0101,mode=IN,jdbcType=VARCHAR},
                                    #{Ckqmonth,mode=IN,jdbcType=VARCHAR})}
    </select>

    <select id="getKqdaysByIdcardnumber" resultType="java.lang.Integer">
        select kqdays
        from b_tj_attendance
        where kqmonth = #{tjMonth}
          and pj0101 = #{pj0101}
          and idcardnumber = #{idcardnumber}
    </select>

    <delete id="deleteTjAttendanceData">
        delete b_tj_attendance
        where kqmonth = #{month}
        and pj0101 = #{pj0101}
    </delete>
</mapper>