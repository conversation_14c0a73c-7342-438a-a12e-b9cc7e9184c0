<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps09.dao.Ps09Dao">


    <select id="isUploadFile" resultType="io.renren.modules.enterprise.ps09.entity.Ps09Entity">
        select count(*) from b_ps09 t where t.pj0101 = #{pj0101} and to_char(t.yearmonth, 'yyyyMM') = #{yearMonth}
    </select>
</mapper>