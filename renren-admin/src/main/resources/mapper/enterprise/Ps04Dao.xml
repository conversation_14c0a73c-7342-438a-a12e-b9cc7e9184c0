<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps04.dao.Ps04Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.ps04.dto.Ps04PageDTO">
        SELECT a.ps0101,
        p.ps0401,
        a.name,
        a.idcardnumber,
        a.cellphone,
        j.name         projectName,
        p.cp0201,
        a.<PERSON>,
        p.JOBTYPE      jobtype,
        p.pj0101,
        p.ENTRYTIME,
        p.EXITTIME,
        p.IN_OR_OUT,
        d.corpname,
        b.corptype,
        p.isconfirm,
        p.confirmresult,
        (
        SELECT CASE
        WHEN COUNT(*) > 0 THEN
        1
        ELSE
        0
        END
        FROM b_ot01 c
        WHERE c.busisysno = p.ps0401
        and c.busitype = '54'
        and c.whether = '1'
        ) as managerFileIsUpload
        FROM b_ps01      a,
        b_ps03      t,
        b_ps04      p,
        B_PJ01      j,
        R_PJ01_DEPT c,
        b_cp02      b,
        b_cp01      d
        where t.ps0301 = p.ps0301
        and t.ps0301 = p.ps0301
        and a.ps0101 = t.ps0101
        and j.pj0101 = p.pj0101
        and j.pj0101 = c.pj0101
        and p.cp0201 = b.cp0201
        and b.cp0101 = d.cp0101
        and c.dept_id = #{deptId}
        <if test="name != null and name != ''">
            and a.NAME like #{name}
        </if>
        <if test="jobtype != null and jobtype != ''">
            and p.jobtype = #{jobtype}
        </if>
        <if test="cpname != null and cpname != ''">
            and d.corpname like #{cpname}
        </if>
        <if test="inOrOut != null and inOrOut != ''">
            and p.IN_OR_OUT = #{inOrOut}
        </if>
    </select>

    <update id="updateInOrOutByIds">
        update B_PS04 t
        set t.IN_OR_OUT = #{type},
        <if test="type == 1">
            t.entrytime  = sysdate,
            t.exittime = null
        </if>
        <if test="type == 2">
            t.exittime  = sysdate
        </if>
         where t.PS0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>
    <update id="exitPs03ByIds">
        update B_PS03 t
           set t.pj0101 = null
         where t.ps0301 in (select a.ps0301 from b_ps04 a where a.ps0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>)
    </update>
    <select id="selectPersonByIds" resultType="io.renren.modules.supdevicetask.dto.PersonDTO">
        select a.NAME, b.PS0401 userId, b.PHOTO imageUrl
        from b_ps01 a,b_ps04 b,b_ps03 c where a.ps0101=c.ps0101 and c.ps0301=b.ps0301
        and b.PS0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="getKeyJobData" resultType="io.renren.modules.enterprise.ps04.dto.KeyJobDTO">
        select *
          from (select *
                  from (select b.pj0101   pj01,
                               b.name     projectname,
                               d.corptype,
                               d.jobtype
                          from (select t.pj0101, t.name, '1' type
                                  from b_pj01 t, r_pj01_dept a
                                 where t.pj0101 = a.pj0101
                                   and a.dept_id = #{deptId}) b,
                               (select c.corptype, c.jobtype, c.create_date, '1' type
                                  from KEYJOB_CONFIG c) d
                         where b.type = d.type
                         <if test="jobtype != null and jobtype != ''" >
                             and d.jobtype = #{jobtype}
                        </if>
                         ) t
                  left join (select s.NAME,
                                   s.idcardnumber,
                                   p.CORPNAME COMPANYNAME,
                                   c.CORPTYPE,
                                   b.pj0101,
                                   b.jobtype,
                                   b.in_or_out,
                                   TO_CHAR(b.entrytime, 'yyyy-MM-dd') entertime
                              from B_PS04 b, B_PS01 s, B_CP02 c, B_CP01 p
                             WHERE s.ps0101 = b.ps0101
                               and b.pj0101 = c.pj0101
                               AND b.cp0201 = c.cp0201
                               AND c.cp0101 = p.cp0101
                            ) b
                    on t.pj01 = b.pj0101
                   and t.jobtype = b.jobtype
                   and t.corptype = b.corptype) z
                   where 1 = 1
                   <if test="name != null and name != ''" >
                        and z.name like '%'|| #{name} ||'%'
                   </if>
         order by z.pj01 desc

</select>
<select id="getKeyStation" resultType="java.util.Map">
    SELECT
        d.dict_label label,
        t.JOBTYPE value
    FROM
        KEYJOB_CONFIG t,
        SYS_DICT_TYPE s,
        SYS_DICT_DATA d
        WHERE
        s.ID = d.DICT_TYPE_ID
        AND
        t.JOBTYPE = d.dict_value
        AND
        s.DICT_TYPE = 'JOBTYPE'
</select>

    <select id="selectTypeById" resultType="java.lang.String">

        select a.CORPTYPE
        from B_CP02 a
        where a.CP0201 = #{cp0201}

    </select>

    <select id="selectByCount" resultType="java.lang.Integer">

        select count(0)
        from B_PS04 t
        where t.PJ0101 = #{pj0101}
          and t.CP0201 in (select a.CP0201 from B_CP02 a where a.CORPTYPE = #{partType})
          and t.JOBTYPE = #{jobType}

    </select>
    <select id="isInProject" resultType="java.lang.Long">
        select count(1)
          from B_PS04 t, B_ps03 a, b_ps01 b
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.pj0101 = #{pj0101}
           and b.ps0101 = #{ps0101}
           and t.in_or_out = '1'
    </select>
    <select id="isInOtherProject" resultType="java.lang.Long">
        select count(1)
          from B_PS04 t, B_ps03 a, b_ps01 b
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.pj0101 != #{pj0101}
           and a.cp0101 != #{cp0101}
           and b.ps0101 = #{ps0101}
           and t.in_or_out = '1'
    </select>
    <select id="selectSeqAttInfo" resultType="java.lang.Long">
        select zjnmg.seq_att_info.nextval from dual
    </select>
    <insert id="insertAttInfo">
        insert into zjnmg.att_info
              (att_id, att_name, att_type, area_code, busiid, file_path)
            values
              (#{ATT_ID},
               #{ATT_NAME},
               'jpg',
               '5115',
               #{ATT_ID},
               #{FILE_PATH})
    </insert>
    <insert id="insertProjectPmInfo">
        insert into zjnmg.project_pm_info
              (pj0101,
               corp_name,
               corp_code,
               corp_type,
               p_type,
               pm_name,
               pm_id_card_type,
               pm_id_card_number,
               pm_phone,
               id,
               busiid,
               grant_org,
               nation,
               politics_type,
               culture_level_type,
               address,
               head_image)
            values
              (#{PJ0101},
               #{CORP_NAME},
               #{CORP_CODE},
               #{CORP_TYPE},
               #{P_TYPE},
               #{PM_NAME},
               '1',
               #{PM_ID_CARD_NUMBER},
               #{PM_PHONE},
               zjnmg.seq_project_pm_info.nextval,
               #{BUSIID},
               substr(#{GRANT_ORG}, 0, 10),
               #{NATION},
               #{POLITICS_TYPE},
               #{CULTURE_LEVEL_TYPE},
               #{ADDRESS},
               #{HEAD_IMAGE})
    </insert>
    <insert id="insertProjectWorkerInoutInfo">
        insert into zjnmg.project_worker_inout_info
              (pj0101, id_card_number, in_out, occur_time, id)
            values
              (#{PJ0101},
               #{ID_CARD_NUMBER},
               '1',
               #{OCCUR_TIME},
               zjnmg.seq_project_worker_inout_info.nextval)
    </insert>

    <select id="getManagerSupplement" resultType="java.lang.Long">
        select count(distinct t.jobtype) as managercount
        from b_ps04 t, b_ps13 t1, b_cp02 t2
        where t.in_or_out = '1'
          and t.cp0201 = t2.cp0201
          and t.jobtype = t1.jobtype
          and t1.corptype = t2.corptype
          and t.pj0101 = #{pj0101}

    </select>
    <select id="selectInfoById" resultType="io.renren.modules.enterprise.ps04.dto.Ps04PageDTO">
        select t.ps0401,
               t.photo,
               b.name,
               b.gender,
               fun_get_age(b.birthday, sysdate) as age,
               b.nation,
               b.idcardnumber,
               b.cellphone,
               c.name as project_name,
               d.corpname,
               t.jobtype,
               t.entrytime,
               t.exittime,
               (select x.corptype from b_cp02 x where x.cp0201=t.cp0201) as corptype
          from B_PS04 t, b_ps03 a, b_ps01 b, b_pj01 c, b_cp01 d
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.pj0101 = c.pj0101
           and a.cp0101 = d.cp0101
           and t.ps0401 = #{ps0401}
    </select>
    <select id="selectAttendanceTJ" resultType="io.renren.modules.enterprise.ps04.dto.Attendance44StatisticsDTO">
        select h.*,
               to_char(round(decode(days, 0, 0, (nvl(kqs, 0) / days) * 100), 2),'fm9999999990.00') || '%' as arrivepercent
          from (select (select count(1)
                          from b_tj04 x
                         where x.pj0101 = t.pj0101
                           and x.userid = t.ps0401
                           and x.person_type = '2'
                           and x.kqstatus = '1'
                           and to_char(x.kqday, 'yyyy-MM') &gt;= #{startdate}
                           and to_char(x.kqday, 'yyyy-MM') &lt;= #{enddate}) as kqs,
                       case
                         when to_char(sysdate, 'yyyy-MM') = #{enddate} then
                          trunc(sysdate - to_date(#{startdate}, 'yyyy-MM')) + 1
                         else
                          LAST_DAY(to_date(#{enddate}, 'yyyy-MM')) -
                          to_date(#{startdate}, 'yyyy-MM') + 1
                       end as days
                  from B_PS04 t
                 where t.ps0401 = #{ps0401}) h
         order by h.kqs desc
    </select>
    <select id="getAttendanceMonthList" resultType="io.renren.modules.enterprise.ps04.dto.AttendanceMonthDTO">
        select g.*,
               g.kqs || '|' ||
               to_char(round(decode(g.days, 0, 0, (nvl(g.kqs, 0) / g.days) * 100), 2),'fm9999999990.00') || '%' as arrivepercent
          from (select t.month,
                    case
                         when t.month = to_char(sysdate, 'yyyy-MM') then
                          to_char(sysdate, 'dd')
                         else
                          t.days
                    end as days,
                    nvl(h.kqs, 0) as kqs
                  from sys_months t
                  left join (select to_char(x.kqday, 'yyyy-MM') as kqmonth,
                                   count(1) as kqs
                              from b_tj04 x
                             where x.userid = #{ps0401}
                               and x.person_type = '2'
                               and x.kqstatus = '1'
                               and to_char(x.kqday, 'yyyy-MM') &gt;= #{startdate}
                               and to_char(x.kqday, 'yyyy-MM') &lt;= #{enddate}
                             group by to_char(x.kqday, 'yyyy-MM')) h
                    on t.month = h.kqmonth
                 where t.month &lt;= #{enddate}
                   and t.month &gt;= #{startdate}
                 order by t.month) g
    </select>
</mapper>