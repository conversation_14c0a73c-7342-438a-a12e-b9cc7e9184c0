<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps08.dao.BPs08Dao">
    <select id="selectContractA" resultType="io.renren.modules.enterprise.ps08.entity.BPs08Entity">
        select t.corpname,
               t.legalman,
               t.corpcode,
               t.address as corpaddress,
               t.linkcellphone
          from b_cp01 t, b_tm01 a, b_cp02 b
         where t.cp0101 = b.cp0101
           and a.cp0201 = b.cp0201
           and a.tm0101 = #{tm0101}
    </select>
    <select id="selectContractB" resultType="io.renren.modules.enterprise.ps08.entity.BPs08Entity">
        select a.ps0201,
               a.pj0101,
               t.name,
               (select y.dict_label
                  from sys_dict_type x, sys_dict_data y
                 where x.dict_type = 'GENDER'
                   and x.id = y.dict_type_id
                   and y.dict_value = t.gender) as gender,
               t.birthday,
               t.idcardnumber,
               t.address,
               t.cellphone,
               to_char(a.entrytime,'yyyy-MM-dd') as contractbegin,
               b.name as projectname,
               (select y.dict_label
                  from sys_dict_type x, sys_dict_data y
                 where x.dict_type = 'WORKTYPECODE'
                   and x.id = y.dict_type_id
                   and y.dict_value = a.worktypecode) as worktypecode
          from b_ps01 t, b_ps02 a, b_pj01 b
         where t.ps0101 = a.ps0101
           and a.pj0101 = b.pj0101
           and a.ps0201 = #{ps0201}
    </select>
    <select id="getWorkerContract" resultType="io.renren.modules.enterprise.ps08.dto.BPs08DTO">
        select t.contractno,
               t.corpname,
               t.contractbegin,
               t.worktypecode,
               t.duty,
               t.payday
          from B_PS08 t
         where t.ps0201 = #{ps0201}
    </select>

    <select id="getPersonSign" resultType="java.lang.Integer">

        select count(*) from b_ps08 t where t.ps0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>

    </select>
</mapper>