<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa02.dao.Pa02Dao">
    <select id="page" resultType="io.renren.modules.enterprise.pa02.dto.Pa02DTO">
        select a.name, t.*
        from B_PA02 t, b_pj01 a
        where t.pj0101 = a.pj0101
        and t.pj0101 = #{pj0101}
        <if test="comaccount != null and comaccount.trim() !=''">
            and a.comaccount like '%'||#{comaccount}||'%'
        </if>
        <if test="comaccountname != null and comaccountname.trim() !=''">
            and a.comaccountname like '%'||#{comaccountname}||'%'
        </if>
        <if test="issuedate != null and issuedate.trim() !=''">
            and to_char(issuedate,'yyyy-mm') = #{issuedate}
        </if>
        order by t.accountdate desc, t.rowid
    </select>

    <select id="getPageList" resultType="io.renren.modules.enterprise.pa02.dto.Pa02DTO">
        select * from b_pa02 t where t.pj0101 = #{pj0101}
        <if test="accounttype != null and accounttype != ''" >
            and t.accounttype = #{accounttype}
        </if>
        <if test="accountdate != null and accountdate != ''" >
            and to_char(t.accountdate, 'yyyyMM') = #{accountdate}
        </if>
    </select>

    <select id="getPa03DetailList" resultType="io.renren.modules.enterprise.pa03.dto.Pa03DTO">

        select * from b_pa03 t where t.pa0201 = #{pa0201}

    </select>
</mapper>