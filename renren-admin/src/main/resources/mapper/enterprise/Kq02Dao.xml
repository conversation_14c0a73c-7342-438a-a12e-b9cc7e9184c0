<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq02.dao.Kq02Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.kq02.dto.Kq02PageDTO">
        select a.KQ0201,
               c.NAME             projectName,
               a.USER_ID          userId,
               a.PJ0101,
               a.PERSON_NAME      personName,
               a.PERSON_TYPE      personType,
               a.DIRECTION,
               a.ATTENDTYPE,
               a.CHECKDATE        checkDate,
               a.IMAGE_URL        imageUrl,
        (select x.teamname from b_tm01 x where x.tm0101=a.tm0101 ) as teamname
        from B_KQ02 a,
             R_PJ01_DEPT b,
             b_pj01 c
                where a.PJ0101 = b.PJ0101
                  and a.PJ0101 = c.PJ0101
                  and a.person_type is not null
                  and b.DEPT_ID = #{deptId}
        <if test="personName != null and personName != ''">
            and a.PERSON_NAME = #{personName}
        </if>
        <if test="startTime != null and startTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
        <if test="personType != null and personType != ''">
            and a.PERSON_TYPE = #{personType}
        </if>
        <if test="attendtype != null and attendtype != ''">
            and a.attendtype = #{attendtype}
        </if>
        <if test="team0101 != null and team0101 != ''">
            and a.tm0101 = #{team0101}
        </if>
        <if test="projectName != null and projectName != ''">
            and c.NAME like '%' || #{projectName} || '%'
        </if>
    </select>
    <select id="selectByCheckDate" resultType="java.lang.Integer">
        select count(1)
        from b_kq02
        where user_id = #{userId}
        <if test="checkdatestring != null and checkdatestring != ''">
            and to_char(checkdate, 'yyyy-mm-dd') = #{checkdatestring}
        </if>
        and pj0101 = #{pj0101}
        and direction=#{direction}
    </select>
    <select id="generate" statementType="CALLABLE">
        {call PC_TJ_ATTENDANCE(#{Cpj0101,mode=IN,jdbcType=VARCHAR},
                            #{Ckqmonth,mode=IN,jdbcType=VARCHAR})}
    </select>
    <select id="exportPage" resultType="io.renren.modules.enterprise.kq02.dto.TjAttendanceDTO">
        select rownum as sno, h.*
          from (select t.tjdate,
                       (select x.corpname from b_cp01 x where x.cp0101 = t.cp0101) as corpname,
                       case
                         when t.tm0101 = -100 then
                          '管理班组'
                         when t.tm0101 != -100 then
                          (select x.teamname from b_tm01 x where x.tm0101 = t.tm0101)
                       end as teamname,
                       t.tm0101,
                       (select x.dict_label
                          from sys_dict_data x, sys_dict_type y
                         where x.dict_type_id = y.id
                           and y.dict_type = 'PERSON_TYPE'
                           and x.dict_value = t.Person_Type) as person_type,
                       (select x.dict_label
                          from sys_dict_data x, sys_dict_type y
                         where x.dict_type_id = y.id
                           and y.dict_type = 'IN_OR_OUT'
                           and x.dict_value = t.in_or_out) as in_or_out,
                       t.workername,
                       t.jobtype,
                       t.entrytime,
                       t.exittime,
                       t.user_id,
                       t.idcardnumber,
                       t.address,
                       t.cellphone,
                       t.urgentlinkmanphone,
                       t.payrollbankname,
                       t.payrollbankcardnumber,
                       t.payrollno,
                       t.kqmonth,
                       t.kqdays,
                       t.day1,
                       t.day2,
                       t.day3,
                       t.day4,
                       t.day5,
                       t.day6,
                       t.day7,
                       t.day8,
                       t.day9,
                       t.day10,
                       t.day11,
                       t.day12,
                       t.day13,
                       t.day14,
                       t.day15,
                       t.day16,
                       t.day17,
                       t.day18,
                       t.day19,
                       t.day20,
                       t.day21,
                       t.day22,
                       t.day23,
                       t.day24,
                       t.day25,
                       t.day26,
                       t.day27,
                       t.day28,
                       t.day29,
                       t.day30,
                       t.day31
                  from B_TJ_ATTENDANCE t
                 where t.pj0101 = #{pj0101}
                order by t.entrytime desc,t.user_id desc, t.tm0101) h
         where 1 = 1
        <if test="tm0101 != null and tm0101 != ''">
            and h.tm0101 = #{tm0101}
        </if>
        <if test="kqmonth != null and kqmonth != ''">
            and h.kqmonth = #{kqmonth}
        </if>
    </select>

    <select id="getNoAbsenceDays" resultType="io.renren.modules.enterprise.kq02.dto.Kq02NoAttendanceStatisticalDTO">
        select t.*
        from ( select t.*,
                      k.fday as recentAttendance,
                      round(to_number(sysdate - k.fday)) absencedays
               from (select p.name,
                            m.teamname,
                            p.idcardnumber,
                            s.ps0201 as userId,
                            s.worktypecode,
                            s.in_or_out,
                            s.entrytime,
                            s.exittime
                     from b_ps01 p, b_ps02 s, b_tm01 m
                     where p.ps0101 = s.ps0101
                       and s.tm0101 = m.tm0101
                       and s.in_or_out = '1'
                       and s.pj0101 = #{params.pj0101}
                       <if test="params.name != null and params.name != ''" >
                           and p.name  like '%' || #{params.name } || '%'
                       </if>
                        <if test="params.teamname != null and params.teamname != ''" >
                            and m.teamname like '%' || #{params.teamname } || '%'
                        </if>
                       ) t
                        left join (select t.user_id, max(t.checkdate) fday
                                   from b_kq02 t
                                   where t.person_type = '1'
                                     and t.pj0101 = #{params.pj0101}
                                   group by t.user_id) k
                                  on t.userId = k.user_id
               order by absencedays desc
             ) t
<!--        where ( t.exittime &lt;= sysdate or t.absencedays &gt;= #{params.dayNum})-->
        where ( t.absencedays &gt;= #{params.dayNum})
    </select>

    <select id="getFailedIssued" resultType="io.renren.modules.enterprise.kq02.dto.Kq02GetFailedIssuedDTO">
        select m.*
        from (select h.*, j.result, j.msg, j.id,j.create_date
        from (SELECT a.ps0201 AS user_id,
        '1' AS user_type,
        a.issuecardpicurl as image_url,
        b.name
        FROM b_ps02 a, b_ps01 b
        WHERE a.pj0101 = #{params.pj0101}
        and a.ps0101 = b.ps0101
        AND a.in_or_out = '1'
        UNION ALL
        SELECT c.ps0401 AS user_id,
        '2' AS user_type,
        c.photo as image_url,
        e.name
        FROM b_ps04 c, b_ps03 d, b_ps01 e
        WHERE c.ps0301 = d.ps0301
        and d.ps0101 = e.ps0101
        AND c.pj0101 = #{params.pj0101}
        AND c.in_or_out = '1') h,
        (select q.*,w.create_date, w.result, w.msg
        from (select max(t.id) as id, a.user_id
        from SUP_DEVICE_TASK t, sup_device_task_detail a
        where t.id = a.task_id
        and t.type = '2'
        and t.device_key = #{params.sn}
        group by a.user_id) q,
        sup_device_task w
        where q.id = w.id) j
        where h.user_id = j.user_id) m
        where 1 = 1
        <if test="params.result !=null and params.result !=''">
            and m.result =  #{params.result}
        </if>
        <if test="params.name !=null and params.name !=''">
            and m.name like  '%'||#{params.name}||'%'
        </if>
        order by m.id desc
    </select>

    <select id="getFailedIssuedDetail" resultType="io.renren.modules.enterprise.kq02.dto.Kq02GetFailedIssuedDetailDTO">

        select t.device_key as sn, t.msg as reason
        from (select d.sn
              from SUP_DEVICE d
              where d.pj0101 = #{pj0101}) d,
             (select t.*
              from SUP_DEVICE_TASK t
              where t.id = (select max(t.id)
                            from SUP_DEVICE_TASK t, sup_device_task_detail a
                            where t.id = a.task_id
                              and t.TYPE = 2
                              and t.RESULT = 1
                              and a.user_id = #{userId})) t
        where d.sn = t.device_key
    </select>
    <select id="getDeviceList" resultType="io.renren.common.common.dto.CommonDto">
        select t.sn as label, t.sn as value
          from SUP_DEVICE t
         where t.pj0101 = #{pj0101}
    </select>
</mapper>