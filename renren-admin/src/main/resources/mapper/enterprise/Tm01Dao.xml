<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.tm01.dao.Tm01Dao">
    <select id="getList" resultType="io.renren.modules.enterprise.tm01.dto.Tm01PageDTO">
        select a.name                      projectName,
               t.responsiblepersonname,
               t.tm0101,
               t.cp0201,
               t.pj0101,
               t.teamname,
               t.entrytime,
               t.exittime,
               t.in_or_out,
            (select case
            when count(1) = 0 then
            0
            else
            1
            end
            from b_ot01 y
            where y.busisysno = t.tm0101
            and y.busitype = '07'
            and y.whether = 1) teamPromiseUpload
        from b_tm01 t,
             R_PJ01_DEPT r,
             b_pj01 a
        where t.pj0101 = r.pj0101
          and t.pj0101 = a.pj0101
          and r.DEPT_ID = #{deptId}
        <if test="teamname != null and teamname != ''">
            and t.TEAMNAME like '%' || #{teamname} || '%'
        </if>
        <if test="name != null and name != ''">
            and a.name like '%' || #{name} || '%'
        </if>
        <if test="inOrOut != null and inOrOut != ''">
            and t.IN_OR_OUT = #{inOrOut}
        </if>
        order by t.create_date desc, t.rowid
    </select>

    <select id="queryByName" resultType="java.lang.Integer">
        select count(0)
        from B_TM01 t
        where t.teamname = #{teamName}
          and t.pj0101 = #{pj0101}
    </select>

    <select id="loadTm01Info" resultType="io.renren.common.common.dto.CommonDto">
        select a.TM0101 value, a.TEAMNAME label
        from B_TM01 a,
             R_PJ01_DEPT t
        where a.PJ0101 = t.PJ0101
          and t.DEPT_ID = #{deptId}
    </select>

    <select id="loadTm01InfoAndPassed" resultType="io.renren.common.common.dto.CommonDto">
        select a.TM0101 value, a.TEAMNAME label
        from B_TM01 a,
            R_PJ01_DEPT t
        where a.PJ0101 = t.PJ0101
          and a.IN_OR_OUT = '1'
          and t.DEPT_ID = #{deptId}
    </select>

    <select id="selectPersonByTeamIds" resultType="io.renren.modules.supdevicetask.dto.PersonDTO">

        select a.NAME, b.PS0201 userId, b.ISSUECARDPICURL imageUrl
        from b_ps01 a, b_ps02 b, b_tm01 t
        where b.PS0101 = a.PS0101
          and b.tm0101 = t.tm0101
          and t.tm0101 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>

    </select>

    <update id="teamInByIds">
        update B_TM01 t
        set t.IN_OR_OUT = '1',
        t.ENTRYTIME  = to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd') where t.tm0101 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>

    </update>

    <update id="teamOutByIds">
        update B_TM01 t
        set t.IN_OR_OUT = '2',
        t.EXITTIME  = to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd') where t.tm0101 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>
    <select id="createTeamsysno" resultType="java.lang.String">
        select seq_team_sysno.nextval from dual
    </select>
</mapper>