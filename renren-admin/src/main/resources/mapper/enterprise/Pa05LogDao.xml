<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa05log.dao.Pa05LogDao">

    <resultMap type="io.renren.modules.enterprise.pa05log.entity.Pa05LogEntity" id="pa05LogMap">
        <result property="id" column="ID"/>
        <result property="pa0501" column="PA0501"/>
        <result property="operation" column="OPERATION"/>
        <result property="operator" column="OPERATOR"/>
        <result property="operatdate" column="OPERATDATE"/>
        <result property="options" column="OPTIONS"/>
    </resultMap>

    <select id="record" resultType="io.renren.modules.enterprise.pa05log.dto.Pa05TimeLineDTO">
        SELECT t.operation, t.operator, t.operatdate
        FROM b_pa05_log t
        where t.pa0501 = #{pa0501}
        order by t.operatdate asc
    </select>
</mapper>