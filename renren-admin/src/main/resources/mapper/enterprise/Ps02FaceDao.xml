<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps02face.dao.Ps02FaceDao">

    <select id="getUnCollectedPj01" resultType="io.renren.modules.enterprise.pj01facetemp.entity.Pj01FaceTempEntity">

        select name, pj0101, state, areacode from b_pj01_face_temp where state = '0'

    </select>

    <select id="getUnCollectedPs02" resultType="io.renren.modules.enterprise.ps02facedata.entity.Ps02FaceDataEntity">

        select id, ps0201, face_feature, create_time, update_time, pj0101, areacode, in_or_out, issuecardpicurl, iscollect from b_ps02_face_data where pj0101 = #{pj0101} and iscollect ='0'

    </select>

    <update id="updatePj01FaceTempComplate">

        update b_pj01_face_temp t set t.state = '1' where t.pj0101 = #{pj0101}
    </update>

    <update id="updatePs02FaceTempComplate">
        update b_ps02_face_data t set t.iscollect = '1' where t.ps0201 = #{ps0201}
    </update>
</mapper>