<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cp04audit.dao.Cp04AuditDao">

    <resultMap type="io.renren.modules.enterprise.cp04audit.entity.Cp04AuditEntity" id="cp04AuditMap">
        <result property="cp0401" column="CP0401"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="subcontractname" column="SUBCONTRACTNAME"/>
        <result property="subcontractcontent" column="SUBCONTRACTCONTENT"/>
        <result property="startdate" column="STARTDATE"/>
        <result property="completedate" column="COMPLETEDATE"/>
        <result property="subcontractprice" column="SUBCONTRACTPRICE"/>
        <result property="memo" column="MEMO"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditor" column="AUDITOR"/>
        <result property="auditdate" column="AUDITDATE"/>
        <result property="auditresult" column="AUDITRESULT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="pageList" resultType="io.renren.modules.enterprise.cp04audit.dto.Cp04AuditPageDTO">

        SELECT b.cp0401,
        j.name pjname,
        c.cp0201,
        p.corpname cpname,
        b.subcontractname,
        b.subcontractcontent,
        b.startdate,
        b.completedate,
        b.subcontractprice,
        b.memo,
        b.auditstatus
        FROM b_cp04_audit b, b_pj01 j, b_cp02 c, b_cp01 p, r_pj01_dept d
        where p.cp0101 = c.cp0101
        and b.cp0201 = c.cp0201
        and c.pj0101 = j.pj0101
        and b.pj0101 = j.pj0101
        and d.pj0101 = j.pj0101
        and d.dept_id = #{deptId}
        <if test="cpname != null and cpname.trim() != ''" >
            and p.corpname like '%'||#{cpname}||'%'
        </if>

    </select>
</mapper>