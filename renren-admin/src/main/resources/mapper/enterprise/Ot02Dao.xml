<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ot02.dao.Ot02Dao">
    <select id="selectPageList" resultType="io.renren.modules.enterprise.ot02.dto.Ot02PageDTO">
        select d.pj0101,
               d.name,
               e.ot0301,
               e.fileName,
               e.updateType,
               e.fileType,
               (select decode(count(1),0,'0','1')
             from b_ot02 f
            where f.ot0301 = e.ot0301
              and f.pj0101 = d.pj0101
              and to_char(f.years,'yyyy-mm') = to_char(add_months(sysdate,-1),'yyyy-mm')) isUpload
        from (select a.pj0101, a.name, '1' id
              from b_pj01 a,
                   r_pj01_dept b
              where a.pj0101 = b.pj0101
                and b.dept_id = #{deptId}) d
                     left join (select b.ot0301,
                                       b.file_name   fileName,
                                       b.update_type updateType,
                                       b.file_type   fileType,
                                       '1'           id
                                from b_ot03 b
                                where b.whether = '1'
                                order by b.file_sort) e
                on d.id = e.id
    </select>
<!--    <select id="selectFileData" resultType="io.renren.modules.ot01.dto.Ot01DTO">-->
<!--        select t.BUSITYPE, t.BUSISYSNO, t.OT0101, t.NAME, t.URL, t.VIEW_TYPE, t.ORIGINAL_NAME-->
<!--        from B_OT01 t-->
<!--                where t.BUSISYSNO in-->
<!--                (select a.OT0201-->
<!--                 from b_ot02 a where a.OT0301 = #{ot0301}-->
<!--                                 and a.PJ0101 = #{pj0101}-->
<!--        <if test="updateType == '1'.toString()">-->
<!--            and to_char(a.YEARS, 'yyyy-MM') = #{years}-->
<!--        </if>-->
<!--        )-->
<!--                and t.BUSITYPE = '02'-->
<!--                and t.WHETHER = '1'-->
<!--    </select>-->
    <select id="selectFileData" resultType="io.renren.modules.ot01.dto.Ot01DTO">
        select * from b_ot01 t where t.busitype = '03' and t.busisysno = #{ot0201}
    </select>

    <select id="getOt02ListByPj0101" resultType="io.renren.modules.enterprise.ot02.dto.Ot02PageDTO">
        select t.ot0201,
        o.ot0301,
        t.pj0101,
        t.years,
        (select name from b_pj01 j where j.pj0101 = t.pj0101) name,
        o.file_name fileName,
        (case when (select count(*) from b_ot01 o where o.busisysno = t.ot0201 and o.busitype = '03') > 0 then '1' else '0' end) isUpload
        from b_ot02 t, b_ot03 o
        where t.ot0301 = o.ot0301
        and t.pj0101 = #{pj0101}
        <if test="yearMonth != null and yearMonth != ''">
            and to_char(t.years, 'yyyyMM') = #{yearMonth}
        </if>
    </select>

    <select id="getYearMonthOt02Count" resultType="int">

        select count(*) from b_ot02 t where t.ot0301 = '302' and t.pj0101 = #{pj0101} and to_char(t.years, 'yyyyMM') = #{yearMonth}

    </select>

    <select id="getOt02YearMonthAndPj0101" resultType="java.lang.Long">

        select t.ot0201 from b_ot02 t where t.pj0101 = #{pj0101} and to_char(t.years, 'yyyyMM') = #{yearMonth} and t.ot0301 = '302'

    </select>
</mapper>