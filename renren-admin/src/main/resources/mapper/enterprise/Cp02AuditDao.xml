<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cp02audit.dao.Cp02AuditDao">

    <resultMap type="io.renren.modules.enterprise.cp02audit.entity.Cp02AuditEntity" id="cp02AuditMap">
        <result property="cp0201" column="CP0201"/>
        <result property="cp0101" column="CP0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="corptype" column="CORPTYPE"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditor" column="AUDITOR"/>
        <result property="auditdate" column="AUDITDATE"/>
        <result property="auditresult" column="AUDITRESULT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPageList" resultType="io.renren.modules.enterprise.cp02audit.dto.Cp02AuditPageDTO">
        select t.cp0201,
        (select c.NAME from b_pj01 c where c.PJ0101 = t.PJ0101) name,
        b.corpname,
        b.corpcode,
        b.linkman,
        b.linkcellphone,
        t.corptype,
        t.auditstatus
        from b_cp02_audit t,
        r_pj01_dept a,
        b_cp01 b
        where t.pj0101 = a.pj0101
        and t.cp0101 = b.cp0101
        and a.dept_id = #{deptId}
        <if test="corpname != null and corpname != ''">
            and b.CORPNAME like '%'||#{corpname}||'%'
        </if>
        <if test="auditstatus != null and auditstatus != ''">
            and t.auditstatus = #{auditstatus}
        </if>
        order by t.auditstatus,t.create_date desc
    </select>

    <select id="getInfo" resultType="io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDetailDTO">

        select t.cp0201,
               (select c.NAME from b_pj01 c where c.PJ0101 = t.PJ0101) name,
               b.corpname,
               b.corpcode,
               b.linkman,
               b.linkcellphone,
               t.corptype,
               t.auditstatus,
               t.auditor,
               t.auditdate,
               t.auditresult
        from b_cp02_audit t, b_cp01 b
        where t.cp0101 = b.cp0101
          and t.cp0201 = #{cp0201}
    </select>
</mapper>