<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cg10.dao.Cg10Dao">
    <resultMap type="io.renren.modules.enterprise.cg10.entity.Cg10Entity" id="cg10Map">
        <result property="cg1001" column="CG1001"/>
        <result property="cg0901" column="CG0901"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="fileYear" column="FILE_YEAR"/>
        <result property="state" column="STATE"/>
        <result property="remark" column="REMARK"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="reviewers" column="REVIEWERS"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="selectPageList" resultType="io.renren.modules.enterprise.cg10.dto.Cg10Page">
        select b.ot0101,
               b.name,
               b.url,
               b.view_type     as viewType,
               b.create_date   as createDate,
               a.state,
               a.FILE_YEAR     as fileYear,
               b.ORIGINAL_NAME as originalName
        from b_cg10 a
                 inner join (select t.cg0901
                             from b_cg09 t
                             start with t.cg0901 = nvl(#{cg0901}, 1)
                             connect by prior t.cg0901 = t.pid) b on a.cg0901 = b.cg0901
                 inner join b_ot01 b
                            on a.cg1001 = b.busisysno
                                and b.whether = '1'
                                and b.busitype = #{fileType}
        <where>
            <if test="pj0101 != null and pj0101 != ''">
                a.PJ0101 = #{pj0101}
            </if>
            <if test="year != null and year != ''">
                and to_char(to_date(a.file_year, 'yyyyMM'), 'yyyy') = #{year}
            </if>
            <if test="month != null and month != ''">
                and to_char(to_date(a.file_year, 'yyyyMM'), 'yyyyMM') = #{month}
            </if>
        </where>
        order by a.STATE, a.UPDATE_DATE desc
    </select>
    <select id="selectArchInfo" resultType="io.renren.modules.enterprise.cg10.dto.Cg10DTO">
        select a.cg1001,
               a.state
        from b_cg10 a
                 inner join b_cg09 b on a.cg0901 = b.cg0901
        where a.pj0101 = #{pj0101}
          and a.cg0901 = #{cg0901}
        <if test="fileYear != '' and fileYear != null">
            and to_char(to_date(a.file_year, 'yyyyMM'), 'yyyyMM') = #{fileYear}
        </if>
        <!--以防出现预期之外的数据-->
        and rownum = 1
    </select>
</mapper>