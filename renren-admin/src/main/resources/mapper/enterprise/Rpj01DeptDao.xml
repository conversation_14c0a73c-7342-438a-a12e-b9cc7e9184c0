<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.rpj01dept.dao.RPj01DeptDao">

    <resultMap type="io.renren.modules.enterprise.rpj01dept.entity.RPj01DeptEntity" id="rPj01DeptMap">
        <result property="id" column="ID"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="deptId" column="DEPT_ID"/>
    </resultMap>
    <delete id="deleteByInsertRpdDTO">
        delete from r_pj01_dept t where t.dept_id = #{deptId} and t.pj0101 not in
        <foreach collection="pj0101s" item="pj0101" index="index"
                 open="(" close=")" separator=",">
            #{pj0101}
        </foreach>
    </delete>

    <select id="getPj0101List" resultType="io.renren.modules.enterprise.rpj01dept.dto.Pj0101NameDTO">
        select  t.pj0101 ,t.name  from  b_pj01 t
    </select>

    <select id="getCountByRPj01DeptDTO" resultType="int">
        SELECT COUNT(*) FROM  r_pj01_dept t where t.dept_id = #{deptId} and t.pj0101 =  #{pj0101}
    </select>

    <select id="getOwnPj0101List" resultType="java.lang.String">
        SELECT t.pj0101 FROM r_pj01_dept t where t.dept_id = #{deptId}
    </select>
</mapper>