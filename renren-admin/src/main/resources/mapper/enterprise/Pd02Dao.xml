<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pd02.dao.Pd02Dao">

    <select id="getList" resultType="io.renren.modules.enterprise.pd02.dto.Pd02DTO">
        select t.PD0201,t.CP0201,t.USETYPE,t.OCCURTIME,t.PD0101,
        t.OPERATIONPERSON,t.REGISTDATE,t.MEMO
        from B_PD02 t,b_pd01 a,R_PJ01_DEPT b
        where a.PJ0101=b.PJ0101 and t.PD0101=a.PD0101 and b.DEPT_ID=#{deptId}
        <if test="devicename != null and devicename.trim() != ''">
            and DEVICENAME like #{devicename}
        </if>
    </select>

    <select id="getPersonBankNum" resultType="io.renren.modules.enterprise.pd02.dto.Pd02PersonBankDTO">

        select b.idcardnumber, t.payrollbankcardnumber, t.ps0201 from b_ps01 b, b_ps02 t
        where b.ps0101 = t.ps0101 and t.pj0101 = #{pj0101} and b.idcardnumber in
        <foreach item="idcardnumber" collection="lists"  open="(" separator="," close=")">
            #{idcardnumber}
        </foreach>
    </select>

    <insert id="batchInsertPersonBank">
        insert into b_pd02 (PD0201, PD0101, WORKER_NAME, WORKER_IDCARDNUMBER, PAYROLLBANKNAME, PAYROLLBANKCARDNUMBER, PAYROLLTOPBANKCODE, DATA_FLAG, REMARK, CREATE_TIME, PS0201, PAYROLLTOPBANK, PAYROLLNO, PJ0101)
        select t.* from (
        <foreach collection="list" separator="union all" index="index" item="item" >
            select
            #{item.pd0201},
            #{item.pd0101},
            #{item.workerName},
            #{item.workerIdcardnumber},
            #{item.payrollbankname},
            #{item.payrollbankcardnumber},
            #{item.payrolltopbankcode},
            #{item.dataFlag},
            #{item.remark},
            #{item.createTime},
            #{item.ps0201},
            #{item.payrolltopbank},
            #{item.payrollno},
            #{item.pj0101} FROM DUAL
        </foreach>
        ) t
    </insert>

    <select id="handleAbnormalData" >

        update b_pd02 t set t.data_flag = '0' where t.pd0201 = #{pd0201}
    </select>

    <select id="getConfirmImportData" resultType="io.renren.modules.enterprise.pd02.dto.Pd02ConfirmImportDataDTO">
        select t.worker_idcardnumber, t.ps0201, t.payrollbankcardnumber, t.payrollbankname, t.payrolltopbankcode, t.payrollno
        from b_pd02 t
        where t.pd0101 = #{pd0101}
          and t.data_flag = 1
          and t.worker_idcardnumber is not null
          and t.ps0201 is not null
          and t.payrollbankcardnumber is not null
    </select>

    <update id="doConfirmImportData">
        <foreach collection="list" item="param" separator="" open="begin" close="end;">
            update b_ps02
            <set>
                <if test="param.payrollbankname != null and param.payrollbankname != ''">
                    payrollbankname =#{param.payrollbankname},
                </if>
                <if test="param.payrollbankcardnumber != null and param.payrollbankcardnumber != ''">
                    payrollbankcardnumber =#{param.payrollbankcardnumber},
                </if>
                <if test="param.payrolltopbankcode != null and param.payrolltopbankcode != ''">
                    payrolltopbankcode =#{param.payrolltopbankcode},
                </if>
                <if test="param.payrollno != null and param.payrollno != ''">
                    payrollno = #{param.payrollno}
                </if>
            </set>
            where ps0201 = #{param.ps0201};
        </foreach>
    </update>

    <select id="pageList" resultType="io.renren.modules.enterprise.pd02.dto.Pd02DTO">

        select t.pd0201,
               t.pd0101,
               t.worker_name,
               t.worker_idcardnumber,
               t.payrollbankname,
               t.payrollbankcardnumber,
               t.payrolltopbankcode,
               t.payrolltopbank,
               t.remark,
               t.data_flag,
               t.ps0201,
               t.payrollno,
               t.create_time
        from b_pd02 t
        where
           t.pd0101 = #{pd0101}
            <if test="dataFlag != null and dataFlag != ''" >
                and t.DATA_FLAG = #{dataFlag}
            </if>
        order by t.data_flag, t.pd0201 desc
    </select>
</mapper>