<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pw01.dao.Pw01Dao">

    <resultMap type="io.renren.modules.enterprise.pw01.entity.Pw01Entity" id="pw01Map">
        <result property="pw0101" column="PW0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="yfgz" column="YFGZ"/>
        <result property="ffzrs" column="FFZRS"/>
        <result property="createDate" column="CREATEDATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="month" column="MONTH"/>
        <result property="issubmit" column="ISSUBMIT"/>
        <result property="sfgz" column="SFGZ"/>
        <result property="submittime" column="SUBMITTIME"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditreason" column="AUDITREASON"/>
    </resultMap>


    <select id="getList" resultType = "io.renren.modules.enterprise.pw01.dto.Pw01DTO">
        select t.pw0101,
               t1.name projectName,
               t.yfgz,
               t.ffzrs,
               t.month,
               TO_DATE(TO_CHAR(t.month), 'YYYYMM') salaryMonth,
               t.sfgz,
               t.submittime,
               t.issubmit,
               t.auditstatus,
               t.auditreason
        from b_pw01 t inner join b_pj01 t1 on t1.pj0101 = t.pj0101
        where t.pj0101 = #{params.pj0101}
        <if test="params.salaryMonth != null and params.salaryMonth != ''">
            and TO_DATE(TO_CHAR(t.month), 'YYYYMM') = TO_DATE(TO_CHAR(#{params.salaryMonth}), 'YYYY-MM')
        </if>
    </select>
</mapper>