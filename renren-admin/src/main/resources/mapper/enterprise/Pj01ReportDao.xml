<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01report.dao.Pj01ReportDao">

    <resultMap type="io.renren.modules.enterprise.pj01report.entity.Pj01ReportEntity" id="pj01ReportMap">
        <result property="pj0101" column="PJ0101"/>
        <result property="payBankCode" column="PAY_BANK_CODE"/>
        <result property="reportType" column="REPORT_TYPE"/>
    </resultMap>


    <select id="getListData" resultType="io.renren.modules.enterprise.pj01report.dto.Pj01ReportDTO">
        SELECT p.NAME,pr.PJ0101,pr.PAY_BANK_CODE code ,FN_REPORT_PARAMS_PJ01(pr.REPORT_TYPE) report  FROM B_PJ01_REPORT pr,B_PJ01 p,R_PJ01_DEPT r
        WHERE pr.PJ0101 = p.PJ0101 and r.PJ0101 = p.PJ0101 and r.DEPT_ID =#{deptId}
        <if test="name !=null and name !=''">
            and p.NAME like '%' || #{name} || '%'
        </if>
        ORDER BY p.UPDATE_DATE DESC
    </select>

    <select id="getData" resultType="io.renren.modules.enterprise.pj01report.dto.Pj01ReportDTO">
        SELECT p.NAME,pr.PJ0101, pr.PAY_BANK_CODE code , pr.REPORT_TYPE report from
        B_PJ01_REPORT pr,B_PJ01 p WHERE pr.PJ0101 = p.PJ0101 and  pr.PJ0101 = #{pj0101}
    </select>


</mapper>