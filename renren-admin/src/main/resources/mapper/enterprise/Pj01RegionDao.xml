<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01region.dao.Pj01RegionDao">
    <select id="pageList" resultType="io.renren.modules.enterprise.pj01region.dto.Pj01RegionDTO">
        select t.*
          from B_PJ01_REGION t
         where t.pj0101 = #{pj0101}
         order by t.create_date desc
    </select>
</mapper>