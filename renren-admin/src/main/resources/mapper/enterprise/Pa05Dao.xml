<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa05.dao.Pa05Dao">
    <resultMap type="io.renren.modules.enterprise.pa05.entity.Pa05Entity" id="pa05Map">
        <result property="pa0501" column="PA0501"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="tm0101" column="TM0101"/>
        <result property="issuedate" column="ISSUEDATE"/>
        <result property="workercount" column="WORKERCOUNT"/>
        <result property="accountnum" column="ACCOUNTNUM"/>
        <result property="salarystatus" column="SALARYSTATUS"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPageList" resultType="io.renren.modules.enterprise.pa05.dto.Pa05PageDTO">
        SELECT t.*, decode(nvl(b.onum, 0), 0, 0, 1) isupload
        FROM (SELECT t.pa0501,
                     b.name pjname,
                     m.teamname,
                     t.issuedate,
                     t.workercount,
                     t.accountnum,
                     t.salarystatus
              FROM b_pa05 t, b_pj01 b, b_tm01 m
              where t.pj0101 = b.pj0101
                and t.tm0101 = m.tm0101
                and b.pj0101 = #{params.pj0101}
                <if test="params.tm0101 != null and params.tm0101 != ''">
                    and m.tm0101 = #{params.tm0101}
                </if>
                <if test="params.kqdate != null and params.kqdate != ''">
                    and to_char( t.ISSUEDATE, 'yyyyMM') = #{params.kqdate}
                </if>
                order by t.create_date desc
             ) t
                 left join (SELECT count(*) onum, b.busisysno
                            FROM b_ot01 b
                            where b.BUSITYPE = '11'
                              and b.whether = '1'
                            group by b.busisysno) b
                           on t.pa0501 = b.busisysno
    </select>

    <select id="getSearchPageList" resultType="io.renren.modules.enterprise.pa05.dto.Pa05SearchPersonDTO">
        SELECT t.*, nvl(k.kqcount, 0) kqcount
        FROM (
                 SELECT t.tm0101,
                        p.ps0201,
                        p.in_or_out,
                        t.teamname,
                        b.name,
                        b.idcardnumber,
                        '[' || (select case
                                           when count(1) = 0 then
                                               0
                                           else
                                               1
                                           end
                                from b_ps08 x
                                where x.ps0201 = p.ps0201) || ',' ||
                        (select case
                                    when count(1) = 0 then
                                        0
                                    else
                                        1
                                    end
                         from b_ot01 y
                         where y.busisysno = p.ps0201
                           and y.busitype = '10') || ']' as signupload
                 FROM b_tm01 t,
                      b_ps01 b,
                      b_ps02 p
                 where b.ps0101 = p.ps0101
                   and p.pj0101 = t.pj0101
                   and t.tm0101 = p.tm0101
                   and p.pj0101 = #{params.pj0101}
                <if test="params.tm0101 != null and params.tm0101 != ''" >
                    and t.tm0101 = #{params.tm0101}
                </if>
                <if test="params.inOrOut != null and params.inOrOut != ''" >
                    and p.in_or_out = #{params.inOrOut}
                </if>
             ) t
                 left join (select t.user_id, count(*) kqcount
                            from b_kq02 t
                            <where>
                                <if test="params.kqdate != null and params.kqdate != ''" >
                                    to_char( t.CHECKDATE, 'yyyyMM') = #{params.kqdate}
                                </if>
                            </where>
                            group by t.user_id) k on t.ps0201 = k.kqcount
    </select>

    <delete id="deletePa05ById">

        delete from b_pa05 t where t.pa0501 = #{pa0501}

    </delete>

    <select id="detailPage" resultType="io.renren.modules.enterprise.pa06.dto.Pa06DTO">

        SELECT t.*, e.bankname
        FROM (select t.PA0601,
                     t.PA0501,
                     t.NAME,
                     t.IDCARDNUMBER,
                     t.CELLPHONE,
                     t.KQCOUNT,
                     t.SALARYDAY,
                     t.SHOULDSALARY,
                     t.ACTUALSALARY,
                     nvl(t.PAYROLLTOPBANKCODE, '无') PAYROLLTOPBANKCODE,
                     nvl(t.PAYROLLBANKCARDNUMBER, '无') PAYROLLBANKCARDNUMBER
              from b_pa06 t
              where t.pa0501 = #{params.pa0501}) t
                 left join (SELECT c.DICT_LABEL bankname, c.DICT_VALUE
                            FROM SYS_DICT_TYPE d, SYS_DICT_DATA c
                            WHERE d.ID = c.DICT_TYPE_ID
                              AND d.DICT_TYPE = 'PAY_BANK_CODE') e
                           on t.PAYROLLTOPBANKCODE = e.DICT_VALUE

    </select>

    <select id="getDetail" resultType="io.renren.modules.enterprise.pa05.dto.Pa05DetailDTO">

        SELECT t.*, m.teamname, j.name pjname
        FROM b_pa05 t, b_tm01 m, b_pj01 j
        where t.pj0101 = j.pj0101
          and m.tm0101 = t.tm0101
          and t.pa0501 = #{pa0501}

    </select>
</mapper>