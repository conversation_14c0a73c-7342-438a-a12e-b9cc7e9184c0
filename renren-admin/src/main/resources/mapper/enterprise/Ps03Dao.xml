<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps03.dao.Ps03Dao">

    <select id="selectByPs0201" resultType="io.renren.modules.enterprise.ps03.dto.Ps03DTO">
        select *
        from B_PS03 t
        where t.PS0201 = #{ps0201}
    </select>
    <select id="getNowMonthSalary" resultType="int">
        select count(*)
          from b_ps03 t
         where to_char(t.SALARYDATE, 'yyyyMM') = #{yearMonth}
           and t.pj0101 = #{pj0101}
</select>
<select id="getPs02List" resultType="io.renren.modules.enterprise.ps03.dto.Ps03DTO">
    select t.*,
       a.special_account_no,
       a.special_account,
       to_date(#{yearMonth}, 'yyyyMM') as SALARYDATE,
       nvl(t.attendancedays, 0) attendancedays,
       a.bank_name,
       nvl(ROUND((s.TIMEWAGE /
                 (select to_char(last_day(sysdate), 'dd') - 2 from dual)) *
                 t.attendancedays,
                 2),
           0) actualamount,
       nvl(s.TIMEWAGE, 0) TOTALPAYAMOUNT
  from (select b.ps0101,
               p.pj0101,
               b.name,
               p.ps0201,
               b.idcardnumber,
               nvl((select sum(case
                               when count(*) > 0 then
                                1
                               else
                                0
                             end) days
                          from b_kq02 k
                         where k.user_id = p.ps0201
                         and to_char(k.checkdate, 'yyyyMM') = #{yearMonth}
                         group by to_char(k.checkdate, 'yyyyMMdd')), 0) attendancedays,
               p.PAYROLLBANKCARDNUMBER
          from b_ps01 b,
               b_ps02 p,
               (select t.user_id
                  from b_kq02 t
                 where to_char(t.checkdate, 'yyyyMM') = #{yearMonth}
                   and t.pj0101 = #{pj0101}
                 group by t.user_id
                union
                select p.ps0201 as user_id
                  from b_ps01 b, b_ps02 p
                 where b.ps0101 = p.ps0101
                   and p.pj0101 = #{pj0101}
                   and p.in_or_out = '1') s
         where b.ps0101 = p.ps0101
           and p.ps0201 = s.user_id) t
  left join b_pa01 a
    on t.pj0101 = a.pj0101
  left join b_ps08 s
    on t.ps0201 = s.ps0201
</select>
<insert id="savePs03List" useGeneratedKeys="false">
insert into b_ps03 (ps0301, pj0101, ps0101, special_account, attendancedays, bank_name, special_account_no, salarydate, payrollbankcardnumber, totalpayamount, actualamount, create_date)
select SEQ_B_PS03.NEXTVAL, t.* from (
  <foreach collection="list" separator="union all" index="index" item="item" >
      select
      #{item.pj0101},
      #{item.ps0101},
      #{item.specialAccount},
      #{item.attendancedays},
      #{item.bankName},
      #{item.specialAccountNo},
      #{item.salarydate},
      #{item.payrollbankcardnumber},
      #{item.totalpayamount},
      #{item.actualamount},
      sysdate FROM DUAL
  </foreach>
  ) t
</insert>
<select id="getPageList" resultType="io.renren.modules.enterprise.ps03.dto.Ps03PageDTO">
    select t.* ,b.name personname, j.name pjname
      from b_ps03 t, b_ps01 b, b_ps02 p, b_pj01 j
     where b.ps0101 = p.ps0101
       and t.ps0101 = b.ps0101
       and p.pj0101 = t.pj0101
       and t.pj0101 = j.pj0101
       and p.pj0101 = #{pj0101}
       and to_char(t.salarydate, 'yyyyMM') = #{yearMonth}
       <if test="personname != null and personname != ''" >
            and b.NAME like '%' || #{personname} || '%'
       </if>
</select>
<update id="updateBatchPs03">
    <foreach collection="list" item="item" open="begin" close=";end;" separator=";" index="index">
        update b_ps03
           <set>
            <if test="item.specialAccount != null and item.specialAccount != ''" >
            special_account = #{item.specialAccount},
            </if>
           <if test="item.bankName != null and item.bankName != ''" >
                bank_name = #{item.bankName},
            </if>
            <if test="item.attendancedays != null and item.attendancedays != ''" >
                attendancedays = #{item.attendancedays},
            </if>
            <if test="item.specialAccountNo != null and item.specialAccountNo != ''" >
                special_account_no = #{item.specialAccountNo},
            </if>
            <if test="item.specialAccount != null and item.specialAccount != ''" >
                salarydate = #{item.salarydate},
            </if>
            <if test="item.payrollbankcardnumber != null and item.payrollbankcardnumber != ''" >
                payrollbankcardnumber = #{item.payrollbankcardnumber},
            </if>
            <if test="item.totalpayamount != null and item.totalpayamount != ''" >
                totalpayamount = #{item.totalpayamount},
            </if>
            <if test="item.actualamount != null and item.actualamount != ''" >
                actualamount = #{item.actualamount},
            </if>
               update_date = sysdate
           </set>
         where ps0301 = #{item.ps0301,jdbcType=BIGINT}
    </foreach>
</update>
<select id="getPs02Exist" resultType="int">
    select count(*)
      from b_ps03 t
     where t.pj0101 = #{pj0101}
       and t.ps0101 = #{ps0101}
       and to_char(t.salarydate, 'yyyyMM') = #{yearMonth}
</select>
<select id="getPs02Info" resultType="io.renren.modules.enterprise.ps03.dto.Ps03DTO">
select t.ps0301,
       t.pj0101,
       t.ps0101,
       t.attendancedays,
       t.payrollbankcardnumber,
       nvl(ROUND((b.TIMEWAGE /
                 (select to_char(last_day(sysdate), 'dd') - 2 from dual)) *
                 t.attendancedays,
                 2),
           0) actualamount,
       nvl(b.TIMEWAGE, 0) TOTALPAYAMOUNT,
       0 ACTUALAMOUNT
  from (select b.ps0101,
               p.ps0201,
               p.payrollbankcardnumber,
               nvl((select sum(case
                               when count(*) > 0 then
                                1
                               else
                                0
                             end) days
                          from b_kq02 k
                         where k.user_id = p.ps0201
                         and to_char(k.checkdate, 'yyyyMM') = #{yearMonth}
                         group by to_char(k.checkdate, 'yyyyMMdd')), 0) attendancedays,
               p.pj0101,
               s.ps0301
          from b_ps01 b, b_ps02 p, b_ps03 s
         where b.ps0101 = p.ps0101
           and b.ps0101 = s.ps0101
           and p.pj0101 = s.pj0101
           and b.ps0101 = #{ps0101}
           and p.pj0101 = #{pj0101}
           and to_char(s.salarydate, 'yyyyMM') = #{yearMonth}) t
  left join b_ps08 b
    on b.ps0201 = t.ps0201
</select>
<select id="getExportPs03List" resultType="io.renren.modules.enterprise.ps03.excel.Ps03Excel">
    select j.name pjname,
           nvl(t.actualamount, 0) actualamount,
           nvl(t.attendancedays, '0') attendancedays,
           nvl(t.BANK_NAME, '未填写') bankName,
           nvl(t.payrollbankcardnumber, '未填写') payrollbankcardnumber,
           nvl(b.name, '未填写') personname,
           nvl(b.idcardnumber, '未填写') idcardnumber,
           to_char(to_date(#{yearMonth}, 'yyyyMM')) as salarydate,
           nvl(t.special_account, '未填写') specialAccount,
           nvl(t.special_account_no, '未填写') specialAccountNo,
           nvl(t.totalpayamount, 0) totalpayamount
      from b_ps03 t, b_pj01 j, b_ps01 b
     where b.ps0101 = t.ps0101
       and t.pj0101 = j.pj0101
       and t.pj0101 = j.pj0101
       and t.pj0101 = #{pj0101}
       and to_char(t.salarydate, 'yyyyMM') = #{yearMonth}
</select>

</mapper>