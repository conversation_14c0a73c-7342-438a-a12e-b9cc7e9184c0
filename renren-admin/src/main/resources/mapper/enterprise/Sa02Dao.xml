<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.sa02.dao.Sa02Dao">
    <select id="page" resultType="io.renren.modules.enterprise.sa02.dto.Sa02DTO">
        select a.* from B_Sa02 a where 1 = 1
        <if test="name != null and name.trim() !=''">
            and a.name like '%'||#{name}||'%'
        </if>
        <if test="institutionnumber != null and institutionnumber.trim() !=''">
            and a.institutionnumber like '%'||#{institutionnumber}||'%'
        </if>
    </select>
</mapper>