<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa01.dao.Pa01Dao">
    <select id="pageList" resultType="io.renren.modules.enterprise.pa01.dto.Pa01DTO">
        select  t.PA0101,
                t.PJ0101,
                t.SPECIAL_ACCOUNT,
                t.BANK_NAME,
                t.SPECIAL_ACCOUNT_NO,
                t.PAY_BANK_CODE,
                t.check_state,
                t.check_note
                from b_pa01 t
                where t.pj0101 = #{pj0101}
        <if test="specialAccountNo != null and specialAccountNo != ''">
            and t.special_account_no like '%'||#{specialAccountNo}||'%'
        </if>
    </select>
    <select id="getSpecialAccountSupplement" resultType="java.lang.Long">
        select count(1)
          from B_PA01 t
         where t.pj0101 = #{pj0101}
           and t.special_account is not null
           and t.bank_name is not null
           and t.special_account_no is not null
           and t.pay_bank_code is not null
    </select>
</mapper>