<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.jg01.dao.Jg01Dao">

<select id="getPageList" resultType="io.renren.modules.enterprise.jg01.dto.Jg01DTO">
    select t.*
    from B_JG01 t
    where t.pj0101 = #{pj0101}
    <if test="wartype != null and wartype != ''" >
        and t.wartype = #{wartype}
    </if>
    <if test="warstatus != null and warstatus != ''" >
        and t.warstatus = #{warstatus}
    </if>
    <if test="starttime != null and starttime != ''" >
        and to_char(t.starttime,'yyyyMM') = #{starttime}
    </if>
    order by t.create_date desc, t.warstatus
    </select>

<select id="getAppropriateList" resultType="io.renren.modules.enterprise.jg01.dto.Jg01DTO">
    select * from b_jg01 t where t.pj0101 = #{pj0101} and t.wartype = '001'
    <if test="warstatus != null and warstatus != ''" >
        and t.warstatus = #{warstatus}
    </if>
    <if test="starttime != null and starttime != ''" >
        and to_char(t.starttime, 'yyyyMMdd') = #{starttime}
    </if>
    order by t.starttime desc
    </select>

    <select id="selectInfoById" resultType="io.renren.modules.enterprise.jg01.dto.Jg01DTO">
        select t.*,a.name from b_jg01 t,b_pj01 a where t.pj0101 = a.pj0101 and t.jg0101 = #{jg0101}
    </select>
    <select id="pcYjProcess" statementType="CALLABLE">
        {call PC_YJ_PROCESS(#{Cjg0101,mode=IN,jdbcType=VARCHAR},
                            #{Cdealstatus,mode=IN,jdbcType=VARCHAR},
                            #{Cdealinfo,mode=IN,jdbcType=VARCHAR},
                            #{Cdealid,mode=IN,jdbcType=VARCHAR},
                            #{cRET,mode=OUT ,jdbcType=VARCHAR})}
    </select>
    <select id="dealPageList" resultType="io.renren.modules.enterprise.jg05.dto.Jg05DTO">
        select t.*,(select a.real_name from sys_user a where a.id = t.dealid) as dealtor
         from B_JG05 t where t.jg0101 = #{jg0101} order by t.create_date desc
    </select>
</mapper>