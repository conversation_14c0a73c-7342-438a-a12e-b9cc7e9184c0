<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.share.dao.ShareDao">
    <select id="getList" resultType="io.renren.modules.enterprise.share.dto.ShareDTO">
        select t.id as user_id,
               t.memo as company,
               t.linkman,
               t.linkphone,
               case
                 when a.id is not null then
                  '1'
                 else
                  '0'
               end as isbind,
               a.create_date as bindtime,
               a.pj0101
          from I_SHARE_USER t
          left join i_share_project a
            on t.id = a.user_id
           and a.pj0101 = #{pj0101}
         where t.whether = '1' order by t.create_date desc
    </select>

    <delete id="unbind">
        delete from I_SHARE_PROJECT t
         where t.user_id = #{userId}
           and t.pj0101 = #{pj0101}
    </delete>
</mapper>