<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj03.dao.Pj03Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.pj03.dto.Pj03DTO">
        select t.*,(select case
                               when count(1) = 0 then
                                   0
                               else
                                   1
                               end
                    from b_pj05 x
                    where x.pj0301 = t.pj0301) hasExams
        from B_PJ03 t
        where t.pj0101 = #{pj0101}
        <if test="trainingname != null and trainingname != ''">
            and t.trainingname like '%'||#{trainingname}||'%'
        </if>
    </select>

    <select id="pagePs02ByTm0101" resultType="io.renren.modules.enterprise.ps02.dto.Ps02PageDTO">
        select t1.name personName,
               (select teamname from b_tm01 where tm0101 = t2.tm0101)   teamname,
               t2.worktypecode      workTypeCode
               from b_ps01 t1, b_ps02 t2
        where t1.ps0101 = t2.ps0101 and in_or_out = '1'
        <foreach collection="tm0101List" item="item" open="and t2.tm0101 in(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="pagePs02" resultType="io.renren.modules.enterprise.ps02.dto.Ps02PageDTO">
        select t4.name personName,
               (select teamname from b_tm01 where tm0101 = t3.tm0101)   teamname,
               t3.worktypecode workTypeCode
        from b_pj03 t1, b_pj04 t2, b_ps02 t3, b_ps01 t4
        where t1.pj0301 = t2.pj0301
          and t2.ps0201 = t3.ps0201
          and t3.ps0101 = t4.ps0101
          and t1.pj0301 = #{params.pj0301}
    </select>
</mapper>