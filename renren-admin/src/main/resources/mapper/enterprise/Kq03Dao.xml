<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq03.dao.Kq03Dao">

    <resultMap type="io.renren.modules.enterprise.kq03.entity.Kq03Entity" id="kq03Map">
        <result property="deviceserialno" column="DEVICESERIALNO"/>
        <result property="devicekey" column="DEVICEKEY"/>
        <result property="devicestatus" column="DEVICESTATUS"/>
    </resultMap>
    <select id="selectByDeviceKey" resultType="java.lang.Integer">
        select count(0)
        from B_KQ03 t
        where t.DEVICESTATUS = '0'
          and t.DEVICEKEY = #{deviceKey}
    </select>


</mapper>