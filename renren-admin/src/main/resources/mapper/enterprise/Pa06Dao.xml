<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa06.dao.Pa06Dao">

    <resultMap type="io.renren.modules.enterprise.pa06.entity.Pa06Entity" id="pa06Map">
        <result property="pa0601" column="PA0601"/>
        <result property="pa0501" column="PA0501"/>
        <result property="name" column="NAME"/>
        <result property="idcardnumber" column="IDCARDNUMBER"/>
        <result property="payrolltopbankcode" column="PAYROLLTOPBANKCODE"/>
        <result property="payrollbankcardnumber" column="PAYROLLBANKCARDNUMBER"/>
        <result property="cellphone" column="CELLPHONE"/>
        <result property="kqcount" column="KQCOUNT"/>
        <result property="salaryday" column="SALARYDAY"/>
        <result property="shouldsalary" column="SHOULDSALARY"/>
        <result property="actualsalary" column="ACTUALSALARY"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getSalaryNum" resultType="java.lang.Integer">

        SELECT count(*)
        FROM b_pa05 b, b_pa06 p
        where b.pa0501 = p.pa0501
          and to_char(b.ISSUEDATE, 'yyyyMM') = #{kqdate,jdbcType=VARCHAR}
          and b.pj0101 = #{pj0101}
        <!--审核成功-->
          and b.SALARYSTATUS = '2'
          and p.idcardnumber in ( (SELECT b.idcardnumber
                                   FROM b_ps01 b, b_ps02 p
                                   where b.ps0101 = p.ps0101
                                     and p.ps0201 in
                    <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
        ) )

    </select>

    <select id="getPersonData" resultType="io.renren.modules.enterprise.pa06.dto.Pa06DTO">

        SELECT t.*, nvl((t.salaryday * t.workTime), 0) shouldsalary
        FROM (SELECT t.*,
                     nvl(k.kqcount, 0) kqcount,
                     nvl(m.timewage, 0) salaryday,
                     nvl(ROUND(kqcount / #{currentMonthNum}, 2), 0) workTime
              FROM (SELECT b.ps0101,
                           p.ps0201,
                           p.pj0101,
                           b.name,
                           b.idcardnumber,
                           p.PAYROLLBANKCARDNUMBER,
                           p.PAYROLLTOPBANKCODE,
                           b.CELLPHONE
                    FROM b_ps01 b, b_ps02 p
                    where b.ps0101 = p.ps0101
                    and p.ps0201 in
                    <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                   ) t
                       left join (select t.user_id, count(*) kqcount
                                  from b_kq02 t
                                  where to_char(t.checkdate, 'yyyyMM') = #{createDate,jdbcType=VARCHAR}
                                  group by t.user_id) k
                                 on t.ps0201 = k.user_id
                       left join (select t.ps0201, t.TIMEWAGE from b_ps08 t) m
                                 on m.ps0201 = t.ps0201) t

    </select>

    <select id="getPage" resultType="io.renren.modules.enterprise.pa06.dto.Pa06DTO">

        select * from b_pa06 t where t.pa0501 = #{parmas.pa0501}

    </select>

    <update id="sumAccountNum">

        update b_pa05 t
        set t.ACCOUNTNUM =
                (SELECT sum(t.actualsalary)
                 FROM b_pa06 t
                 where t.pa0501 = #{pa0501} group by t.pa0501)
        where t.pa0501 = #{pa0501}

    </update>

    <select id="getWorkerNumAndAccountNum" resultType="java.lang.Double">

        SELECT sum(a.shouldsalary) accountnum
        FROM (SELECT t.*, nvl((t.timewage * t.workTime), 0) shouldsalary
              FROM (SELECT t.*,
                           nvl(k.kqcount, 0) kqcount,
                           nvl(m.timewage, '0') timewage,
                           nvl(ROUND(kqcount / 31, 2), 0) workTime
                    FROM (SELECT p.ps0201, p.pj0101
                          FROM b_ps02 p
                          where p.ps0201 in
                    <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                         ) t
                             left join (select t.user_id, count(*) kqcount
                                        from b_kq02 t
                                        where to_char(t.checkdate, 'yyyyMM') = #{createDate}
                                        group by t.user_id) k
                                       on t.ps0201 = k.user_id
                             left join (select t.ps0201, t.TIMEWAGE from b_ps08 t) m
                                       on m.ps0201 = t.ps0201) t) a
        group by a.pj0101

    </select>

    <select id="getExportData" resultType="io.renren.modules.enterprise.pa06.dto.Pa06ExportDataDTO">

        select t.*, d.worktype, e.contactno, e.payrollbankname
        from (SELECT (j.name || '(' || to_char(b.issuedate, 'yyyyMM') ||
                      ')农民工工资发放记录表') title,
                     j.name pjname,
                     a.name personname,
                     z.worktypecode,
                     p.cellphone,
                     p.idcardnumber,
                     p.payrollbankcardnumber,
                     p.payrolltopbankcode,
                     p.salaryday,
                     p.kqcount,
                     p.shouldsalary,
                     p.actualsalary,
                     z.ps0201
              FROM b_pa05 b, b_pa06 p, b_ps01 a, b_ps02 z, b_pj01 j
              where b.pa0501 = p.pa0501
                and a.ps0101 = z.ps0101
                and b.pj0101 = z.pj0101
                and z.pj0101 = j.pj0101
                and p.idcardnumber = a.idcardnumber
                and b.pa0501 = #{pa0501}) t
                 left join (SELECT c.DICT_LABEL worktype, c.DICT_VALUE
                            FROM SYS_DICT_TYPE d, SYS_DICT_DATA c
                            WHERE d.ID = c.DICT_TYPE_ID
                              AND d.DICT_TYPE = 'WORKTYPECODE') d
                           on t.worktypecode = d.DICT_VALUE
                 left join (SELECT c.DICT_LABEL payrollbankname, c.DICT_VALUE
                            FROM SYS_DICT_TYPE d, SYS_DICT_DATA c
                            WHERE d.ID = c.DICT_TYPE_ID
                              AND d.DICT_TYPE = 'PAY_BANK_CODE') e
                           on t.payrolltopbankcode = e.DICT_VALUE
                 left join (select e.contractno contactno, e.ps0201 from b_ps08 e) e
                           on t.ps0201 = e.ps0201

    </select>

    <delete id="deletePa06List">

        delete from b_pa06 t where t.pa0501 = #{pa0501}

    </delete>
</mapper>