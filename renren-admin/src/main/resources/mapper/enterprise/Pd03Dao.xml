<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pd03.dao.Pd03Dao">

    <select id="getPersonContactNum" resultType="io.renren.modules.enterprise.pd03.dto.Pd03PersonContactDTO">

        select distinct b.idcardnumber,
        t.ps0201,
        b.name,
        decode(b.gender, 1, '男', 2, '女') gender,
        b.birthday,
        b.address,
        b.cellphone,
        (select g.dict_label
        from sys_dict_type f, sys_dict_data g
        where f.dict_type = 'WORKTYPECODE'
        and f.id = g.dict_type_id
        and g.dict_value = t.worktypecode) worktypecode
        from b_ps01 b, b_ps02 t
        where b.ps0101 = t.ps0101
        and t.pj0101 = #{pj0101} and b.idcardnumber in
        <foreach item="idcardnumber" collection="lists"  open="(" separator="," close=")">
            #{idcardnumber}
        </foreach>
    </select>

    <insert id="batchInsertPersonBank">
        insert into b_pd02 (PD0201, PD0101, WORKER_NAME, WORKER_IDCARDNUMBER, PAYROLLBANKNAME, PAYROLLBANKCARDNUMBER, PAYROLLTOPBANKCODE, DATA_FLAG, REMARK, CREATE_TIME, PS0201, PAYROLLTOPBANK, PJ0101)
        select t.* from (
        <foreach collection="list" separator="union all" index="index" item="item" >
            select
            #{item.pd0201},
            #{item.pd0101},
            #{item.workerName},
            #{item.workerIdcardnumber},
            #{item.payrollbankname},
            #{item.payrollbankcardnumber},
            #{item.payrolltopbankcode},
            #{item.dataFlag},
            #{item.remark},
            #{item.createTime},
            #{item.ps0201},
            #{item.payrolltopbank},
            #{item.pj0101} FROM DUAL
        </foreach>
        ) t
    </insert>

    <select id="handleAbnormalData" >

        update b_pd03 t set t.data_flag = '0' where t.pd0301 = #{pd0301}
    </select>

    <select id="getCompanyList" resultType="io.renren.modules.enterprise.pd03.dto.Pd03PersonCompanyDTO">
        select t.*, a.*
        from (select t.ps0201, t.pj0101, a.name, c.cp0201, a.idcardnumber
              from b_ps02 t,
                   b_ps01 a,
                   b_tm01 c
              where t.ps0101 = a.ps0101
                and t.tm0101 = c.tm0101
        and t.pj0101 = #{pj0101}
        and a.idcardnumber in
        <foreach item="idcardnumber" collection="lists"  open="(" separator="," close=")">
            #{idcardnumber}
        </foreach>
        ) t
                 left join (select b.corpcode,
                                   b.corpname,
                                   b.address as corpaddress,
                                   b.legalman,
                                   b.linkman,
                                   b.linkcellphone,
                                   t.cp0201
                            from b_cp01 b,
                                 b_cp02 t
                            where b.cp0101 = t.cp0101) a
                           on t.cp0201 = a.cp0201
    </select>

    <insert id="batchInsertPersonContact">

        insert into b_pd03
        (pd0301,
        ps0201,
        pj0101,
        contractno,
        corpname,
        legalman,
        corpcode,
        corpaddress,
        linkcellphone,
        name,
        gender,
        birthday,
        idcardnumber,
        address,
        cellphone,
        contractbegin,
        prjname,
        projectname,
        worktypecode,
        duty,
        salarypaymentmethod,
        timewage,
        workload,
        workloadwage,
        otherwage,
        payday,
        workhoursystem,
        workhour,
        workday,
        workirregular,
        welfare,
        consignor,
        workeraddress,
        otherthings,
        signdate,
        remark,
        create_date,
        update_date,
        pd0101,
        data_flag
        )
        select t.* from (
        <foreach collection="list" separator="union all" index="index" item="item" >
            select
            #{item.pd0301},
            #{item.ps0201},
            #{item.pj0101},
            #{item.contractno},
            #{item.corpname},
            #{item.legalman},
            #{item.corpcode},
            #{item.corpaddress},
            #{item.linkcellphone},
            #{item.name},
            #{item.gender},
            #{item.birthday},
            #{item.idcardnumber},
            #{item.address},
            #{item.cellphone},
            #{item.contractbegin},
            #{item.prjname},
            #{item.projectname},
            #{item.worktypecode},
            #{item.duty},
            #{item.salarypaymentmethod},
            #{item.timewage},
            #{item.workload},
            #{item.workloadwage},
            #{item.otherwage},
            #{item.payday},
            #{item.workhoursystem},
            #{item.workhour},
            #{item.workday},
            #{item.workirregular},
            #{item.welfare},
            #{item.consignor},
            #{item.workeraddress},
            #{item.otherthings},
            #{item.signdate},
            #{item.remark},
            #{item.createDate},
            #{item.updateDate},
            #{item.pd0101},
            #{item.dataFlag}
            FROM DUAL
        </foreach>
        ) t
    </insert>

    <select id="getConfirmImportData" resultType="io.renren.modules.enterprise.pd03.dto.Pd03ConfirmImportDataDTO">
        select t.pd0301,
               t.ps0201,
               t.pj0101,
               t.contractno,
               t.corpname,
               t.legalman,
               t.corpcode,
               t.corpaddress,
               t.linkcellphone,
               t.name,
               t.gender,
               t.birthday,
               t.idcardnumber,
               t.address,
               t.cellphone,
               t.contractbegin,
               t.prjname,
               t.projectname,
               t.worktypecode,
               t.duty,
               t.salarypaymentmethod,
               t.timewage,
               t.workload,
               t.workloadwage,
               t.otherwage,
               t.payday,
               t.workhoursystem,
               t.workhour,
               t.workday,
               t.workirregular,
               t.welfare,
               t.consignor,
               t.workeraddress,
               t.otherthings,
               t.signdate,
               t.data_flag,
               t.remark,
               t.create_date,
               t.update_date,
               t.pd0101,
               p.ps0801
        from b_pd03 t
                 left join b_ps08 p
                           on p.ps0201 = t.ps0201
        where t.pd0101 = #{pd0101}
          and t.data_flag = 1
    </select>

    <update id="doUpdateConfirmImportData">
        <foreach collection="list" item="param" separator="" open="begin" close="end;">
            update b_ps08
            set ps0801          = #{param.ps0801},
            ps0201              = #{param.ps0201},
            pj0101              = #{param.pj0101},
            contractno          = #{param.contractno},
            corpname            = #{param.corpname},
            legalman            = #{param.legalman},
            corpcode            = #{param.corpcode},
            corpaddress         = #{param.corpaddress},
            linkcellphone       = #{param.linkcellphone},
            name                = #{param.name},
            gender              = #{param.gender},
            birthday            = #{param.birthday},
            idcardnumber        = #{param.idcardnumber},
            address             = #{param.address},
            cellphone           = #{param.cellphone},
            contractbegin       = #{param.contractbegin},
            prjname             = #{param.prjname},
            projectname         = #{param.projectname},
            worktypecode        = #{param.worktypecode},
            duty                = #{param.duty},
            salarypaymentmethod = #{param.salarypaymentmethod},
            timewage            = #{param.timewage},
            workload            = #{param.workload},
            workloadwage        = #{param.workloadwage},
            otherwage           = #{param.otherwage},
            payday              = #{param.payday},
            workhoursystem      = #{param.workhoursystem},
            workhour            = #{param.workhour},
            workday             = #{param.workday},
            workirregular       = #{param.workirregular},
            welfare             = #{param.welfare},
            consignor           = #{param.consignor},
            workeraddress       = #{param.workeraddress},
            otherthings         = #{param.otherthings},
            signdate            = #{param.signdate},
            create_date         = #{param.createDate},
            update_date         = #{param.updateDate}
            where ps0201 = #{param.ps0201};
        </foreach>
    </update>

    <select id="getSysWorkerDictByWorkName" resultType="io.renren.common.common.dto.CommonDto">

        select g.dict_label as label, g.dict_value as value
        from sys_dict_type f, sys_dict_data g
        where f.dict_type = 'WORKTYPECODE'
          and f.id = g.dict_type_id
          and g.dict_label in
        <foreach item="dictname" collection="lists"  open="(" separator="," close=")">
            #{dictname}
        </foreach>
    </select>

    <select id="pageList" resultType="io.renren.modules.enterprise.pd03.dto.Pd03PageDTO">
        select a.pd0301,
        a.ps0201,
        a.pj0101,
        a.contractno,
<!--        a.corpname,-->
        a.legalman,
        a.corpcode,
        a.corpaddress,
        a.linkcellphone,
        a.name,
        a.gender,
        a.birthday,
        a.idcardnumber,
        a.address,
        a.cellphone,
        a.contractbegin,
        a.prjname,
        a.projectname,
        a.worktypecode,
        a.duty,
        a.salarypaymentmethod,
        a.timewage,
        a.workload,
        a.workloadwage,
        a.otherwage,
        a.payday,
        a.workhoursystem,
        a.workhour,
        a.workday,
        a.workirregular,
        a.welfare,
        a.consignor,
        a.workeraddress,
        a.otherthings,
        a.signdate,
        a.data_flag,
        a.remark,
        a.create_date,
        a.update_date,
        a.pd0101,
        b.corpname
        from b_pd03 a
        left join (select c.corpname, t.ps0201
        from b_ps02 t, b_tm01 m, b_cp01 c, b_cp02 p
        where m.cp0201 = p.cp0201
        and c.cp0101 = p.cp0101
        and t.tm0101 = m.tm0101) b
        on a.ps0201 = b.ps0201
        where a.pd0101 = #{pd0101}
        <if test="dataFlag != null and dataFlag != ''" >
            and a.DATA_FLAG = #{dataFlag}
        </if>
        order by a.data_flag, a.pd0301 desc
    </select>

    <select id="getSysWorkerDictByWorkCode" resultType="io.renren.common.common.dto.CommonDto">
        select g.dict_label as label, g.dict_value as value
        from sys_dict_type f, sys_dict_data g
        where f.dict_type = 'WORKTYPECODE'
        and f.id = g.dict_type_id
        and g.dict_value in
        <foreach item="dictname" collection="lists"  open="(" separator="," close=")">
            #{dictname}
        </foreach>
    </select>

    <insert id="doSaveConfirmImportData">

        insert into b_ps08
        (ps0801,
        ps0201,
        pj0101,
        contractno,
        corpname,
        legalman,
        corpcode,
        corpaddress,
        linkcellphone,
        name,
        gender,
        birthday,
        idcardnumber,
        address,
        cellphone,
        contractbegin,
        prjname,
        projectname,
        worktypecode,
        duty,
        salarypaymentmethod,
        timewage,
        workload,
        workloadwage,
        otherwage,
        payday,
        workhoursystem,
        workhour,
        workday,
        workirregular,
        welfare,
        consignor,
        workeraddress,
        otherthings,
        signdate,
        create_date
        )
        select t.* from (
        <foreach collection="list" separator="union all" index="index" item="item" >
            select
            #{item.ps0801},
            #{item.ps0201},
            #{item.pj0101},
            #{item.contractno},
            #{item.corpname},
            #{item.legalman},
            #{item.corpcode},
            #{item.corpaddress},
            #{item.linkcellphone},
            #{item.name},
            #{item.gender},
            #{item.birthday},
            #{item.idcardnumber},
            #{item.address},
            #{item.cellphone},
            #{item.contractbegin},
            #{item.prjname},
            #{item.projectname},
            #{item.worktypecode},
            #{item.duty},
            #{item.salarypaymentmethod},
            #{item.timewage},
            #{item.workload},
            #{item.workloadwage},
            #{item.otherwage},
            #{item.payday},
            #{item.workhoursystem},
            #{item.workhour},
            #{item.workday},
            #{item.workirregular},
            #{item.welfare},
            #{item.consignor},
            #{item.workeraddress},
            #{item.otherthings},
            #{item.signdate},
            #{item.createDate}
            FROM DUAL
        </foreach>
        ) t
    </insert>
</mapper>