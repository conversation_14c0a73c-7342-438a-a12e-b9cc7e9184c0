<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01info.dao.Pj01InfoDao">

    <select id="getListData" resultType="io.renren.modules.enterprise.pj01info.dto.Pj01InfoDTO">
        select
        t.NAME,t.PJ0101,t.AREACODE,t.INDUSTRY,t.PRJSTATUS,t.LINKMAN,t.LINKPHONE,
        t.INVESTTYPE,t.CONSTRUCTTYPE,t.STARTDATE,t.INVEST,t.CATEGORY,t.ADDRESS
        from B_PJ01 t,
        R_PJ01_DEPT a,
        sys_dept b
        where t.PJ0101 = a.PJ0101 and a.DEPT_ID = #{deptId} and b.id = a.dept_id
        <if test="name !=null and name !=''">
            and t.NAME like '%' || #{name} || '%'
        </if>
        <if test="areacode !=null and areacode !=''">
            and t.areacode like  #{areacode} || '%'
        </if>
    </select>

    <!--    参建单位列表分页查询SQL-->
    <select id="getCp02PageList" resultType="io.renren.modules.enterprise.pj01info.dto.Cp02PageDTO">
        select t.cp0201,
               (select c.NAME from b_pj01 c where c.PJ0101 = t.PJ0101) name,
               b.corpname,
               b.corpcode,
               b.linkman,
               b.linkcellphone,
               t.corptype
        from b_cp02 t,
             r_pj01_dept a,
             b_cp01 b
        where t.pj0101 = a.pj0101
          and t.cp0101 = b.cp0101
          and a.dept_id = #{deptId}
          and t.pj0101 = #{pj0101}
    </select>

    <select id="getTeamPageList" resultType="io.renren.modules.enterprise.pj01info.dto.Tm01PageDTO">
        select a.name projectName,
               t.responsiblepersonname,
               t.tm0101,
               t.cp0201,
               t.pj0101,
               t.teamname,
               t.entrytime,
               t.exittime,
               t.IN_OR_OUT
        from b_tm01 t,
             R_PJ01_DEPT r,
             b_pj01 a
        where t.pj0101 = r.pj0101
          and t.pj0101 = a.pj0101
          and r.DEPT_ID = #{deptId}
          and a.pj0101 = #{pj0101}
    </select>

    <select id="getWorkerPageList" resultType="io.renren.modules.enterprise.pj01info.dto.Ps02PageDTO">
        select t.ps0201,
               (select c.name
                from b_pj01 c
                where c.pj0101 = t.pj0101)                    projectName,
               t.TM0101,
               (select h.corpname
                from b_cp01 h
                where h.cp0101 = (select f.cp0101
                                  from b_tm01 e,
                                       b_cp02 f
                                  where e.cp0201 = f.cp0201
                                    and e.tm0101 = t.tm0101)) corpName,
               a.name                                         personName,
               a.idcardnumber                                 idCardNumber,
               t.isteamleader                                 isTeamLeader,
               a.cellphone                                    cellPhone,
               t.worktypecode                                 workTypeCode,
               t.in_or_out                                    inOrOut,
               -- 新增
               (
                   SELECT CASE
                              WHEN COUNT(*) > 0 THEN
                                  1
                              ELSE
                                  0
                              END
                   FROM b_ot01 c
                   WHERE c.busisysno = t.ps0201
                     and c.busitype = '13'
                     and c.whether = '1'
               ) as isUploadWorkerFile
        from b_ps02 t,
             b_PS01 a,
             R_PJ01_DEPT b,
             b_tm01 c
        where t.ps0101 = a.ps0101
          and t.PJ0101 = b.PJ0101
          and b.DEPT_ID = #{deptId}
          and t.tm0101 = c.tm0101
          and t.pj0101 = #{pj0101}
    </select>

    <select id="getAttendancePageList" resultType="io.renren.modules.enterprise.pj01info.dto.Kq02PageDTO">
        select a.KQ0201,
               c.NAME        projectName,
               a.USER_ID     userId,
               a.PJ0101,
               a.PERSON_NAME personName,
               a.PERSON_TYPE personType,
               a.DIRECTION,
               a.ATTENDTYPE  attendType,
               a.CHECKDATE   checkDate,
               a.IMAGE_URL   imageUrl
        from B_KQ02 a,
             R_PJ01_DEPT b,
             b_pj01 c
        where a.PJ0101 = b.PJ0101
          and a.PJ0101 = c.PJ0101
          and b.DEPT_ID = #{deptId}
          and c.pj0101 = #{pj0101}
    </select>

    <select id="getManagerPageList" resultType="io.renren.modules.enterprise.pj01info.dto.Ps04PageDTO">
<!--        select t.PS0101,-->
<!--               t.PS0401,-->
<!--               a.NAME,-->
<!--               a.idcardnumber,-->
<!--               a.cellphone,-->
<!--               (select d.NAME from B_PJ01 d where d.PJ0101 = t.PJ0101) projectName,-->
<!--               t.CP0201,-->
<!--               a.GENDER,-->
<!--               t.JOBTYPE                                               jobtype,-->
<!--               t.MANAGETYPE                                            manageType-->
<!--        from B_PS04 t,-->
<!--             b_ps01 a,-->
<!--             R_PJ01_DEPT c-->
<!--        where t.PJ0101 = c.PJ0101-->
<!--          and t.PS0101 = a.PS0101-->
<!--          and c.DEPT_ID = #{deptId}-->
<!--          and t.pj0101 = #{pj0101}-->
        SELECT a.ps0101,
        p.ps0401,
        a.name,
        a.idcardnumber,
        a.cellphone,
        j.name         projectName,
        p.cp0201,
        a.GENDER,
        p.JOBTYPE      jobtype,
        p.pj0101,
        p.ENTRYTIME,
        p.EXITTIME,
        p.IN_OR_OUT,
        -- 新增
        (
        SELECT CASE
        WHEN COUNT(*) > 0 THEN
        1
        ELSE
        0
        END
        FROM b_ot01 c
        WHERE c.busisysno = p.ps0401
        and c.busitype = '54'
        and c.whether = '1'
        ) as isUploadWorkerFile
        FROM b_ps01 a, b_ps03 t, b_ps04 p, B_PJ01 j, R_PJ01_DEPT c
        where t.ps0301 = p.ps0301
        and a.ps0101 = t.ps0101
        and j.pj0101 = p.pj0101
        and p.in_or_out = '1'
        and j.pj0101 = c.pj0101
        and c.dept_id =  #{deptId}
    </select>
</mapper>