<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.excelexp.dao.ExcelExpDao">

    <resultMap type="io.renren.modules.enterprise.excelexp.dto.PayrollRegisterDTO" id="payrollregisterMap">
        <result property="name" column="NAME"/>
        <result property="gender" column="GENDER"/>
        <result property="idcardnumber" column="IDCARDNUMBER"/>
        <result property="cellphone" column="CELLPHONE"/>
        <result property="payrollbankcardnumber" column="PAYROLLBANKCARDNUMBER"/>
        <result property="count" column="COUNT"/>
    </resultMap>

    <select id="getPayrollRegisterDTOList"
            resultType="io.renren.modules.enterprise.excelexp.dto.PayrollRegisterDTO">
        <choose>
            <when test="personType != null and personType != ''and personType == 1">
                SELECT
                (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = t.pj0101 and m.tm0101 = s2.tm0101) teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                s2.payrollbankcardnumber,
                s3.unitprice,
                (SELECT nvl(COUNT(distinct to_char(q.checkdate,'yyyymmdd')),0) FROM b_kq02 q
                where q.user_id = s2.ps0201
                and q.pj0101 = t.pj0101
                and to_char(q.checkdate,'yyyymm')= #{month} ) count
                FROM r_pj01_dept t,b_ps02 s2,b_ps01 s1,b_ps03 s3
                where t.pj0101 = t.pj0101
                and s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and s2.ps0201 = s3.ps0201
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                and t.dept_id = #{deptId}
            </when>
            <when test="personType != null and personType != ''and personType == 2">
                SELECT
                '管理人员' teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                '' payrollbankcardnumber,
                null unitprice,
                (SELECT nvl(COUNT(distinct to_char(q.checkdate,'yyyymmdd')),0) FROM b_kq02 q
                where q.user_id = s2.ps0401
                and q.pj0101 = t.pj0101
                and to_char(q.checkdate,'yyyymm')=  #{month} ) count
                FROM r_pj01_dept t,b_ps04 s2,b_ps01 s1
                where t.pj0101 = t.pj0101
                and s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                and t.dept_id = #{deptId}
            </when>
            <otherwise>
                (SELECT
                (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = t.pj0101 and m.tm0101 = s2.tm0101) teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                s2.payrollbankcardnumber,
                s3.unitprice,
                (SELECT nvl(COUNT(distinct to_char(q.checkdate,'yyyymmdd')),0) FROM b_kq02 q
                where q.user_id = s2.ps0201
                and q.pj0101 = t.pj0101
                and to_char(q.checkdate,'yyyymm')= #{month} ) count
                FROM r_pj01_dept t,b_ps02 s2,b_ps01 s1,b_ps03 s3
                where t.pj0101 = t.pj0101
                and s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and s2.ps0201 = s3.ps0201
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                and t.dept_id = #{deptId}) union all
                (  SELECT
                '管理人员' teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                '' payrollbankcardnumber,
                null unitprice,
                (SELECT nvl(COUNT(distinct to_char(q.checkdate,'yyyymmdd')),0) FROM b_kq02 q
                where q.user_id = s2.ps0401
                and q.pj0101 = t.pj0101
                and to_char(q.checkdate,'yyyymm')=  #{month} ) count
                FROM r_pj01_dept t,b_ps04 s2,b_ps01 s1
                where t.pj0101 = t.pj0101
                and s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                and t.dept_id = #{deptId})
            </otherwise>
        </choose>
    </select>

    <select id="getWorkerRecordDTOList" resultType="io.renren.modules.enterprise.excelexp.dto.WorkerRecordDTO">

        <choose>
            <when test="personType != null and personType != ''and personType == 1">
                select
                (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = t.pj0101 and m.tm0101 = s2.tm0101) teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                s3.unitprice,
                (SELECT a.dict_label FROM sys_dict_data a ,sys_dict_type c where a.dict_type_id = c.id
                and c.dict_type = 'WORKTYPECODE' and a.dict_value = s2.worktypecode) worktypecode,
                to_char(s2.entrytime,'yyyy-MM-dd') starttime,
                to_char(s2.exittime,'yyyy-MM-dd') endtime
                FROM r_pj01_dept t,b_ps02 s2,b_ps01 s1,b_ps03 s3
                where  s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and  s2.ps0201 = s3.ps0201
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                and t.dept_id = #{deptId}
            </when>
            <when test="personType != null and personType != ''and personType == 2">
                select
                '管理人员' teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                null unitprice,
                (SELECT a.dict_label FROM sys_dict_data a ,sys_dict_type c where a.dict_type_id = c.id
                and c.dict_type = 'MANAGETYPE' and a.dict_value = s2.managetype) worktypecode,
                to_char(s2.entrytime,'yyyy-MM-dd') starttime,
                to_char(s2.exittime,'yyyy-MM-dd') endtime
                FROM r_pj01_dept t,b_ps04 s2,b_ps01 s1
                where s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                and t.dept_id = #{deptId}
            </when>
            <otherwise>
                ( select
                (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = t.pj0101 and m.tm0101 = s2.tm0101) teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                s3.unitprice,
                (SELECT a.dict_label FROM sys_dict_data a ,sys_dict_type c where a.dict_type_id = c.id
                and c.dict_type = 'WORKTYPECODE' and a.dict_value = s2.worktypecode) worktypecode,
                to_char(s2.entrytime,'yyyy-MM-dd') starttime,
                to_char(s2.exittime,'yyyy-MM-dd') endtime
                FROM r_pj01_dept t,b_ps02 s2,b_ps01 s1,b_ps03 s3
                where  s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and  s2.ps0201 = s3.ps0201
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                and t.dept_id = #{deptId}) union all (
                select
                '管理人员' teamname,
                s1.name,
                decode(S1.Gender,'0','女','男') gender,
                s1.idcardnumber,
                s1.cellphone,
                null unitprice,
                (SELECT a.dict_label FROM sys_dict_data a ,sys_dict_type c where a.dict_type_id = c.id
                and c.dict_type = 'MANAGETYPE' and a.dict_value = s2.managetype) worktypecode,
                to_char(s2.entrytime,'yyyy-MM-dd') starttime,
                to_char(s2.exittime,'yyyy-MM-dd') endtime
                FROM r_pj01_dept t,b_ps04 s2,b_ps01 s1
                where s2.pj0101 = t.pj0101
                and s1.ps0101 = s2.ps0101
                and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}
                and t.dept_id = #{deptId})
            </otherwise>
        </choose>
    </select>

    <select id="getAttendanceRecordDTOList" resultType="io.renren.modules.enterprise.excelexp.dto.AttendanceRecordDTO">
        <if test="personType == 1 ">
            select a.teamname,
            (select x.name from b_ps01 x where x.ps0101=t.ps0101) as name,
            (select x.idcardnumber from b_ps01 x where x.ps0101=t.ps0101) as idcardnumber,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '01') as day1,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '02') as day2,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '03') as day3,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '04') as day4,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '05') as day5,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '06') as day6,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '07') as day7,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '08') as day8,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '09') as day9,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '10') as day10,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '11') as day11,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '12') as day12,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '13') as day13,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '14') as day14,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '15') as day15,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '16') as day16,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '17') as day17,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '18') as day18,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '19') as day19,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '20') as day20,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '21') as day21,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '22') as day22,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '23') as day23,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '24') as day24,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '25') as day25,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '26') as day26,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '27') as day27,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '28') as day28,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '29') as day29,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '30') as day30,
            FUN_ANALYSIS_KQHOURS(t.ps0201, '1', #{month} || '31') as day31,
            FUN_ANALYSIS_SUMKQHOURS(t.ps0201, '1', #{month}) as counts
            from b_ps02 t,b_tm01 a
            where t.pj0101 = #{pj0101}
            and t.tm0101 = a.tm0101
            <if test="tm0101 != null and tm0101 != ''">
                and a.tm0101 = #{tm0101}
            </if>
            <if test="inOrOut != null and inOrOut != ''">
                and t.in_or_out = #{inOrOut}
            </if>
        </if>
        <if test="personType == 2 ">
            select '管理人员' as teamname,
            (select x.name from b_ps01 x where x.ps0101=t.ps0101) as name,
            (select x.idcardnumber from b_ps01 x where x.ps0101=t.ps0101) as idcardnumber,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '01') as day1,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '02') as day2,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '03') as day3,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '04') as day4,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '05') as day5,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '06') as day6,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '07') as day7,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '08') as day8,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '09') as day9,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '10') as day10,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '11') as day11,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '12') as day12,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '13') as day13,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '14') as day14,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '15') as day15,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '16') as day16,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '17') as day17,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '18') as day18,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '19') as day19,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '20') as day20,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '21') as day21,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '22') as day22,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '23') as day23,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '24') as day24,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '25') as day25,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '26') as day26,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '27') as day27,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '28') as day28,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '29') as day29,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '30') as day30,
            FUN_ANALYSIS_KQHOURS(t.ps0401, '1', #{month} || '31') as day31,
            FUN_ANALYSIS_SUMKQHOURS(t.ps0401, '1', #{month}) as counts
            from b_ps04 t
            where t.pj0101 = #{pj0101}
            <if test="inOrOut != null and inOrOut != ''">
                and t.in_or_out = #{inOrOut}
            </if>
        </if>
    </select>

</mapper>