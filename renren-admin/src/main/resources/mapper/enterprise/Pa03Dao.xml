<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa03.dao.Pa03Dao">
    <select id="pageList" resultType="io.renren.modules.enterprise.pa03.dto.Pa03DTO">
        select a.name as projectname,
            (select y.name
            from b_ps02 x, b_ps01 y
            where x.ps0101 = y.ps0101
            and x.ps0201 = t.ps0201) as name,
            t.*
        from B_PA03 t, b_pj01 a
        where t.pj0101 = a.pj0101
        and t.flag = '1'
        and t.pj0101 = #{pj0101}
        <if test="projectname !=null and projectname !=''">
            and b.NAME like '%' || #{projectname} || '%'
        </if>
        <if test="name !=null and name !=''">
            and d.name = #{name}
        </if>
        <if test="issuedate !=null and issuedate !=''">
            and to_char(a.issuedate,'yyyyMM') = #{issuedate}
        </if>
        <if test="pa0201 !=null and pa0201 !=''">
            and t.pa0201 = #{pa0201}
        </if>
        order by t.accountdate desc, t.rowid
    </select>
</mapper>