<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj04.dao.Pj04Dao">
    <select id="selectByPj0301" resultType="io.renren.modules.enterprise.pj04.dto.Pj04DTO">
        select *
        from b_pj04 t
        where t.PJ0301 = #{id}
    </select>

    <insert id="batchInsertPj04">
        insert into B_PJ04
        (
        pj0401,
        pj0301,
        ps0201
        )
        select a.* from (
        <foreach collection="list" item="item" separator="union all">
            select
            #{item.pj0401},
            #{item.pj0301},
            #{item.ps0201}
            from dual
        </foreach>
        ) a
    </insert>
</mapper>