<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps06.dao.BPs06Dao">

    <insert id="batchInsertInOrOut">

        insert into b_ps06
        (
        PS0601,
        PS0201,
        ENTRY_OR_EXIT_TIME,
        IN_OR_OUT,
        MEMO,
        CREATE_DATE,
        UPDATE_DATE
        )
        select a.* from(
        <foreach collection="list" item="item" separator="union all">
            select
            #{item.ps0601},
            #{item.ps0201},
            #{item.entryOrExitTime},
            #{item.inOrOut},
            #{item.memo},
            #{item.createDate},
            #{item.updateDate}
            from dual
        </foreach>
        ) a
    </insert>
</mapper>