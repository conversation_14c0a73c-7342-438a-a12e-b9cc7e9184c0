<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj13.dao.Pj13Dao">

    <resultMap type="io.renren.modules.enterprise.pj13.entity.Pj13Entity" id="pj13Map">
        <result property="id" column="ID"/>
        <result property="exitId" column="EXIT_ID"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditor" column="AUDITOR"/>
        <result property="auditdate" column="AUDITDATE"/>
        <result property="auditreason" column="AUDITREASON"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="exitType" column="EXIT_TYPE"/>
    </resultMap>

    <select id="ps02ExitPage" resultType = "io.renren.modules.enterprise.pj13.dto.Ps02ExitPageDTO">
        select t2.name,
        t1.ps0201,
        t2.idcardnumber,
        t2.gender,
        t2.cellphone,
        t1.entrytime,
        a.auditstatus,
        a.auditreason,
        (select teamname from b_tm01 where tm0101 = t1.tm0101) teamName
        from b_ps02 t1
        inner join b_ps01 t2
        on t1.ps0101 = t2.ps0101
        left join (select exit_id,
        entrytime,
        auditstatus,
        auditreason,
        ROW_NUMBER() OVER(PARTITION BY exit_id ORDER BY create_date DESC) AS rn
        FROM b_pj13) a
        ON a.exit_id = t1.ps0201
        AND a.entrytime = t1.entrytime
        AND a.rn = 1
        where t1.in_or_out = '1'
        and t1.pj0101 = #{params.pj0101}
        <if test="params.name != null and params.name != ''">
            and t2.name like '%' || #{params.name} || '%'
        </if>
        <if test="params.auditstatus != null and params.auditstatus != ''">
            and auditstatus = #{params.auditstatus}
        </if>
    </select>

    <select id="ps04ExitPage" resultType = "io.renren.modules.enterprise.pj13.dto.Ps04ExitPageDTO">
        select t3.name,
        t3.idcardnumber,
        t3.gender,
        t3.cellphone,
        t1.entrytime,
        t1.ps0401,
        t1.jobtype jobType,
        a.auditstatus,
        a.auditreason,
        (select corpname from b_cp01 where cp0101 = t2.cp0101) corpName
        from b_ps04 t1
        inner join b_ps03 t2
        on t1.ps0301 = t2.ps0301
        inner join b_ps01 t3
        on t3.ps0101 = t2.ps0101
        left join (select exit_id,
        entrytime,
        auditstatus,
        auditreason,
        ROW_NUMBER() OVER(PARTITION BY exit_id ORDER BY create_date DESC) AS rn
        FROM b_pj13) a
        ON a.exit_id = t1.ps0401
        AND a.entrytime = t1.entrytime
        AND a.rn = 1
        where t1.in_or_out = '1'
        and t1.pj0101 = #{params.pj0101}
        <if test="params.name != null and params.name != ''">
            and t3.name like '%' || #{params.name} || '%'
        </if>
        <if test="params.auditstatus != null and params.auditstatus != ''">
            and auditstatus = #{params.auditstatus}
        </if>
    </select>

    <select id="tm01ExitPage" resultType = "io.renren.modules.enterprise.pj13.dto.Tm01ExitPageDTO">
        select t1.tm0101,
        t1.teamname,
        t1.responsiblepersonname,
        t1.responsiblepersonphone,
        t1.responsiblepersonidnumber,
        t1.entrytime,
        a.auditstatus,
        a.auditreason
        from b_tm01 t1
        left join (select exit_id,
        entrytime,
        auditstatus,
        auditreason,
        ROW_NUMBER() OVER(PARTITION BY exit_id ORDER BY create_date DESC) AS rn
        FROM b_pj13) a
        ON a.exit_id = t1.tm0101
        AND a.entrytime = t1.entrytime
        AND a.rn = 1
        where t1.in_or_out = '1'
        and t1.pj0101 = #{params.pj0101}
        <if test="params.name != null and params.name != ''">
            and t1.teamname like '%' || #{params.name} || '%'
        </if>
        <if test="params.auditstatus != null and params.auditstatus != ''">
            and auditstatus = #{params.auditstatus}
        </if>
    </select>
</mapper>