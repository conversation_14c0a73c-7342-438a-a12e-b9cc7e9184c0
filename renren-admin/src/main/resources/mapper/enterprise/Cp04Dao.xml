<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cp04.dao.Cp04Dao">

    <resultMap type="io.renren.modules.enterprise.cp04.entity.Cp04Entity" id="cp04Map">
        <result property="cp0401" column="CP0401"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="subcontractname" column="SUBCONTRACTNAME"/>
        <result property="subcontractcontent" column="SUBCONTRACTCONTENT"/>
        <result property="startdate" column="STARTDATE"/>
        <result property="completedate" column="COMPLETEDATE"/>
        <result property="subcontractprice" column="SUBCONTRACTPRICE"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPageList" resultType="io.renren.modules.enterprise.cp04.dto.Cp04PageDTO">

        SELECT b.cp0401,
               j.name pjname,
               c.cp0201,
               p.corpname cpname,
               b.subcontractname,
               b.subcontractcontent,
               b.startdate,
               b.completedate,
               b.subcontractprice,
               b.memo
        FROM b_cp04 b, b_pj01 j, b_cp02 c, b_cp01 p, r_pj01_dept d
        where p.cp0101 = c.cp0101
          and b.cp0201 = c.cp0201
          and c.pj0101 = j.pj0101
          and b.pj0101 = j.pj0101
          and d.pj0101 = j.pj0101
          and d.dept_id = #{deptId}
        <if test="cpname != null and cpname.trim() != ''" >
            and p.corpname like '%'||#{cpname}||'%'
        </if>

    </select>

    <select id="getCompanyList" resultType="io.renren.common.common.dto.CommonDto">
        select t.cp0201 value,
               b.corpname label
        from b_cp02 t,
             r_pj01_dept a,
             b_cp01 b
        where t.pj0101 = a.pj0101
          and t.cp0101 = b.cp0101
          and t.corptype in ('1', '2', '3', '6')
          and a.dept_id = #{deptId}

    </select>
</mapper>