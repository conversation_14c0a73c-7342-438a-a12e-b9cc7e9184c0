<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.bankjoint.dao.BankJointDao">

    <select id="getList" resultType="io.renren.modules.enterprise.bankjoint.dto.BankJointDTO">
        select h.*
          from (select t.*,
                        (select x.dict_label
                        from sys_dict_data x, sys_dict_type y
                        where x.dict_type_id = y.id
                        and y.dict_type = 'BANKJOINTSTATUS'
                        and x.dict_value = t.bankjointstatus) as bankjointstatus_dict_label,
                       (select count(1)
                          from b_pa01 a, b_pj01 b,sys_dept c
                         where a.pj0101 = b.pj0101
                           and b.dept_id = c.id
                           and c.areacode like #{areacode}||'%'
                           and a.pay_bank_code = t.pay_bank_code
                           and b.prjstatus in ('3', '5')) as projects
                  from I_BANK_JOINT t) h
                  where 1 = 1
            <if test="payBankCode != null and payBankCode != ''">
                and h.pay_bank_code = #{payBankCode}
            </if>
            <if test="bankjointstatus != null and bankjointstatus != ''">
                and h.bankjointstatus = #{bankjointstatus}
            </if>
         order by h.bankjointstatus desc, h.projects desc
    </select>
    <select id="getJointBanks" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select t.pay_bank_code as value, t.name as label
          from I_BANK_JOINT t
         order by t.bankjointstatus desc, t.id
    </select>
</mapper>