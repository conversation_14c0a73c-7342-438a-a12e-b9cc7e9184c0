<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq05.dao.Kq05Dao">

<select id="getKq05Data" resultType="io.renren.modules.enterprise.kq05.dto.Kq05ListDTO">
                    SELECT * FROM (
                        SELECT
                                s1.name username,
                                s2.ps0201 userId,
                                p.name projectname,
                                s1.idcardnumber,
                                '1' PERSON_TYPE,
                                s1.cellphone,
                                d.DEPT_ID
                            FROM
                                b_ps02 s2,
                                b_ps01 s1,
                                b_pj01 p,
                                R_PJ01_DEPT d
                            WHERE
                                s1.PS0101 = s2.PS0101
                                AND s2.PJ0101 = p.PJ0101
                                AND p.PJ0101 = d.PJ0101
                                AND s2.pj0101 = d.pj0101
                                UNION ALL
                                SELECT
                                s1.name username,
                                s2.ps0401 userId,
                                p.name projectname,
                                s1.idcardnumber,
                                '2' PERSON_TYPE,
                                s1.cellphone,
                                d.DEPT_ID
                            FROM
                                b_ps04 s2,
                                b_ps01 s1,
                                b_pj01 p,
                                R_PJ01_DEPT d
                            WHERE
                                s1.PS0101 = s2.PS0101
                                AND s2.PJ0101 = p.PJ0101
                                AND p.PJ0101 = d.PJ0101
                                AND s2.pj0101 = d.pj0101
                            ) temp
                            WHERE
                                temp.DEPT_ID = #{deptId}
                    <if test="projectname != null and projectname != ''" >
                        and temp.PROJECTNAME like '%' || #{projectname} || '%'
                    </if>
                    <if test="username != null and username != ''" >
                       and temp.USERNAME like '%' || #{username} || '%'
                    </if>
</select>
    <select id="getKq05Detail" resultType="io.renren.modules.enterprise.kq05.dto.Kq05DTO">
        SELECT * FROM B_KQ05 k WHERE k.USER_ID = #{userId} order by k.CREATE_DATE DESC
    </select>
</mapper>