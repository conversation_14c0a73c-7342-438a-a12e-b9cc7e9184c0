<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.archives.dao.ArchiveDao">
    <select id="selectPersonPageList" resultType="io.renren.modules.enterprise.archives.dto.PersonPage">
        select *
        from (SELECT a.PS0201       AS personId,
                     b.name         AS personName,
                     b.idcardnumber AS idCardNumber,
                     c.teamname     AS teamName,
                     a.worktypecode AS workTypeCode,
                     a.ISTEAMLEADER AS isTeamLeader,
                     a.in_or_out    AS inOrOut,
                     CASE
                         WHEN EXISTS (SELECT count(1)
                                      FROM b_ot01 d
                                      WHERE d.BUSISYSNO = a.PS0201
                                        AND d.WHETHER = '1'
                                        AND d.BUSITYPE = #{fileType}) THEN '1'
                         ELSE '0'
                         END        AS whether
              FROM b_ps02 a
                       INNER JOIN b_ps01 b ON a.ps0101 = b.ps0101
                       INNER JOIN b_tm01 c ON a.tm0101 = c.tm0101
              WHERE a.PJ0101 = #{pj0101}) t
        <where>
            <if test="personName != null and personName != ''">
                t.personName LIKE '%' || #{personName} || '%'
            </if>
            <if test="whether != null and whether != ''">
                AND t.whether = #{whether}
            </if>
        </where>
        ORDER BY whether DESC
    </select>
    <select id="selectPartUnitPageList" resultType="io.renren.modules.enterprise.archives.dto.PartUnitPage">
        select *
        from (select a.CP0201                                         as partUnitId,
                     b.CORPNAME                                       as partUnitName,
                     a.CORPTYPE                                       as partUnitType,
                     a.IN_OR_OUT                                      as inOrOut,
                     CASE WHEN c1.fileCount > 0 THEN '1' ELSE '0' END as whether
              from b_cp02 a
                       inner join B_CP01 b on a.CP0101 = b.CP0101 and a.PJ0101 = #{pj0101}
                       left join (select c.BUSISYSNO, count(1) as fileCount
                                  from b_ot01 c
                                  where c.BUSITYPE = #{fileType}
                                    and c.WHETHER = '1'
                                  group by c.BUSISYSNO) c1 on a.CP0201 = c1.BUSISYSNO
        where a.CORPTYPE in
        <foreach item="item" collection="corpType" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        ) a1
        <where>
            <if test="whether != null and whether != ''">
                a1.whether = #{whether}
            </if>
        </where>
        order by a1.whether
    </select>
    <select id="selectTeamPageList" resultType="io.renren.modules.enterprise.archives.dto.TeamPage">
        select *
        from (select a.TM0101                                         as teamId,
                     a.TEAMNAME                                       as teamName,
                     a.RESPONSIBLEPERSONNAME                          as responsibleName,
                     a.RESPONSIBLEPERSONPHONE                         as phone,
                     a.IN_OR_OUT                                      as inOrOut,
                     CASE WHEN b1.fileCount > 0 THEN '1' ELSE '0' END as whether
              from b_tm01 a
                       left join (select b.BUSISYSNO, count(1) as fileCount
                                  from b_ot01 b
                                  where b.WHETHER = '1'
                                    and b.BUSITYPE = #{fileType}
                                  group by b.BUSISYSNO) b1 on a.TM0101 = b1.BUSISYSNO
              where a.PJ0101 = #{pj0101}) a1
        <where>
            <if test="whether != null and whether != ''">
                a1.whether = #{whether}
            </if>
        </where>
        order by a1.whether, a1.teamName
    </select>
</mapper>