<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa04.dao.Pa04Dao">

<select id="getProjectCorp" resultType="io.renren.modules.enterprise.cp02.dto.Cp02PageDTO">

    select c.cp0101,
           p.cp0201,
           p.pj0101,
           (select name from b_pj01 j where j.pj0101 = p.pj0101) name,
           c.corpname,
           c.corpcode,
           c.linkman,
           c.linkcellphone,
           p.corptype
    from b_cp01 c, b_cp02 p
    where c.cp0101 = p.cp0101
      and p.corptype = '8'
      and p.pj0101 = #{pj0101}

    </select>

<select id="getProjectCorpDetail" resultType="io.renren.modules.enterprise.pa04.dto.Pa04DTO">

    select * from b_pa04 t where t.cp0201 = #{cp0201} and t.pj0101 = #{pj0101}

    </select>

<select id="getPageList" resultType="io.renren.modules.enterprise.pa04.dto.Pa04PageDTO">
    select t.pa0401, (
            select c.corpname from b_cp01 c, b_cp02 p where c.cp0101 = p.cp0101 and p.cp0201 = t.cp0201) corpname, t.COMACCOUNT, t.COMACCOUNTNAME, t.BANK_NAME
    from b_pa04 t
    where t.pj0101 = #{pj0101}
    <if test="comaccountname != null and comaccountname != ''" >
       and t.comaccountname like '%' || #{comaccountname} || '%'
    </if>
    <if test="bankname != null and bankname != ''" >
       and t.bank_name like '%' || #{bankname} || '%'
    </if>
</select>

<select id="getProjectCp0201" resultType="java.lang.Long">
    select p.cp0201
    from b_cp01 c, b_cp02 p
    where c.cp0101 = p.cp0101
      and p.corptype = '8'
      and p.pj0101 = #{pj0101}
    </select>

<select id="detail" resultType="io.renren.modules.enterprise.pa04.dto.Pa04PageDTO">

    select t.pa0401, (
            select c.corpname from b_cp01 c, b_cp02 p where c.cp0101 = p.cp0101 and p.cp0201 = t.cp0201) corpname, t.COMACCOUNT, t.COMACCOUNTNAME, t.BANK_NAME
    from b_pa04 t where t.pa0401 = #{pa0401}

    </select>
</mapper>