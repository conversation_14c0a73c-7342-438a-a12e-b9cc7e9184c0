<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cg07.dao.Cg07Dao">
    <delete id="deleteByPj0101">
        delete from b_cg07 where cg0601 in (select cg0601 from  b_cg06 where pj0101 = #{pj0101})
    </delete>

    <select id="getTree" resultType="io.renren.modules.enterprise.cg06.dto.TransferDTO">
        select a.CG0501 as key, b.dict_label as label
        from b_cg05 a,
            (select dict_label, dict_value
            from sys_dict_data
            where DICT_TYPE_ID = '1265521283182740000') b
        where a.cg0401 = #{cg0401}
          and a.cg0502 = b.dict_value
          and a.cg0503 = '1'
    </select>

</mapper>