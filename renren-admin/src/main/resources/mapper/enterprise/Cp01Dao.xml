<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cp01.dao.Cp01Dao">
    <!--  根据统一社会信用代码查询企业信息  -->
    <select id="loadCp01" resultType="io.renren.modules.enterprise.cp01.dto.Cp01DTO">
        select *
        from B_CP01 t
        where t.CORPCODE = #{corpCode}
    </select>
    <select id="getList" resultType="io.renren.modules.enterprise.cp01.dto.Cp01DTO">
        select CP0101, CORPCODE, CORPNAME, ADDRESS, LEGALMAN, REGCAPITAL, BUSINESSSTATUS
        from B_CP01
                where 1 = 1
        <if test="params.corpname != null and params.corpname.trim() != ''">
            and CORPNAME like '%' || #{params.corpname} || '%'
        </if>
        <if test="params.corpcode != null and params.corpcode.trim() != ''">
            and CORPCODE like '%' || #{params.corpcode} || '%'
        </if>
    </select>

    <select id="getCompanyList" resultType="io.renren.common.common.dto.CommonDto">

        SELECT t.cp0101 value, t.corpname label FROM b_cp01 t

    </select>
    <select id="getRegisterCompanyList" resultType="io.renren.common.common.dto.CommonDto">
        SELECT t.cp0101 value, t.corpname label
          from b_cp01 t, sys_user a
         where t.dept_id = a.dept_id
    </select>
    <select id="querySearch" resultType="io.renren.common.common.dto.CommonDto">
        SELECT t.cp0101 label, t.corpname value FROM b_cp01 t
    </select>
</mapper>