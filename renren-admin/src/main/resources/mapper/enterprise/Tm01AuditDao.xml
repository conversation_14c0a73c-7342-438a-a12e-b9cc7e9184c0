<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.tm01audit.dao.Tm01AuditDao">

    <resultMap type="io.renren.modules.enterprise.tm01audit.entity.Tm01AuditEntity" id="tm01AuditMap">
        <result property="tm0101" column="TM0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="teamsysno" column="TEAMSYSNO"/>
        <result property="teamname" column="TEAMNAME"/>
        <result property="responsiblepersonname" column="RESPONSIBLEPERSONNAME"/>
        <result property="responsiblepersonphone" column="RESPONSIBLEPERSONPHONE"/>
        <result property="idcardtype" column="IDCARDTYPE"/>
        <result property="responsiblepersonidnumber" column="RESPONSIBLEPERSONIDNUMBER"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="memo" column="MEMO"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditor" column="AUDITOR"/>
        <result property="auditdate" column="AUDITDATE"/>
        <result property="auditresult" column="AUDITRESULT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPageList" resultType="io.renren.modules.enterprise.tm01audit.dto.Tm01PageAuditDTO">
        select a.name projectName,
               t.responsiblepersonname,
               t.tm0101,
               t.cp0201,
               t.pj0101,
               t.teamname,
               t.entrytime,
               t.exittime,
               t.in_or_out,
               t.auditstatus,
               t.AUDITDATE,
               t.auditor
        from b_tm01_audit t, R_PJ01_DEPT r, b_pj01 a
        where t.pj0101 = r.pj0101
          and t.pj0101 = a.pj0101
          and r.DEPT_ID = #{deptId}
        <if test="teamname != null and teamname != ''">
            and t.TEAMNAME like '%' || #{teamname} || '%'
        </if>
        <if test="auditstatus != null and auditstatus.trim() != ''" >
            and t.AUDITSTATUS = #{auditstatus}
        </if>
        order by t.auditstatus,t.create_date desc
    </select>

    <select id="getInfo" resultType="io.renren.modules.enterprise.tm01audit.dto.Tm01DetailAuditDTO">

        SELECT t.*, p.name pjname
        FROM b_tm01_audit t, b_pj01 p
        where t.pj0101 = p.pj0101
          and t.tm0101 = #{id}

    </select>
</mapper>