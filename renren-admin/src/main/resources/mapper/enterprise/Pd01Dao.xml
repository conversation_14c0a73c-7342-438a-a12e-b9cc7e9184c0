<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pd01.dao.Pd01Dao">

    <resultMap type="io.renren.modules.enterprise.pd01.entity.Pd01Entity" id="pd01Map">
        <result property="pd0101" column="PD0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="tagno" column="TAGNO"/>
        <result property="model" column="MODEL"/>
        <result property="devicename" column="DEVICENAME"/>
        <result property="trademark" column="TRADEMARK"/>
        <result property="devicetype" column="DEVICETYPE"/>
        <result property="dischargestage" column="DISCHARGESTAGE"/>
        <result property="manufacturer" column="MANUFACTURER"/>
        <result property="enginetype" column="ENGINETYPE"/>
        <result property="factorymonthly" column="FACTORYMONTHLY"/>
        <result property="propertyunit" column="PROPERTYUNIT"/>
        <result property="creditcode" column="CREDITCODE"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="getList" resultType="io.renren.modules.enterprise.pd01.dto.Pd01DTO">
        select t.PD0101,t.TAGNO,t.MODEL,t.DEVICENAME,t.DEVICETYPE,
        t.DISCHARGESTAGE,t.MANUFACTURER,t.PROPERTYUNIT
        from B_PD01 t,R_PJ01_DEPT a
        where t.PJ0101=a.PJ0101 and a.DEPT_ID=#{deptId}
        <if test="devicename != null and devicename.trim() != ''">
            and DEVICENAME like #{devicename}
        </if>
    </select>
    <select id="loadEquipmentInfo" resultType="io.renren.common.common.dto.CommonDto">
        select t.PD0101 value, t.DEVICENAME label
        from B_PD01 t,
             R_PJ01_DEPT a
        where t.PJ0101 = a.PJ0101
          and a.DEPT_ID = #{deptId}
    </select>

    <update id="refreshDoReduceBankSuccessNum">
        update b_pd01 t
        set t.UPLOAD_EXCEPTION_NUM =
                (select count(*)
                 from b_pd02 t
                 where t.pd0101 = #{pd0101}
                   and t.DATA_FLAG = '0')
        where t.PD0101 = #{pd0101}
    </update>

    <select id="getPayBankList" resultType="io.renren.common.common.dto.CommonPayBankDTO">
        select g.dict_label as label , g.DICT_VALUE as code
        from sys_dict_type f, sys_dict_data g
        where f.dict_type = 'PAY_BANK_CODE'
          and f.id = g.dict_type_id
          and g.dict_label in
        <foreach collection="list" item="bankname" index="index"
                 open="(" close=")" separator=",">
            #{bankname}
        </foreach>
    </select>

    <select id="pageList" resultType="io.renren.modules.enterprise.pd01.dto.Pd01DTO">

        select t.pd0101,
               t.upload_type,
               t.upload_time,
               t.upload_success_num,
               t.upload_exception_num,
               t.is_import_data,
               t.total_data_num,
               t.pj0101,
               t.create_date
        from B_PD01 t
            where
                t.pj0101 = #{pj0101}
                <if test="businessType != null and businessType != ''">
                   and t.upload_type = #{businessType}
                </if>
                <if test="isImportData != null and isImportData != ''">
                    and t.is_import_data = #{isImportData}
                </if>
        order by t.pd0101 desc
    </select>

    <select id="getImportDataPersonList" resultType="io.renren.modules.enterprise.pd01.dto.Pd01ImportDataPersonDTO">
        select b.name, b.idcardnumber
        from b_ps01 b, b_ps02 t
        where b.ps0101 = t.ps0101
        and t.pj0101 = #{pj0101}
        <if test="tm0101 != null and tm0101!= ''" >
            and t.tm0101 = #{tm0101}
        </if>
        <if test="inOrOut != null and inOrOut != ''" >
            and t.in_or_out = #{inOrOut}
        </if>
        <if test='banknumberisnull != null and banknumberisnull != "" and "1" == banknumberisnull' >
            and (t.payrollbankcardnumber is null or t.payrolltopbankcode is null or t.payrollbankname is null)
        </if>
        order by t.ps0201 desc
    </select>

    <select id="getImportWokerContactList" resultType="io.renren.modules.enterprise.pd01.dto.Pd01ImportDataPersonDTO">
        <choose>
            <when test='isExistsContact != null and isExistsContact != "" and "1" == isExistsContact'>
                select b.name,
                b.idcardnumber,
                t.ps0201,
                (select h.corpname
                from b_cp01 h
                where h.cp0101 = (select f.cp0101
                from b_tm01 e, b_cp02 f
                where e.cp0201 = f.cp0201
                and e.tm0101 = t.tm0101)) corpName,
                p.contractno,
                p.corpname,
                p.legalman,
                p.corpcode,
                p.corpaddress,
                p.linkcellphone,
                p.name,
                p.gender,
                p.birthday,
                p.idcardnumber,
                p.address,
                p.cellphone,
                p.contractbegin,
                p.prjname,
                p.projectname,
                p.worktypecode,
                p.duty,
                p.salarypaymentmethod,
                p.timewage,
                p.workload,
                p.workloadwage,
                p.otherwage,
                p.payday,
                p.workhoursystem,
                p.workhour,
                p.workday,
                p.workirregular,
                p.welfare,
                p.consignor,
                p.workeraddress,
                p.otherthings,
                p.signdate
                from b_ps01 b, b_ps02 t, b_tm01 m, b_ps08 p
                where b.ps0101 = t.ps0101
                and t.tm0101 = m.tm0101
                and t.ps0201 = p.ps0201
                and t.pj0101 = #{pj0101}
                <if test="tm0101 != null and tm0101 != ''" >
                    and t.tm0101 = #{tm0101}
                </if>
                <if test="inOrOut != null and inOrOut != ''" >
                    and t.in_or_out = #{inOrOut}
                </if>
            </when>
            <otherwise>
                select b.name,
                b.idcardnumber,
                (select y.dict_label
                from sys_dict_type x, sys_dict_data y
                where x.dict_type = 'WORKTYPECODE'
                and x.id = y.dict_type_id
                and y.dict_value = t.worktypecode) as worktypecode,
                (select h.corpname
                from b_cp01 h
                where h.cp0101 = (select f.cp0101
                from b_tm01 e, b_cp02 f
                where e.cp0201 = f.cp0201
                and e.tm0101 = t.tm0101)) corpName
                from b_ps01 b, b_ps02 t, b_tm01 m
                where b.ps0101 = t.ps0101
                and t.tm0101 = m.tm0101
                and t.pj0101 = #{pj0101}
                <if test="tm0101 != null and tm0101 != ''" >
                    and t.tm0101 = #{tm0101}
                </if>
                <if test="inOrOut != null and inOrOut != ''" >
                    and t.in_or_out = #{inOrOut}
                </if>
                and not exists (select 1 from b_ps08 a where a.ps0201 = t.ps0201)
                order by t.ps0201 desc
            </otherwise>
        </choose>
    </select>

    <update id="refreshDoReduceContactSuccessNum">
        update b_pd01 t
        set t.UPLOAD_EXCEPTION_NUM =
                (select count(*)
                 from b_pd03 t
                 where t.pd0101 = #{pd0101}
                   and t.DATA_FLAG = '0')
        where t.PD0101 = #{pd0101}
    </update>
</mapper>