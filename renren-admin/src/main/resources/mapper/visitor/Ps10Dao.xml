<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.suppliers.visitor.dao.Ps10Dao">
    <select id="pageList" resultType="io.renren.modules.suppliers.visitor.dto.VisitorDTO">
        select h.* from (select t.*,
            (select a.real_name from sys_user a where a.id = t.creator) as accreditor,
            (select count(1)
            from b_ps10_pj01 x
            where x.ps1001 = t.ps1001
            and x.pj0101 = #{pj0101}) as accreditstatus
            from B_PS10 t
            where t.dept_id = #{deptId}
            and t.visitortype = '2'
            <if test="name != null and name.trim() != ''">
                and t.name like '%'||#{name}||'%'
            </if>) h where 1 = 1
        <if test="accreditstatus == 1 and accreditstatus.trim() != ''">
            and h.accreditstatus>0
        </if>
        <if test="accreditstatus == 0 and accreditstatus.trim() != ''">
            and h.accreditstatus=0
        </if>
        order by h.create_date desc
    </select>
    <select id="selectAuthList" resultType="io.renren.modules.supdevicetask.dto.PersonDTO">
        select t.ps1001 as userId, t.name, t.headimageurl as imageUrl
          from B_PS10 t
         where t.ps1001 = #{ps1001}
    </select>
</mapper>