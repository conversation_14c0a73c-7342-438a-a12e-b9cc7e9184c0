<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.bank.pa03imp.dao.Pa03ImpDao">
    <select id="page" resultType="io.renren.modules.bank.pa03imp.dto.Pa03ImpDTO">
        select t.*
        from B_PA03_IMP t
        where t.impuserid = #{userId}
        <if test="banknumber != null and banknumber.trim() !=''">
            and t.banknumber like '%'||#{banknumber}||'%'
        </if>
        <if test="banknumbername != null and banknumbername.trim() !=''">
            and t.banknumbername like '%'||#{banknumbername}||'%'
        </if>
        <if test="startTime != null and startTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
        order by t.create_date desc
    </select>
</mapper>