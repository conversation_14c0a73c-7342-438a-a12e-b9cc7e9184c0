<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.bank.pa02imp.dao.Pa02ImpDao">
    <select id="page" resultType="io.renren.modules.bank.pa02imp.dto.Pa02ImpDTO">
        select t.*
        from B_PA02_IMP t
        where t.impuserid = #{userId}
        <if test="comaccount != null and comaccount.trim() !=''">
            and t.comaccount like '%'||#{comaccount}||'%'
        </if>
        <if test="comaccountname != null and comaccountname.trim() !=''">
            and t.comaccountname like '%'||#{comaccountname}||'%'
        </if>
        <if test="startTime != null and startTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
        order by t.create_date desc
    </select>
</mapper>