<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.suppliers.supplier.dao.SupplierDao">
    <select id="publicList" resultType="io.renren.modules.suppliers.supplier.dto.SupplierDTO">
        select t.id, t.name, t.linkman, t.linkman_phone
          from SUP_SUPPLIER t
         where t.ispublic = '1'
        <if test="name != null and name != ''">
            and t.name like '%'||#{name}||'%'
        </if>
        order by t.update_date
    </select>
</mapper>