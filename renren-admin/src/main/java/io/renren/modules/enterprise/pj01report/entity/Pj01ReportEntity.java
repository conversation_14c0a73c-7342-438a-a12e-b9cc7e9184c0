package io.renren.modules.enterprise.pj01report.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 项目上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PJ01_REPORT")
@Accessors(chain = true)
public class Pj01ReportEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long pj0101;
    /**
     * 所属银行
     */
    private String payBankCode;
    /**
     * 数据上报
     */
    private String reportType;
}