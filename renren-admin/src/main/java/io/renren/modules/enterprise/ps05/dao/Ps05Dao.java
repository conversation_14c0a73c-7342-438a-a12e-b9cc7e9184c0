package io.renren.modules.enterprise.ps05.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps05.entity.Ps05Entity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps05Dao extends BaseDao<Ps05Entity> {
    /**
     * 校验人员是否注册
     * @param personId 人员ID
     * @return Integer
     */
    Integer checkPerson(Long personId);
}