package io.renren.modules.enterprise.ps11.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 管理人员/工人预约进场
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-25
 */
@Data
@ApiModel(value = "管理人员/工人预约进场")
public class Ps11DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long ps1101;
    @ApiModelProperty(value = "项目Id")
    private Long pj0101;
    @ApiModelProperty(value = "基础人员信息")
    private Long ps0101;
    @ApiModelProperty(value = "班组（人员类型为工人才写入）")
    private Long tm0101;
    @ApiModelProperty(value = "参见单位（人员类型为管理人员才写入）")
    private Long cp0201;
    @ApiModelProperty(value = "$column.comments")
    private String worktypecode;
    @ApiModelProperty(value = "1工人 2管理人员")
    private Short persontype;
    @ApiModelProperty(value = "进场时间")
    private Date entrydate;
    @ApiModelProperty(value = "0未处理1已处理")
    private Short status;
    @ApiModelProperty(value = "岗位类型（类型为管理人员才写入）")
    private String jobtype;
}