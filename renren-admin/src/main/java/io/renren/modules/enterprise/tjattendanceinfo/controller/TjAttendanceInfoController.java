package io.renren.modules.enterprise.tjattendanceinfo.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceGenerateDataDTO;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoPageExportDTO;
import io.renren.modules.enterprise.tjattendanceinfo.service.TjAttendanceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 考勤统计表详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-05-14
 */
@RestController
@RequestMapping("enterprise/tjattendanceinfo")
@Api(tags="考勤统计表详情")
public class TjAttendanceInfoController {

    @Autowired
    private TjAttendanceInfoService tjAttendanceInfoService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
        @ApiImplicitParam(name = "tjMonth", value = "导出月份(yyyy-MM)", paramType = "query", required = true, dataType="String")
    })
    @RequiresPermissions("enterprise:tjattendanceinfo:page")
    public Result<PageData<TjAttendanceInfoPageExportDTO>> pageList(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<TjAttendanceInfoPageExportDTO> page = tjAttendanceInfoService.pageList(params);

        return new Result<PageData<TjAttendanceInfoPageExportDTO>>().ok(page);
    }

    @PostMapping("/generateTjAttendanceData")
    @ApiOperation("生成月份数据")
    @LogOperation("生成月份数据")
    @RequiresPermissions("enterprise:tjattendanceinfo:generateTjAttendanceData")
    public Result generateTjAttendanceData(@RequestBody TjAttendanceGenerateDataDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        tjAttendanceInfoService.generateTjAttendanceData(dto);

        return new Result();
    }

    @GetMapping("exportTjAttendanceInfo")
    @ApiOperation("导出")
    @LogOperation("导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "yyyy-MM", value = "tjMonth（导出月份）", paramType = "query", required = true, dataType="String") ,
    })
    @RequiresPermissions("enterprise:tjattendanceinfo:exportTjAttendanceInfo")
    public void exportTjAttendanceInfo(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        tjAttendanceInfoService.exportTjAttendanceInfo(params, response);
    }
}