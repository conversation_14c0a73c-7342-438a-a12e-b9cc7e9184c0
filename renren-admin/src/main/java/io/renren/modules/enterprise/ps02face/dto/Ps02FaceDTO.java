package io.renren.modules.enterprise.ps02face.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工人人脸库信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-20
 */
@Data
@ApiModel(value = "工人人脸库信息表")
public class Ps02FaceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "人员id")
    private Long ps0201;
    @ApiModelProperty(value = "人脸特征")
    private byte[] faceFeature;
    @ApiModelProperty(value = "$column.comments")
    private Date createTime;
    @ApiModelProperty(value = "$column.comments")
    private Date updateTime;

}