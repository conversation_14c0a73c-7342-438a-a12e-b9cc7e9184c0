package io.renren.modules.enterprise.tjattendanceinfo.annotation;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.ImageUtils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 注解处理器
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/5/15 15:24
 **/
public class VirtualProcessor {

    /**
     * 处理Bean对象中的指定字段<br>
     *
     * @param object Bean对象
     * @param clazz  对象类型
     * @return 处理之后的对象
     */
    public static <T> T annotationBean(T object, Class<T> clazz) {
        //判断是否有自定义注解的字段
        for (Field field : clazz.getDeclaredFields()) {
            //判断是否有指定的注解
            boolean busAttendanceFormatPresent = field.isAnnotationPresent(AttendanceFormat.class);
            if (busAttendanceFormatPresent) {
                String fieldName = field.getName();
                Object fieldValue = BeanUtil.getFieldValue(object, fieldName);
                //如果有值则不处理
                if (ObjectUtil.isNull(fieldValue)) {
                    AttendanceFormat attendanceFormat = field.getAnnotation(AttendanceFormat.class);
                    String fieldValueStr = fieldValue.toString();
                    String newData = fieldValueStr.replace(attendanceFormat.oldSymbol(), attendanceFormat.nowSymbol());
                    BeanUtil.setFieldValue(object, fieldName, newData);
                }
            }

        }
        // 没有找到注解字段,直接返回传入的对象
        return Convert.convert(clazz, object);
    }

    /**
     * 处理Bean对象中的指定字段<br>
     *
     * @param object Bean对象
     * @return 处理之后的对象
     */
    public static <T> T dealAnnotationBean(T object) {
        //判断是否有自定义注解的字段
        for (Field field : object.getClass().getDeclaredFields()) {
            //判断是否有指定的注解
            boolean busIdAttendanceFormatPresent = field.isAnnotationPresent(AttendanceFormat.class);

            if (busIdAttendanceFormatPresent) {
                String fieldName = field.getName();
                Object fieldValue = BeanUtil.getFieldValue(object, fieldName);
                //如果有值则不处理
                if (ObjectUtil.isNotEmpty(fieldValue)) {
                    AttendanceFormat attendanceFormat = field.getAnnotation(AttendanceFormat.class);
                    String fieldValueStr = fieldValue.toString();
                    String newData = fieldValueStr.replace(attendanceFormat.oldSymbol(), attendanceFormat.nowSymbol());
                    BeanUtil.setFieldValue(object, fieldName, newData);
                }
            }
        }
        //没有找到注解字段,直接返回传入的对象
        return Convert.convert(object.getClass().getGenericSuperclass(), object);
    }

    /**
     * 处理集合中每个对象的指定注解字段
     *
     * @param collection Bean对象集合
     * @param clazz      集合中的对象类型
     * @return 处理之后的JavaBean集合
     */
    public static <T> Collection<T> annotationBeanList(Collection<T> collection, Class<T> clazz) {

        return collection.stream().map(t -> annotationBean(t, clazz)).collect(Collectors.toList());
    }

    /**
     * 处理集合中每个对象的指定注解字段
     *
     * @param collection Bean对象集合
     * @return 处理之后的JavaBean集合
     */
    public static <T> Collection<T> dealBeanList(Collection<T> collection) {

        return collection.stream().map(VirtualProcessor::dealAnnotationBean).collect(Collectors.toList());
    }
}
