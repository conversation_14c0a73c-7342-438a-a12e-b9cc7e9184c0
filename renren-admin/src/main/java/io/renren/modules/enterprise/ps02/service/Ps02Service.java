package io.renren.modules.enterprise.ps02.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps02.dto.*;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.ps02.vo.Ps02FilePromiseUploadVO;
import io.renren.modules.enterprise.ps02.vo.Ps02FileUploadVO;
import io.renren.modules.enterprise.ps08.dto.BPs08DTO;
import io.renren.modules.enterprise.ps08.entity.BPs08Entity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Ps02Service extends CrudService<Ps02Entity, Ps02DTO> {
    /**
     * 列表查询数据
     *
     * @param params
     * @return
     */
    PageData<Ps02PageDTO> pageList(Map<String, Object> params);

    /**
     * 新增保存方法
     *
     * @param dto
     */
    void savePs02Info(Ps02DTO dto);

    /**
     * 更新保存方法
     *
     * @param dto
     */
    void updatePs02Info(Ps02DTO dto);

    /**
     * 加载数据
     *
     * @param id ps0201
     * @return
     */
    Ps02DTO getPs02Info(Long id);

    /**
     * 工人退场
     * @param ids 工人主键ps0201
     */
    void exitPerson(Long[] ids);

    /**
     * 工人进场
     * @param ids 工人主键ps0201
     */
    void enterPerson(Long[] ids);

    /**
     * 工人下发到设备
     * @param ids
     */
    void deviceAddPerson(Long[] ids);

    /**
     * 工人下发到设备
     * @param ids
     */
    void deviceIntoPerson(Long[] ids);

    /**
     * 人员用工记录信息
     * @param params
     * @return
     */
    PageData<Ps02EmpRecordDTO> empRecordPageList(Map<String, Object> params);


    /**
     * 预览在线合同
     * @param userId
     * @return
     */
    BPs08Entity previewUserContract(String userId);


    /**
     * 保存工人在线合同信息
     * @param bPs08DTO
     * @return
     */
    void saveUserContract(BPs08DTO bPs08DTO) ;

    /**
     * 导出工人合同模板
     * @param ids
     */
    void exportUserContract(Long[] ids, HttpServletResponse response);

    /**
     * 工人入场
     * @param dto
     */
    void newSave(Ps02DTO dto);

    /**
     * 更新头像
     * @param params
     */
    void updateHeadImage(Map<String, Object> params);


    /**
     * 合同附件上传
     * @param fileUploadVO
     */
    Result contractUpload(Ps02FileUploadVO fileUploadVO) ;

    /**
     * 获取上传附件
     * @param fileUploadVO
     * @return
     */
    Result getContractUpload(String ps0201) ;


    /**
     * 获取上传工人承诺书
     * @param ps0201
     * @return
     */
    Result getPromiseLetter(String ps0201) ;


    /**
     * 上传建筑工人承诺书
     * @param fileUploadVO
     * @return
     */
    Result contractPromiseUpload(Ps02FilePromiseUploadVO fileUploadVO) ;


    /**
     * 下载承诺书
     * @param response
     */
    void downloadPromiseLetter(HttpServletResponse response) ;

    /**
     * 花名册数据分页
     * @param params
     * @return
     */
    PageData<Ps02ExportDTO> exportRosterPage(Map<String, Object> params);

    /**
     * 花名册导出
     * @param params
     * @param response
     */
    void exportRoster(Map<String, Object> params, HttpServletResponse response) throws IOException;

    /**
     * 批量退场
     *
     * @param ids
     */
    void batchExit(String[] ids);

    /**
     * 批量上传合同
     *
     * @param request
     * @param response
     */
    Result batchImportContract(MultipartHttpServletRequest request, HttpServletResponse response);

    /**
     * 获取工人进退场列表
     *
     * @param params
     * @return
     */
    PageData<Ps02InOrOutDTO> getWorkerInOrOutData(Map<String, Object> params);

    /**
     * 获取当前项目工人超龄数量（男大于60，女大于55）
     *
     * @return
     */
    String getWorkerOverageNum();

    /**
     * 获取工人超龄列表数据
     *
     * @param params
     * @return
     */
    PageData<Ps02WorkerOverageDTO> getWorkerOveragePage(Map<String, Object> params);

    /**
     * 根据身份证号获取工人信息
     *
     * @param idcardnumber
     * @return
     */
    Ps02Entity getByIdcardnumberAndName(String idcardnumber, String name);
}