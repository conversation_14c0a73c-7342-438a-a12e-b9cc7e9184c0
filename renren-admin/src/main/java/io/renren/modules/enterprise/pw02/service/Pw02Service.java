package io.renren.modules.enterprise.pw02.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.pw02.dto.Pw02DTO;
import io.renren.modules.enterprise.pw02.entity.Pw02Entity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 工资导入明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
public interface Pw02Service extends CrudService<Pw02Entity, Pw02DTO> {
    // 查询列表数据
    List<Pw02DTO> getList(Map<String, Object> params);
    // 导入工资单
    Result importSalary(MultipartFile file, Map<String, Object> params);
    // 下载工资单模板
    void download(HttpServletResponse response, String month);
    // 导出
    void export(HttpServletResponse response, Long pw0101);
    // 获取工资单实发总金额
    double getSfgzByPw0101(Long pw0101);
    // 更新明细审核状态
    void updateAuditStatusByPw0101(Long pw0101);
}