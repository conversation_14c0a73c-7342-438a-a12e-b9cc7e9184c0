package io.renren.modules.enterprise.tm01audit.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.tm01audit.dto.Tm01DetailAuditDTO;
import io.renren.modules.enterprise.tm01audit.dto.Tm01PageAuditDTO;
import io.renren.modules.enterprise.tm01audit.entity.Tm01AuditEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 分包班组审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@Mapper
public interface Tm01AuditDao extends BaseDao<Tm01AuditEntity> {

    /**
     * 分页
     * @param params
     * @return
     */
    List<Tm01PageAuditDTO> getPageList(Map<String, Object> params);


    /**
     * 详情
     * @param id
     * @return
     */
    Tm01DetailAuditDTO getInfo(Long id) ;
}