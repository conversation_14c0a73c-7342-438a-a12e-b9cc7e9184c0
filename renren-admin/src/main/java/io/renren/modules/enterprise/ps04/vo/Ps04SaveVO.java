package io.renren.modules.enterprise.ps04.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chris
 * @Date : 2022-12-28
 **/
@Data
@ApiModel(value = "管理人员添加")
public class Ps04SaveVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0401;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;
    @ApiModelProperty(value = "人员ID")
    private Long ps0301;
    @ApiModelProperty(value = "岗位类型")
    private String jobtype;
    @ApiModelProperty(value = "项目采集照片")
    private String photo;
    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "证书附件")
    private List<Ot01DTO> certificateFiles;
    //人员基本信息
    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "证件类型_Select选择器", required = true)
    private String idcardtype;

    @ApiModelProperty(value = "证件号码", required = true)
    private String idcardnumber;

    @ApiModelProperty(value = "性别_Select选择器", required = true)
    private String gender;

    @ApiModelProperty(value = "民族_Select选择器", required = true)
    private String nation;

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    @ApiModelProperty(value = "住址", required = true)
    private String address;

    @ApiModelProperty(value = "学历_Select选择器")
    private String edulevel;

    @ApiModelProperty(value = "学位_Select选择器")
    private String degree;

    @ApiModelProperty(value = "身份证头像")
    private String headimageurl;

    @ApiModelProperty(value = "政治面貌", required = true)
    private String politicstype;

    @ApiModelProperty(value = "人员类别", required = true)
    private String workertype;

    @ApiModelProperty(value = "是否加入工会_Select选择器")
    private String isjoined;

    @ApiModelProperty(value = "加入工会时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date joinedtime;

    @ApiModelProperty(value = "手机号码", required = true)
    private String cellphone;

    @ApiModelProperty(value = "文化程度_Select选择器")
    private String cultureleveltype;

    @ApiModelProperty(value = "特长")
    private String specialty;

    @ApiModelProperty(value = "是否有重大病史_Select选择器")
    private String hasbadmedicalhistory;

    @ApiModelProperty(value = "紧急联系人姓名")
    private String urgentlinkman;

    @ApiModelProperty(value = "紧急联系电话")
    private String urgentlinkmanphone;

    @ApiModelProperty(value = "开始工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workdate;

    @ApiModelProperty(value = "婚姻状况")
    private String maritalstatus;

    @ApiModelProperty(value = "发证机关")
    private String grantorg;

    @ApiModelProperty(value = "正面照 URL")
    private String positiveidcardimageurl;

    @ApiModelProperty(value = "反面照 URL")
    private String negativeidcardimageurl;

    @ApiModelProperty(value = "有效期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "有效期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expirydate;

    @ApiModelProperty(value = "籍贯（身份证号前6位）", required = true)
    private String areacode;

    @ApiModelProperty(value = "管理人员八大员附件")
    @Size(max = 1)
    private List<Ot01DTO> managerCertificateFiles;
}
