package io.renren.modules.enterprise.bankjoint.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.bankjoint.dto.BankJointDTO;
import io.renren.modules.enterprise.bankjoint.entity.BankJointEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 诚信评价主体表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
public interface BankJointService extends CrudService<BankJointEntity, BankJointDTO> {

    List<BankJointDTO> pageList(Map<String, Object> params);

}