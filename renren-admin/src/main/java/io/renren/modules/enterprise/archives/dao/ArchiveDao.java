package io.renren.modules.enterprise.archives.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.archives.dto.ArchiveInfo;
import io.renren.modules.enterprise.archives.dto.PartUnitPage;
import io.renren.modules.enterprise.archives.dto.PersonPage;
import io.renren.modules.enterprise.archives.dto.TeamPage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface ArchiveDao extends BaseDao<ArchiveInfo> {
    /**
     * 查询人员列表
     * @param params Map<String, Object>
     * @return List<PersonPage>
     */
    List<PersonPage> selectPersonPageList(Map<String, Object> params);

    /**
     * 查询参建单位列表
     * @param params Map<String, Object>
     * @return List<PartUnitPage>
     */
    List<PartUnitPage> selectPartUnitPageList(Map<String, Object> params);

    /**
     * 获取班组列表
     * @param params Map<String, Object>
     * @return List<TeamPage>
     */
    List<TeamPage> selectTeamPageList(Map<String, Object> params);
}
