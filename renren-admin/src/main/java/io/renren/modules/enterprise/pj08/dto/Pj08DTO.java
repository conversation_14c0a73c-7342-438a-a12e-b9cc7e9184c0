package io.renren.modules.enterprise.pj08.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目竣工验收信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-21
 */
@Data
@ApiModel(value = "项目竣工验收信息表")
public class Pj08DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pj0801;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "工程名称")
    private String prjname;
    @ApiModelProperty(value = "竣工验收编号")
    private String prjfinishchecknum;
    @ApiModelProperty(value = "竣工验收日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edate;
    @ApiModelProperty(value = "申报状态(dic)")
    private String finishstatus;
    @ApiModelProperty(value = "项目状态(dic)")
    private String prjstatus;
    @ApiModelProperty(value = "竣工验收报告附件")
    private List<Ot01DTO> finishFiles;
    @ApiModelProperty(value = "竣工验收报告附件精简版")
    private List<String> finishSimpleFiles;
    @ApiModelProperty(value = "申报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
}