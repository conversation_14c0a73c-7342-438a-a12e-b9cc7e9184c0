package io.renren.modules.enterprise.pw01.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 工资导入记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Data
@ApiModel(value = "工资导入记录表")
public class Pw01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键")
	private Long pw0101;
		@ApiModelProperty(value = "项目ID")
	private Long pj0101;
	@ApiModelProperty(value = "项目名称")
	private String projectName;
		@ApiModelProperty(value = "应发档案工资总额")
	private double yfgz;
		@ApiModelProperty(value = "发放总人数")
	private Long ffzrs;
			@ApiModelProperty(value = "工资所属年月")
	private Long month;
	@ApiModelProperty(value = "工资所属年月")
	@JsonFormat(pattern = "yyyy-MM")
	private Date salaryMonth;
		@ApiModelProperty(value = "是否确认")
	private String issubmit;
		@ApiModelProperty(value = "实发工资总额")
	private double sfgz;
		@ApiModelProperty(value = "提交时间")
	private Date submittime;
		@ApiModelProperty(value = "审核状态（0：待审核，1：通过，2：驳回）")
	private String auditstatus;
		@ApiModelProperty(value = "审核说明")
	private String auditreason;
}