package io.renren.modules.enterprise.share.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 三方对接
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Data
@ApiModel(value = "三方对接")
public class ShareDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "授权公司ID")
    private Long userId;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "授权公司")
    private String company;

    @ApiModelProperty(value = "联系人")
    private String linkman;

    @ApiModelProperty(value = "联系电话")
    private String linkphone;

    @ApiModelProperty(value = "授权状态")
    private String isbind;

    @ApiModelProperty(value = "授权时间")
    private Date bindtime;

}