package io.renren.modules.enterprise.archives.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.archives.dto.PartUnitPage;
import io.renren.modules.enterprise.archives.dto.PersonPage;
import io.renren.modules.enterprise.archives.dto.TeamPage;
import io.renren.modules.enterprise.archives.service.ArchiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;


/**
 * 人员相关档案信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("archives/projectFile")
@Api(tags = "项目档案相关信息")
public class ArchiveController {

    @Resource
    private ArchiveService archiveService;

    @GetMapping("personPage")
    @ApiOperation("人员档案分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "fileType", value = "附件类型", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "personName", value = "人员姓名", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "whether", value = "是否上传", paramType = "query", dataType = "String")
    })
    public Result<PageData<PersonPage>> personPage(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<PersonPage> page = archiveService.personPageList(params);

        return new Result<PageData<PersonPage>>().ok(page);
    }

    @GetMapping("partUnitPage")
    @ApiOperation("参建单位档案分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "fileType", value = "附件类型", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "corpType", value = "参建类型", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "whether", value = "是否上传", paramType = "query", dataType = "String")
    })
    public Result<PageData<PartUnitPage>> partUnitPage(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<PartUnitPage> page = archiveService.partUnitPageList(params);

        return new Result<PageData<PartUnitPage>>().ok(page);
    }

    @GetMapping("teamPage")
    @ApiOperation("班组档案分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "fileType", value = "附件类型", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "whether", value = "是否上传", paramType = "query", dataType = "String")
    })
    public Result<PageData<TeamPage>> teamPage(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<TeamPage> page = archiveService.teamPageList(params);

        return new Result<PageData<TeamPage>>().ok(page);
    }
}