package io.renren.modules.enterprise.ps02face.service;

import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps02face.dto.FaceCheckedResDTO;
import io.renren.modules.enterprise.ps02face.dto.Ps02FaceDTO;
import io.renren.modules.enterprise.ps02face.entity.Ps02FaceEntity;
import io.renren.modules.enterprise.ps02face.vo.FaceAddParamsVO;
import io.renren.modules.enterprise.ps02face.vo.FaceDataAddParamsVO;
import io.renren.modules.enterprise.ps02face.vo.FaceRecoResVO;

import java.util.List;

/**
 * 工人人脸库信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-20
 */
public interface IPs02FaceService extends CrudService<Ps02FaceEntity, Ps02FaceDTO> {

    /**
     * 人脸数据添加
     * @param faceAddParamsVO
     * @return
     */
    Result<String> faceFeatureAdd(FaceAddParamsVO faceAddParamsVO) ;

    /**
     * 采集人脸数据添加
     * @param faceDataAddParamsVO
     * @return
     */
    Result<String> collectFaceDataFeatureAdd(FaceDataAddParamsVO faceDataAddParamsVO) ;

    /**
     * 采集人脸数据添加
     * @param base64
     * @return
     */
    byte[] collectFaceDataFeature(String base64) ;

    /**
     * 人脸搜索
     * @param faceRecoResVO
     * @return
     */
    Result<List<FaceCheckedResDTO>> faceSearch(FaceRecoResVO faceRecoResVO) ;
}