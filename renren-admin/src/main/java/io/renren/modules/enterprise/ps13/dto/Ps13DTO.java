package io.renren.modules.enterprise.ps13.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 关键岗位配置表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-24
 */
@Data
@ApiModel(value = "关键岗位配置表")
public class Ps13DTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键ID")
	private Long ps1301;
		@ApiModelProperty(value = "岗位类型")
	private String jobtype;
		@ApiModelProperty(value = "参建类型")
	private String corptype;
		@ApiModelProperty(value = "岗位类型名称")
	private String jobtypename;
				
}