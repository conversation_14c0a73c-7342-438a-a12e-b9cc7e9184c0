package io.renren.modules.enterprise.pj08.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.message.service.SysSmsService;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.sys.dao.SysUserDao;
import io.renren.modules.admin.sys.dto.SysUserDTO;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.pj08.dao.Pj08Dao;
import io.renren.modules.enterprise.pj08.dto.Pj08DTO;
import io.renren.modules.enterprise.pj08.entity.Pj08Entity;
import io.renren.modules.enterprise.pj08.service.Pj08Service;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目竣工验收信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Pj08ServiceImpl extends CrudServiceImpl<Pj08Dao, Pj08Entity, Pj08DTO> implements Pj08Service {
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private SysUserDao userDao;
    @Autowired
    private SysSmsService sysSmsService;

    @Override
    public QueryWrapper<Pj08Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pj08Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<Pj08DTO> pageList(Map<String, Object> params) {
        IPage<Pj08Entity> page = getPage(params, "", false);
        List<Pj08DTO> list = baseDao.getList(params);
        for (Pj08DTO dto : list) {
            List finishSimpleFiles = new ArrayList();
            List<Ot01DTO> ot01s = ot01Service.loadBusinessData(dto.getPj0801(), "41");
            for (Ot01DTO ot01 : ot01s) {
                finishSimpleFiles.add(ot01.getUrl());
            }
            dto.setFinishSimpleFiles(finishSimpleFiles);
        }
        return getPageData(list, page.getTotal(), Pj08DTO.class);
    }

    @Override
    public Result saveInfo(Pj08DTO dto) {
        Result<Object> result = new Result<>();
        Pj08Entity isfinish = baseDao.selectOne(new QueryWrapper<Pj08Entity>().eq("PJ0101", dto.getPj0101()).eq("FINISHSTATUS", "0"));
        if (isfinish != null) {
            return result.error("已提交状态修改申请，尚未完成，请勿重复申请！");
        }
        if ("4".equals(dto.getPrjstatus()) || "5".equals(dto.getPrjstatus()) || "6".equals(dto.getPrjstatus())) {
            if (dto.getFinishFiles().size() == 0) {
                return result.error("证明文件不能为空！");
            }
        }
        Pj01Entity pj01 = pj01Dao.selectById(dto.getPj0101());
        Pj08Entity pj08 = new Pj08Entity();
        BeanUtil.copyProperties(dto, pj08, CopyOptions.create().setIgnoreNullValue(true));
        pj08.setPrjname(pj01.getName());
        if ("4".equals(dto.getPrjstatus())) {
            pj08.setFinishstatus("0");
            //查询项目对应监管人员手机号
            String areaCode = SecurityUser.getDeptAreaCode();
            List<SysUserDTO> userDtos = userDao.selectRegulUsers(areaCode);
            //发送通知短信
            JSONObject jo = new JSONObject(2);
            jo.put("name", pj01.getName().length() > 30 ? pj01.getName().substring(0, 30) : pj01.getName());
            for (SysUserDTO userDto : userDtos) {
                sysSmsService.send("1006", userDto.getMobile(), jo.toJSONString());
            }
        } else {
            pj08.setFinishstatus("1");
            pj01.setPrjstatus(dto.getPrjstatus());
            pj01Dao.updateById(pj01);
        }
        baseDao.insert(pj08);
        ot01Service.doFileRelation(dto.getFinishFiles(), pj08.getPj0801());
        return result;
    }

    @Override
    public Result callback(Pj08DTO dto) {
        Result<Object> result = new Result<>();
        Long pj0801 = dto.getPj0801();
        Pj08Entity pj08 = baseDao.selectById(pj0801);
        if (!"0".equals(pj08.getFinishstatus())) {
            return result.error("申请已完成或已撤销，无法进行撤销操作！");
        }
        pj08.setFinishstatus("2");
        baseDao.updateById(pj08);
        return result;
    }
}