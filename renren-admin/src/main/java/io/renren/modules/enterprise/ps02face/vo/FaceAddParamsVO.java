package io.renren.modules.enterprise.ps02face.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人脸数据添加
 * <AUTHOR>
 * @date 2023/2/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FaceAddParamsVO {

    /**
     * id
     */
    private Long ps0201;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 是否在场
     */
    private String inOrOut;

    /**
     * 项目区划
     */
    private String areacode;

    /**
     * 图片
     */
    private String image;

    /**
     * 是否是新增数据，默认是新增
     */
    private Boolean isAdd = Boolean.TRUE;
}
