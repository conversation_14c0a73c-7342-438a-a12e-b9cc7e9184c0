package io.renren.modules.enterprise.kq01log.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 考勤设备信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_KQ01")
public class Kq01LogEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long kq0101;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 设备类型
     */
    private String terminaltype;
    /**
     * 设备序列号
     */
    private String deviceserialno;
    /**
     * 设备注册码
     */
    private String devicekey;
    /**
     * 进出状态
     */
    private String note;
    /**
     * 名称
     */
    private String name;
    /**
     * 是否可用
     */
    private String whether;
    /**
     * 网络状态
     */
    private String networkStatus;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}