package io.renren.modules.enterprise.pj01info.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 工资专户
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PA01")
public class Pa01Entity {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long pa0101;
    /**
     * 项目ID
     */
	private Long pj0101;

    /**
     * 农民工工资专用账户名称
     */
	private String specialAccount;
    /**
     * 农民工工资专用账户开户行名称
     */
	private String bankName;
    /**
     * 农民工工资专用账户账号
     */
	private String specialAccountNo;
    /**
     * 银行代码
     */
	private String payBankCode;

	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}