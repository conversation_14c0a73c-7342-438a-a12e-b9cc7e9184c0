package io.renren.modules.enterprise.pw01.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.pw01.dto.Pw01DTO;
import io.renren.modules.enterprise.pw01.entity.Pw01Entity;

import java.util.Map;

/**
 * 工资导入记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
public interface Pw01Service extends CrudService<Pw01Entity, Pw01DTO> {
    // 获取分页数据
    PageData<Pw01DTO> pageList(Map<String, Object> params);
}