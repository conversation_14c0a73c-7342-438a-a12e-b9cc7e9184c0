package io.renren.modules.enterprise.ps02facedata.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj01facetemp.entity.Pj01FaceTempEntity;
import io.renren.modules.enterprise.ps02facedata.entity.Ps02FaceDataEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工人人脸库信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-20
 */
@Mapper
public interface Ps02FaceDataDao extends BaseDao<Ps02FaceDataEntity> {

    /**
     * 获取未采集完成项目
     *
     * @return
     */
    List<Pj01FaceTempEntity> getUnCollectedPj01();
}