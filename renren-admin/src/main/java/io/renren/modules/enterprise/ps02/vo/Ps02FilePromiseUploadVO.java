package io.renren.modules.enterprise.ps02.vo;

import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 建筑工人承诺书信息上传
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "建筑工人承诺书信息上传")
public class Ps02FilePromiseUploadVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "ps0201")
    @NotBlank(message = "请选择一个工人进行承诺书信息上传")
    private String ps0201;

    @ApiModelProperty(value = "工人合同", required = true)
    @Valid
    private List<Ot01DTO> ot01DTOList;

}