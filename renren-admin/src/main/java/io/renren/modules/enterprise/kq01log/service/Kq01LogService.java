package io.renren.modules.enterprise.kq01log.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.kq01log.dto.Kq01LogDTO;
import io.renren.modules.enterprise.kq01log.dto.Kq01LogPageDTO;
import io.renren.modules.enterprise.kq01log.entity.Kq01LogEntity;
import io.renren.modules.enterprise.kq04.dto.Kq04DTO;

import java.util.Map;

/**
 * 考勤设备信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Kq01LogService extends CrudService<Kq01LogEntity, Kq01LogDTO> {

    /**
     * 查询列表数据
     * @param params
     * @return
     */
    PageData<Kq01LogPageDTO> pageList(Map<String, Object> params);

    PageData<Kq04DTO> getLog(Map<String, Object> params);
}