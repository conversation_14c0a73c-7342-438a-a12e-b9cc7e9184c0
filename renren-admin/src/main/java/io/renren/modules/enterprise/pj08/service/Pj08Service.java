package io.renren.modules.enterprise.pj08.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.pj08.dto.Pj08DTO;
import io.renren.modules.enterprise.pj08.entity.Pj08Entity;

import java.util.Map;

/**
 * 项目竣工验收信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-03-21
 */
public interface Pj08Service extends CrudService<Pj08Entity, Pj08DTO> {

    Result saveInfo(Pj08DTO dto);

    Result callback(Pj08DTO dto);

    PageData<Pj08DTO> pageList(Map<String, Object> params);
}