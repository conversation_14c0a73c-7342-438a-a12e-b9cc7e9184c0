package io.renren.modules.enterprise.excelexp.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * DO 用工备案表对象
 *
 * @className: WorkerRecordDTO
 * @author: lrl
 * @date: 2021-05-18 10:36
 **/
@Data
@ApiModel(value = "用工备案表对象")
public class WorkerRecordExcel  {

    @Excel(name = "班组名称", width = 15)
    private String teamname;

    @Excel(name = "姓名", width = 12)
    private String name;

    @Excel(name = "性别", width = 10)
    private String gender;

    @Excel(name = "身份证号码", width = 25)
    private String idcardnumber;

    @Excel(name = "联系电话", width = 25)
    private String cellphone;

    @Excel(name = "工种", width = 25)
    private String worktypecode;

    @Excel(name = "标价", width = 25)
    private String unitprice;

    @Excel(name = "用工开始时间", format = "yyyy-MM-dd", width = 20)
    private String starttime;

    @Excel(name = "用工结束时间", format = "yyyy-MM-dd", width = 20)
    private String endtime;

    @Excel(name = "个人确认签字", width = 20)
    private String selfsignature;

    @Excel(name = "备注", width = 40)
    private String remarks;

}
