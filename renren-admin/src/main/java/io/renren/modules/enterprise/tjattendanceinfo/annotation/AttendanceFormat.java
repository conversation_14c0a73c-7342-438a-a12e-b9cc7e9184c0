package io.renren.modules.enterprise.tjattendanceinfo.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 考勤数据明细表导出，数据格式化
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/5/15 14:54
 **/
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = AttendanceFormatSerialize.class)
public @interface AttendanceFormat {

    /**
     * 原来符号
     *
     * @return
     */
    char oldSymbol() default CharPool.COMMA;

    /**
     * 转换格式
     *
     * cn.hutool.core.text.CharPool
     *
     * @return
     */
    char nowSymbol() default CharPool.LF;
}
