package io.renren.modules.enterprise.cp02audit.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "参建单位分页列表信息")
public class Cp02AuditPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", hidden = true)
    private Long cp0201;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "企业名称")
    private String corpname;

    @ApiModelProperty(value = "统一社会信用代码")
    private String corpcode;

    @ApiModelProperty(value = "联系人")
    private String linkman;

    @ApiModelProperty(value = "联系手机号码")
    private String linkcellphone;

    @ApiModelProperty(value = "参建类型")
    private String corptype;

    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;

}
