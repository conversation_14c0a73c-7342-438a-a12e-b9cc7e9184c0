package io.renren.modules.enterprise.pa05log.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pa05log.dto.Pa05TimeLineDTO;
import io.renren.modules.enterprise.pa05log.entity.Pa05LogEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 工资单操作日志
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-03
 */
@Mapper
public interface Pa05LogDao extends BaseDao<Pa05LogEntity> {


    /**
     * 分页
     * @param pa0501
     * @return
     */
    List<Pa05TimeLineDTO> record(Map<String, Object> params) ;

}