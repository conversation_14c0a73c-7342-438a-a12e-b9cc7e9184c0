package io.renren.modules.enterprise.ps02.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import lombok.Data;

import java.util.Date;

/**
 * 退场
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/3/27 11:39
 **/
@Data
public class Ps02WorkerOverageDTO {

    /**
     * 工人id
     */
    private String ps0201;

    /**
     * 所属企业
     */
    private String corpname;

    /**
     * 所属班组
     */
    private String teamname;

    /**
     * 姓名
     */
    private String personname;

    /**
     * 性别
     */
    private String gender;

    /**
     * 工人年龄
     */
    private String personage;

    /**
     * 工种
     */
    private String worktypecode;

    /**
     * 进场时间
     */
    private Date entrytime;

    /**
     * 身份证号码
     */
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;
}
