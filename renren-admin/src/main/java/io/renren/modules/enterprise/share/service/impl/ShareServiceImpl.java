package io.renren.modules.enterprise.share.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.share.dao.ShareDao;
import io.renren.modules.enterprise.share.dto.ShareDTO;
import io.renren.modules.enterprise.share.entity.ShareEntity;
import io.renren.modules.enterprise.share.service.ShareService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ShareServiceImpl extends CrudServiceImpl<ShareDao, ShareEntity, ShareDTO> implements ShareService {

    @Override
    public QueryWrapper<ShareEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<ShareEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<ShareDTO> pageList(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<ShareEntity> page = getPage(params, "", false);
        List<ShareDTO> list = baseDao.getList(params);
        return getPageData(list, page.getTotal(), ShareDTO.class);
    }

    @Override
    public Result bind(ShareDTO dto) {
        Result<Object> result = new Result<>();
        ShareEntity share = new ShareEntity();
        share.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        share.setUserId(dto.getUserId());
        baseDao.insert(share);
        return result;
    }

    @Override
    public Result unbind(ShareDTO dto) {
        Result<Object> result = new Result<>();
        baseDao.unbind(dto.getUserId(), CommonUtils.userProjectInfo().getPj0101());
        return result;
    }
}