package io.renren.modules.enterprise.share.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.share.dto.ShareDTO;
import io.renren.modules.enterprise.share.entity.ShareEntity;

import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
public interface ShareService extends CrudService<ShareEntity, ShareDTO> {


    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    PageData<ShareDTO> pageList(Map<String, Object> params);

    /**
     * 数据授权
     *
     * @param dto
     * @return
     */
    Result bind(ShareDTO dto);

    /**
     * 解除授权
     *
     * @param dto
     * @return
     */
    Result unbind(ShareDTO dto);
}