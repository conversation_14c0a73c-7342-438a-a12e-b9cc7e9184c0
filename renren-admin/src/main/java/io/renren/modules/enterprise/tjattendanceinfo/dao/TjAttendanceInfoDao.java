package io.renren.modules.enterprise.tjattendanceinfo.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoDTO;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoPageExportDTO;
import io.renren.modules.enterprise.tjattendanceinfo.entity.TjAttendanceInfoEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 考勤统计表详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-05-14
 */
@Mapper
public interface TjAttendanceInfoDao extends BaseDao<TjAttendanceInfoEntity> {

    /**
     * 获取导出考勤数据
     *
     * @param params
     * @return
     */
    List<TjAttendanceInfoPageExportDTO> getTjAttendanceInfoList(Map<String, Object> params);

    /**
     * 生成数据
     *
     * @param mapTjAttendance
     */
    void generateTjAttendanceData(Map<Object, Object> mapTjAttendance);

    Integer getKqdaysByIdcardnumber(Map<String, Object> params);

    void deleteTjAttendanceData(Map<String, Object> params);
}