package io.renren.modules.enterprise.pj05.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目竣工验收信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
public class Pj05Excel {
    @Excel(name = "主键ID")
    private BigDecimal pj0501;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "工程名称")
    private String prjname;
    @Excel(name = "竣工验收编号")
    private String prjfinishchecknum;
    @Excel(name = "竣工验收日期")
    private Date edate;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}