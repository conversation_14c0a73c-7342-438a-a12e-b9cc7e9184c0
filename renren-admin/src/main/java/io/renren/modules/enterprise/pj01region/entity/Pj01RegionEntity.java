package io.renren.modules.enterprise.pj01region.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PJ01_REGION")
public class Pj01RegionEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 电子围栏区域
     */
    private String region;
    /**
     * 审核状态(0待审核，1通过，2不通过)
     */
    private String auditstatus;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核时间
     */
    private Date auditdate;
    /**
     * 审核结果
     */
    private String auditresult;
    /**
     * 标注位置
     */
    private String markLocation;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}