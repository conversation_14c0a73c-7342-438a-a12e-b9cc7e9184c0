package io.renren.modules.enterprise.sa02.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
*<AUTHOR>
*@date 2021/10/9
*/


@Data
@EqualsAndHashCode
@TableName("B_SA02")
@KeySequence("SEQ_B_SA02")
public class Sa02Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long sa0201;

    /**
     * 网点名称
     */
    private String name;

    /**
     * 网点详细地址
     */
    private String address;

    /**
     * 网点所在精度
     */
    private String lng;

    /**
     *网点所在纬度
     */
    private String lat;

    /**
     * 网点行政区划
     */
    private String areacode;

    /**
     * 网点联系人
     */
    private String contacts;

    /**
     * 网点联系电话
     */
    private String contactnumber;

    /**
     *银行代码
     */
    private String Code;

    /**
     * 机构号
     */
    private String institutionnumber;

    /**
     * 邮箱
     */
    private String mailbox;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date createDate;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Double updater;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Double creator;

    /**
     * 上级机构号
     */
    private String superiorinstitutionnumber;

    /**
     * 机构id
     */
    private Double deptId;


}
