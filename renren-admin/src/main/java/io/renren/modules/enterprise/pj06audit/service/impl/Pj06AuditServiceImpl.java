package io.renren.modules.enterprise.pj06audit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.common.service.CommonService;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.pj06audit.dao.Pj06AuditDao;
import io.renren.modules.enterprise.pj06audit.dto.Pj06AuditDTO;
import io.renren.modules.enterprise.pj06audit.entity.Pj06AuditEntity;
import io.renren.modules.enterprise.pj06audit.service.Pj06AuditService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-27
 */
@Service
public class Pj06AuditServiceImpl extends CrudServiceImpl<Pj06AuditDao, Pj06AuditEntity, Pj06AuditDTO> implements Pj06AuditService {
    @Autowired
    private Pj06AuditDao pj06AuditDao;
    @Autowired
    private CommonService commonService;

    @Override
    public QueryWrapper<Pj06AuditEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pj06AuditEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

}