package io.renren.modules.enterprise.ps01.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps01.dto.PersonInfoDTO;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Ps01Service extends CrudService<Ps01Entity, Ps01DTO> {

    /**
     * 列表分页查询
     *
     * @param params
     * @return
     */
//    PageData<Ps01DTO> pageList(Map<String, Object> params);

    /**
     * 根据主键加载数据
     *
     * @param id ps0101
     * @return Ps01DTO
     */
    Ps01DTO getPs01Info(Long id);

    /**
     * 保存信息
     *
     * @param dto
     */
    void savePs01Info(Ps01DTO dto);

    /**
     * 修改信息
     *
     * @param dto
     */
    void updatePs01Info(Ps01DTO dto);

    PageData<Ps01DTO> getManageList(Map<String, Object> params);


    /**
     * 获取人员信息
     * @param params idcardnumber
     * @return
     */
    Result<PersonInfoDTO> getPersonInfo(Map<String, Object> params) ;
}