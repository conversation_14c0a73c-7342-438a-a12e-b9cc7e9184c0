package io.renren.modules.enterprise.cp04audit.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 填写单位分包信息
 * <AUTHOR> chris
 * @Date : 2022-11-22
 **/
@Data
public class Cp04AuditDetailDTO {

    @ApiModelProperty(value = "分包信息ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long cp0401;
    @ApiModelProperty(value = "分包单位ID", required = true)
    @NotNull(message = "请选择一个单位")
    private Long cp0201;

    @ApiModelProperty(value = "分包工程名称", required = true)
    @NotBlank(message = "请填写分包工程名称")
    private String subcontractname;

    @ApiModelProperty(value = "分包工程内容", required = true)
    @NotBlank(message = "请填写分包工程内容")
    private String subcontractcontent;

    @ApiModelProperty(value = "开工时间", required = true)
    @NotNull(message = "请选择开工时间")
    private Date startdate;

    @ApiModelProperty(value = "计划完工时间", required = true)
    @NotNull(message = "请选择计划完工时间")
    private Date completedate;

    @ApiModelProperty(value = "分包价格（万元）")
    @NotNull(message = "请填写分包价格")
    private BigDecimal subcontractprice;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "确认状态（0未审核，1通过，2不通过）")
    private String auditstatus;

    @ApiModelProperty(value = "确认结果")
    private String auditresult;

    @ApiModelProperty(value = "审核人")
    private String auditor;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date auditdate;

    @ApiModelProperty(value = "合同附件")
    @Valid
    private List<Ot01DTO> ot01DTOList;

}
