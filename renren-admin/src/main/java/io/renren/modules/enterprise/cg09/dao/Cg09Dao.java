package io.renren.modules.enterprise.cg09.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.cg09.dto.Cg09DTO;
import io.renren.modules.enterprise.cg09.entity.Cg09Entity;
import io.renren.modules.enterprise.cg10.dto.FileCountInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 检查清单档案配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-09
 */
@Mapper
public interface Cg09Dao extends BaseDao<Cg09Entity> {

    /**
     * 获取文件树
     * @param pj0101 项目ID
     * @return List<Cg09DTO>
     */
    List<Cg09DTO> getFileTreeList(Long pj0101);

    /**
     * 获取所有档案信息
     *
     * @param params Map<String, Object>
     * @return List<Cg09DTO>
     */
    List<Cg09DTO> getAllArchiveList(Map<String, Object> params);

    /**
     * 获取档案配置信息
     *
     * @param params Map<String, Object>
     * @return Cg09DTO
     */
    Cg09DTO selectConfigInfo(Map<String, Object> params);

    /**
     * 获取档案数量信息
     *
     * @param pj0101 项目ID
     * @return List<FileCountInfo>
     */
    List<FileCountInfo> getFileCountByPj0101(Long pj0101);
}