package io.renren.modules.enterprise.ps02.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps08.dto.BPs08DTO;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "建筑工人信息")
public class Ps02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "ps0201")
    private Long ps0201;

    @ApiModelProperty(value = "人员ID")
    private Long ps0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "班组ID", required = true)
    @NotNull(message = "班组长不能为空")
    private Long tm0101;

    @ApiModelProperty(value = "是否班组长", required = true)
    @NotBlank(message = "是否班组长不能为空")
    private String isteamleader;

    @ApiModelProperty(value = "工种", required = true)
    @NotBlank(message = "工种信息不能为空")
    private String worktypecode;

    @ApiModelProperty(value = "工资卡帐号", required = true)
    private String payrollbankcardnumber;

    @ApiModelProperty(value = "工资卡开户行名称", required = true)
    private String payrollbankname;

    @ApiModelProperty(value = "工资卡银行代码", required = true)
    private String payrolltopbankcode;

    @ApiModelProperty(value = "工资卡支付行号")
    private String payrollno;

    @ApiModelProperty(value = "工人头像")
   // @NotBlank(message = "人员头像不能为空")
    private String issuecardpicurl;

    @ApiModelProperty(value = "是否购买工伤或意外伤害保险 ")
    private String hasbuyinsurance;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "人员基础信息", required = true)
    @Valid
    private Ps01DTO ps01DTO;

    @ApiModelProperty(value = "合同信息", required = true)
    @Valid
    private BPs08DTO ps08DTO;

    @ApiModelProperty(value = "工人合同", required = true)
    @Valid
    private List<Ot01DTO> ot01DTOList;

    @ApiModelProperty(value = "特殊工种人员附件")
    @Size(max = 1)
    private List<Ot01DTO> specialWorkCertificateFiles;
}