/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.modules.enterprise.projectledger.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.projectledger.dto.ProjectLedgerDTO;
import io.renren.modules.enterprise.projectledger.service.ProjectLedgerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 项目台账
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys/projectLedger")
@Api(tags="项目台账")
public class ProjectLedgerController {
	@Autowired
	private ProjectLedgerService projectLedgerService;

	@GetMapping("page")
	@ApiOperation("列表")
	@RequiresPermissions("sys:projectLedger:page")
	public Result<PageData<ProjectLedgerDTO>> list(@ApiIgnore @RequestParam Map<String, Object> params){
		PageData<ProjectLedgerDTO> pageData = projectLedgerService.selectProjectLedgerEntityList(params);
		return new Result<PageData<ProjectLedgerDTO>>().ok(pageData);
	}

	@GetMapping("export")
	@ApiOperation("导出项目台账")
	@LogOperation("导出项目台账")
	@RequiresPermissions("sys:projectLedger:export")
	public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {

		projectLedgerService.excelExp(params,response);

	}

}