package io.renren.modules.enterprise.bankjoint.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 诚信评价主体表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("I_BANK_JOINT")
public class BankJointEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 银行代码
     */
    private String payBankCode;
    /**
     * 银行名称
     */
    private String name;
    /**
     * 对接状态
     */
    private String bankjointstatus;
}