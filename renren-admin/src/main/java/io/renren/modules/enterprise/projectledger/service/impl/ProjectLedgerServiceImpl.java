/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.modules.enterprise.projectledger.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.DateUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.modules.enterprise.projectledger.dao.ProjectLedgerDao;
import io.renren.modules.enterprise.projectledger.dto.ProjectLedgerDTO;
import io.renren.modules.enterprise.projectledger.entity.ProjectLedgerEntity;
import io.renren.modules.enterprise.projectledger.service.ProjectLedgerService;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ProjectLedgerServiceImpl extends CrudServiceImpl<ProjectLedgerDao,ProjectLedgerEntity,ProjectLedgerDTO> implements ProjectLedgerService {

    @Autowired
    private ProjectLedgerDao projectLedgerDao;
    /**
     * 考勤表导出模板文件路径
     */
    @Value("${expFile.projectLedger}")
    private  String projectLedger;

    @Override
    public PageData<ProjectLedgerDTO> selectProjectLedgerEntityList(Map<String, Object> params) {
        //转换成likep
        paramsToLike(params, "projectName");
        String areaCode = (String)params.get("areaCode");
        //根据areaCode 获取层级关系
        String level = projectLedgerDao.selectLevelByAreacode(areaCode);
        if ("1".equals(level)){
            //省级
            areaCode = areaCode.substring(0,2) + "____";
        }else if("2".equals(level)){
            areaCode = areaCode.substring(0,4) + "__";
        }
        params.replace("areaCode",areaCode);
        IPage<ProjectLedgerEntity> page = getPage(params, "PJ0101", false);
        List<ProjectLedgerEntity> list = projectLedgerDao.selectProjectLedgerEntityList(params);
        return getPageData(list, page.getTotal(), ProjectLedgerDTO.class);
    }

    @Override
    public void excelExp(Map<String, Object> params, HttpServletResponse response) throws Exception {
        File file = new File(projectLedger);
        if (!file.exists()) {
            throw new IOException("配置的项目台账模板文件不存在");
        }
        paramsToLike(params, "projectName");
        List<ProjectLedgerEntity> list = projectLedgerDao.selectProjectLedgerEntityList(params);
        TemplateExportParams exportParams = new TemplateExportParams(projectLedger);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("date", DateUtils.format(new Date()));
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        String exportFileName = "项目台账";
        ExcelUtils.export(response, workbook, exportFileName);
    }


    @Override
    public QueryWrapper<ProjectLedgerEntity> getWrapper(Map<String, Object> params) {
        return null;
    }
}
