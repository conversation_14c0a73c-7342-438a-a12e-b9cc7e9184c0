package io.renren.modules.enterprise.po01.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.po01.dao.Po01Dao;
import io.renren.modules.enterprise.po01.dto.Po01DTO;
import io.renren.modules.enterprise.po01.entity.Po01Entity;
import io.renren.modules.enterprise.po01.service.Po01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 用油管理
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Po01ServiceImpl extends CrudServiceImpl<Po01Dao, Po01Entity, Po01DTO> implements Po01Service {

    @Override
    public QueryWrapper<Po01Entity> getWrapper(Map<String, Object> params){
        String purchaseunit = (String)params.get("purchaseunit");

        QueryWrapper<Po01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(purchaseunit), "purchaseunit", purchaseunit);

        return wrapper;
    }


}