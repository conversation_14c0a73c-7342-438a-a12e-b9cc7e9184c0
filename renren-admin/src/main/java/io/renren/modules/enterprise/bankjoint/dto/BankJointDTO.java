package io.renren.modules.enterprise.bankjoint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 银行对接情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
@Data
@ApiModel(value = "银行对接情况")
public class BankJointDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "银行代码")
    private String payBankCode;
    @ApiModelProperty(value = "银行名称")
    private String name;
    @ApiModelProperty(value = "对接状态")
    private String bankjointstatus;
    @ApiModelProperty(value = "对接状态-翻译")
    private String bankjointstatusDictLabel;
    @ApiModelProperty(value = "开户项目数")
    private String projects;
}