package io.renren.modules.enterprise.pa05log.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.enterprise.pa05log.dto.Pa05LogDTO;
import io.renren.modules.enterprise.pa05log.dto.Pa05TimeLineDTO;
import io.renren.modules.enterprise.pa05log.service.Pa05LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 工资单操作日志
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-03
 */
@RestController
@RequestMapping("enterprise/pa05log")
@Api(tags="工资单操作日志")
public class Pa05LogController {
    @Autowired
    private Pa05LogService pa05LogService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("enterprise:pa05log:page")
    public Result<PageData<Pa05LogDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Pa05LogDTO> page = pa05LogService.page(params);

        return new Result<PageData<Pa05LogDTO>>().ok(page);
    }



    @GetMapping("record")
    @ApiOperation("记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("enterprise:pa05log:record")
    public Result<List<Pa05TimeLineDTO>> record(@ApiIgnore @RequestParam Map<String, Object> params){

        return pa05LogService.record(params);
    }

}