package io.renren.modules.enterprise.pj01report.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.pj01report.dto.Pj01ReportDTO;
import io.renren.modules.enterprise.pj01report.service.Pj01ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 项目上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-12
 */
@RestController
@RequestMapping("enterprise/pj01report")
@Api(tags="项目上报配置表")
public class Pj01ReportController {
    @Autowired
    private Pj01ReportService pj01ReportService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pj01report:page")
    public Result<PageData<Pj01ReportDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Pj01ReportDTO> page = pj01ReportService.selectListData(params);

        return new Result<PageData<Pj01ReportDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    public Result<Pj01ReportDTO> get(@PathVariable("id") Long pj0101){
        Pj01ReportDTO data = pj01ReportService.getData(pj0101);

        return new Result<Pj01ReportDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:pj01report:save")
    public Result save(@RequestBody Pj01ReportDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj01ReportService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    public Result update(@RequestBody Pj01ReportDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj01ReportService.updateData(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pj01report:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj01ReportService.delete(ids);

        return new Result();
    }

}