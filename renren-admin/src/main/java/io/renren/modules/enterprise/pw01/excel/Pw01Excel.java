package io.renren.modules.enterprise.pw01.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工资导入记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Data
public class Pw01Excel {
    @Excel(name = "主键")
    private BigDecimal pw0101;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "应发档案工资总额")
    private BigDecimal yfgz;
    @Excel(name = "发放总人数")
    private Long ffzrs;
    @Excel(name = "扣发工资总额")
    private BigDecimal kfgz;
    @Excel(name = "创建时间")
    private Date createdate;
    @Excel(name = "创建人")
    private String creator;
    @Excel(name = "工资所属年月")
    private Date month;
    @Excel(name = "是否确认")
    private String issubmit;
    @Excel(name = "实发工资总额")
    private BigDecimal sfgz;
    @Excel(name = "提交时间")
    private Date submittime;
    @Excel(name = "审核状态（0：待审核，1：通过，2：驳回）")
    private String auditstatus;
    @Excel(name = "审核说明")
    private String auditreason;

}