package io.renren.modules.enterprise.pj06.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2020-05-18 10:55
 */

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("R_PJ01_DEPT")
public class Rpj01DeptEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 机构ID
     */
    private Long deptId;
}
