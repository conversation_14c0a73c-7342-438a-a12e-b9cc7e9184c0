package io.renren.modules.enterprise.pj05.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 培训考题表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-21
 */
@Data
@ApiModel(value = "培训考题表")
public class Pj05DTO implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "主键ID")
	private Long pj0501;
	@ApiModelProperty(value = "培训ID")
	private Long pj0301;
	@ApiModelProperty(value = "序号")
	private String sno;
	@ApiModelProperty(value = "试题标题")
	private String title;
	@ApiModelProperty(value = "选项A")
	private String optiona;
	@ApiModelProperty(value = "选项B")
	private String optionb;
	@ApiModelProperty(value = "选项C")
	private String optionc;
	@ApiModelProperty(value = "选项D")
	private String optiond;
	@ApiModelProperty(value = "正确答案")
	private String answer;
	@ApiModelProperty(value = "分值")
	private Short score;
				
}