package io.renren.modules.enterprise.ps08.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工人合同信息
 * <AUTHOR> chris
 * @Date : 2022-11-25
 **/
@Data
@ApiModel(value = "在线合同")
public class BPs08DetailDTO {

    @ApiModelProperty(value = "主键ID")
    private Long ps0801;

    @ApiModelProperty(value = "工人ID")
    private Long ps0201;

    @ApiModelProperty(value = "合同编号")
    private String contractno;

    @ApiModelProperty(value = "甲方（用工企业）")
    private String corpname;

    @ApiModelProperty(value = "统一信用代码")
    private String corpcode;

    @ApiModelProperty(value = "法定代表人或委托代理人")
    private String legalman;

    @ApiModelProperty(value = "单位地址")
    private String corpaddress;

    @ApiModelProperty(value = "联系电话")
    private String linkcellphone;

    @ApiModelProperty(value = "乙方姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "出生年月")
    @JsonFormat(pattern = "yyyyMMdd")
    private Date birthday;

    @ApiModelProperty(value = "身份证号码")
    private String idcardnumber;

    @ApiModelProperty(value = "居住地址")
    private String address;

    @ApiModelProperty(value = "联系电话")
    private String cellphone;

    @ApiModelProperty(value = "合同开始时间")
    private String contractbegin;

    @ApiModelProperty(value = "项目名称")
    private String projectname;

    @ApiModelProperty(value = "工程名")
    private String prjname;

    @ApiModelProperty(value = "工种")
    private String worktypecode;

    @ApiModelProperty(value = "职责")
    private String duty;

    @ApiModelProperty(value = "工资支付方式")
    private String salarypaymentmethod;

    @ApiModelProperty(value = "每月计时工资")
    private String timewage;

    @ApiModelProperty(value = "工作量")
    private String workload;

    @ApiModelProperty(value = "工作量工资")
    private String workloadwage;

    @ApiModelProperty(value = "其他支付形式")
    private String otherwage;

    @ApiModelProperty(value = "工资发放日")
    private String payday;

    @ApiModelProperty(value = "工时制度")
    private String workhoursystem;

    @ApiModelProperty(value = "每天工作小时")
    private String workhour;

    @ApiModelProperty(value = "每周工作天数")
    private String workday;

    @ApiModelProperty(value = "不定时工时制")
    private String workirregular;

    @ApiModelProperty(value = "福利")
    private String welfare;

    @ApiModelProperty("委托代理人")
    private String consignor ;

    @ApiModelProperty("工作地点")
    private String workeraddress;

    @ApiModelProperty("其他补充事项")
    private String otherthings;

    @ApiModelProperty("签订时间")
    private String signdate;

    @ApiModelProperty(value = "创建者")
    private Long creator;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新者")
    private Long updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
}
