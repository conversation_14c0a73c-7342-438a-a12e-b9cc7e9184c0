package io.renren.modules.enterprise.excelexp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.DateUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.excelexp.dao.ExcelExpDao;
import io.renren.modules.enterprise.excelexp.dto.AttendanceRecordDTO;
import io.renren.modules.enterprise.excelexp.dto.PayrollRegisterDTO;
import io.renren.modules.enterprise.excelexp.dto.WorkerRecordDTO;
import io.renren.modules.enterprise.excelexp.service.ExcelExpService;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 三表导出
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-18
 */
@Service
public class ExcelExpServiceImpl extends CrudServiceImpl implements ExcelExpService {
    @Autowired
    ExcelExpDao excelExpDao;
    /**
     * 考勤表导出模板文件路径
     */
    @Value("${expFile.attendanceRecord}")
    private  String attendanceRecord;

    /**
     * 工资发放花名册导出模板文件路径
     */
    @Value("${expFile.payrollRegister}")
    private String payrollRegister;
    /**
     * 备案表导出模板文件路径
     */
    @Value("${expFile.workerRecord}")
    private String workerRecord;


    @Override
    public PageData<JsonObject> getPageList(Map<String, Object> params) {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        params.put("pj0101",pj0101);
        String type = (String) params.get("type");
        if("".equals(type) || null == type){
            return null;
        }
        if ("1".equals(type)) {
            IPage<PayrollRegisterDTO> page = getPage(params, "", false);
            List<PayrollRegisterDTO> list = excelExpDao.getPayrollRegisterDTOList(params);
            return getPageData(list, page.getTotal(), PayrollRegisterDTO.class);
        } if ( "2".equals(type)) {
            IPage<WorkerRecordDTO> page = getPage(params, "", false);
            List<WorkerRecordDTO> list = excelExpDao.getWorkerRecordDTOList(params);
            return getPageData(list, page.getTotal(), WorkerRecordDTO.class);
        }
        IPage<AttendanceRecordDTO> page = getPage(params, "", false);
        List<AttendanceRecordDTO> list = excelExpDao.getAttendanceRecordDTOList(params);
        return getPageData(list, page.getTotal(), AttendanceRecordDTO.class);
    }

    @Override
    public void excelExp(Map<String, Object> params, HttpServletResponse response) throws Exception  {
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId",deptId);
        String type = (String) params.get("type");
        if (null != type && "1".equals(type)) {
            expPayrollRegister(params,response);
        }else if(null != type && "2".equals(type)){
            expWorkerRecord(params,response);
        }else if(null != type && "3".equals(type)){
            expAttendanceRecord(params,response);
        }
    }

    @Override
    public QueryWrapper getWrapper(Map params) {
        return null;
    }

    private void expPayrollRegister(Map<String, Object> params, HttpServletResponse response) throws Exception {
        File file = new File(payrollRegister);
        if (!file.exists()) {
            throw new IOException("配置的工资发放花名册模板文件不存在");
        }
        List<PayrollRegisterDTO> list = excelExpDao.getPayrollRegisterDTOList(params);
        TemplateExportParams exportParams = new TemplateExportParams(payrollRegister);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("month", params.get("month"));
        data.put("date", DateUtils.format(new Date()));
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        String exportFileName = "农民工工资发放花名册";
        ExcelUtils.export(response, workbook, exportFileName);
//        ExcelUtils.exportExcelToTarget(response, "农民工工资发放花名册", list, PayrollregisterExcel.class);
    }

    private void expWorkerRecord(Map<String, Object> params, HttpServletResponse response) throws Exception {
        File file = new File(workerRecord);
        if (!file.exists()) {
            throw new IOException("配置的用工备案表模板文件不存在");
        }
        List<WorkerRecordDTO> list = excelExpDao.getWorkerRecordDTOList(params);
        TemplateExportParams exportParams = new TemplateExportParams(workerRecord);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("month", params.get("month"));
        data.put("date", DateUtils.format(new Date()));
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        String exportFileName = "项目工人用工备案表";
        ExcelUtils.export(response, workbook, exportFileName);
//        ExcelUtils.exportExcelToTarget(response, "用工备案表", list, WorkerRecordExcel.class);
    }

    private void expAttendanceRecord(Map<String, Object> params, HttpServletResponse response) throws Exception {
        File file = new File(attendanceRecord);
        if (!file.exists()) {
            throw new IOException("配置的考勤统计模板文件不存在");
        }
        List<AttendanceRecordDTO> list = excelExpDao.getAttendanceRecordDTOList(params);
        TemplateExportParams exportParams = new TemplateExportParams(attendanceRecord);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("month", params.get("month"));
        data.put("date", DateUtils.format(new Date()));
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        String exportFileName = "项目人员考勤统计表";
        ExcelUtils.export(response, workbook, exportFileName);
//        ExcelUtils.exportExcelToTarget(response, "考勤统计表", list, AttendanceRecordExcel.class);
    }


}
