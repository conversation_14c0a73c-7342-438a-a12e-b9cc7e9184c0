package io.renren.modules.enterprise.ps02face.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.arcsoft.face.*;
import com.arcsoft.face.enums.DetectMode;
import com.arcsoft.face.enums.DetectOrient;
import com.arcsoft.face.toolkit.ImageInfo;
import com.google.common.collect.Lists;
import io.renren.common.exception.RenException;
import io.renren.modules.enterprise.ps02face.constants.ErrorCodeConstants;
import io.renren.modules.enterprise.ps02face.dto.FaceRecoResDTO;
import io.renren.modules.enterprise.ps02face.entity.Ps02FaceEntity;
import io.renren.modules.enterprise.ps02face.factory.FaceEngineFactory;
import io.renren.modules.enterprise.ps02face.service.IFaceEngineService;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2023/2/18
 */
@Service
public class FaceEngineServiceImpl implements IFaceEngineService {

    public final static Logger logger = LoggerFactory.getLogger(FaceEngineServiceImpl.class);

    @Value("${config.arcface-sdk.sdk-lib-path}")
    public String sdkLibPath;

    @Value("${config.arcface-sdk.app-id}")
    public String appId;

    @Value("${config.arcface-sdk.sdk-key}")
    public String sdkKey;

    @Value("${config.arcface-sdk.thread-pool-size}")
    public Integer threadPoolSize;

    private ExecutorService executorService;

    private GenericObjectPool<FaceEngine> faceEngineObjectPool;


    @PostConstruct
    public void init() {
        executorService = Executors.newFixedThreadPool(threadPoolSize);
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxIdle(threadPoolSize);
        poolConfig.setMaxTotal(threadPoolSize);
        poolConfig.setMinIdle(threadPoolSize);
        poolConfig.setLifo(false);

        //引擎配置
        EngineConfiguration engineConfiguration = new EngineConfiguration();
        engineConfiguration.setDetectMode(DetectMode.ASF_DETECT_MODE_IMAGE);
        engineConfiguration.setDetectFaceOrientPriority(DetectOrient.ASF_OP_0_ONLY);

        //功能配置
        FunctionConfiguration functionConfiguration = new FunctionConfiguration();
        functionConfiguration.setSupportAge(true);
        functionConfiguration.setSupportFaceDetect(true);
        functionConfiguration.setSupportFaceRecognition(true);
        functionConfiguration.setSupportGender(true);
        functionConfiguration.setSupportLiveness(true);
        functionConfiguration.setSupportIRLiveness(true);
        engineConfiguration.setFunctionConfiguration(functionConfiguration);

        faceEngineObjectPool = new GenericObjectPool(new FaceEngineFactory(sdkLibPath, appId, sdkKey, engineConfiguration), poolConfig);//底层库算法对象池
    }


    @Override
    public List<FaceInfo> detectFaces(ImageInfo imageInfo) {
        FaceEngine faceEngine = null;
        try {
            faceEngine = faceEngineObjectPool.borrowObject();
            // 检测得到的数据列表
            List<FaceInfo> faceInfoList = new ArrayList<>();
            // 人脸检测
            faceEngine.detectFaces(imageInfo.getImageData(), imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getImageFormat(), faceInfoList);
            return faceInfoList;
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            if (faceEngine != null) {
                //释放引擎对象
                faceEngineObjectPool.returnObject(faceEngine);
            }
        }
        return null;
    }

    @Override
    public Float compareFace(ImageInfo imageInfo1, ImageInfo imageInfo2) {
        List<FaceInfo> faceInfoList1 = detectFaces(imageInfo1);
        List<FaceInfo> faceInfoList2 = detectFaces(imageInfo2);

        if (CollectionUtil.isEmpty(faceInfoList1)) {
            throw new RenException(ErrorCodeConstants.CHECK_NO_FACE.getCode(),
                    ErrorCodeConstants.CHECK_NO_FACE.getDesc());
        }
        if (CollectionUtil.isEmpty(faceInfoList2)) {
            throw new RenException(ErrorCodeConstants.CHECK_NO_FACE.getCode(),
                    ErrorCodeConstants.CHECK_NO_FACE.getDesc());
        }

        byte[] feature1 = extractFaceFeature(imageInfo1, faceInfoList1.get(0));
        byte[] feature2 = extractFaceFeature(imageInfo2, faceInfoList2.get(0));

        FaceEngine faceEngine = null;
        try {
            faceEngine = faceEngineObjectPool.borrowObject();
            if (faceEngine == null) {
                throw new RenException(500, "获取引擎失败");
            }
            FaceFeature faceFeature1 = new FaceFeature();
            faceFeature1.setFeatureData(feature1);
            FaceFeature faceFeature2 = new FaceFeature();
            faceFeature2.setFeatureData(feature2);
            //提取人脸特征
            FaceSimilar faceSimilar = new FaceSimilar();
            int errorCode = faceEngine.compareFaceFeature(faceFeature1, faceFeature2, faceSimilar);
            if (errorCode == 0) {
                return faceSimilar.getScore();
            } else {
                logger.error("特征提取失败，errorCode：" + errorCode);
            }

        } catch (Exception e) {
            logger.error("", e);
        } finally {
            if (faceEngine != null) {
                //释放引擎对象
                faceEngineObjectPool.returnObject(faceEngine);
            }
        }
        return null;
    }

    @Override
    public byte[] extractFaceFeature(ImageInfo imageInfo, FaceInfo faceInfo) {
        FaceEngine faceEngine = null;
        try {
            faceEngine = faceEngineObjectPool.borrowObject();
            if (faceEngine == null) {
                throw new RenException(ErrorCodeConstants.INIT_FACE_ENGINE_FAILED.getCode(),
                        ErrorCodeConstants.INIT_FACE_ENGINE_FAILED.getDesc());
            }

            FaceFeature faceFeature = new FaceFeature();
            // 提取人脸特征
            int errorCode = faceEngine.extractFaceFeature(imageInfo.getImageData(), imageInfo.getWidth(), imageInfo.getHeight(), imageInfo.getImageFormat(), faceInfo, faceFeature);
            if (errorCode == 0) {
                return faceFeature.getFeatureData();
            } else {
                logger.error("特征提取失败，errorCode：" + errorCode);
            }
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            if (faceEngine != null) {
                //释放引擎对象
                faceEngineObjectPool.returnObject(faceEngine);
            }
        }
        return null;
    }

    @Override
    public List<FaceRecoResDTO> faceRecoSearch(byte[] faceFeature, List userInfoList, float passRate) {

        List<FaceRecoResDTO> resultUserInfoList = Lists.newLinkedList();

        FaceFeature targetFaceFeature = new FaceFeature();
        targetFaceFeature.setFeatureData(faceFeature);

        List<List<Ps02FaceEntity>> faceUserInfoPartList = Lists.partition(userInfoList, 1000);
        CompletionService<List<FaceRecoResDTO>> completionService = new ExecutorCompletionService(executorService);
        for (List<Ps02FaceEntity> part : faceUserInfoPartList) {
            completionService.submit(new CompareFaceTask(part, targetFaceFeature, passRate));
        }
        for (int i = 0; i < faceUserInfoPartList.size(); i++) {
            List<FaceRecoResDTO> faceUserInfoList = null;
            try {
                faceUserInfoList = completionService.take().get();
            } catch (Exception e) {
                logger.error("Failed to get face data {}", e.getMessage());
            }
            if (CollectionUtil.isNotEmpty(userInfoList)) {
                resultUserInfoList.addAll(faceUserInfoList);
            }
        }

        sortUserInfoList(resultUserInfoList) ;

        return resultUserInfoList;
    }

    /**
     * 多线程人脸数据处理
     */
    private class CompareFaceTask implements Callable<List<FaceRecoResDTO>> {

        private List<Ps02FaceEntity> userInfoList;
        private FaceFeature targetFaceFeature;
        private float passRate;

        public CompareFaceTask(List<Ps02FaceEntity> userInfoList, FaceFeature targetFaceFeature, float passRate) {
            this.userInfoList = userInfoList;
            this.targetFaceFeature = targetFaceFeature;
            this.passRate = passRate;
        }

        @Override
        public List<FaceRecoResDTO> call() throws Exception {
            FaceEngine faceEngine = null;
            List<FaceRecoResDTO> resultUserInfoList = Lists.newLinkedList();//识别到的人脸列表
            try {
                faceEngine = faceEngineObjectPool.borrowObject();
                for (Ps02FaceEntity userInfo : userInfoList) {
                    FaceFeature sourceFaceFeature = new FaceFeature();
                    sourceFaceFeature.setFeatureData(userInfo.getFaceFeature());
                    FaceSimilar faceSimilar = new FaceSimilar();
                    faceEngine.compareFaceFeature(targetFaceFeature, sourceFaceFeature, faceSimilar);
                    if (faceSimilar.getScore() >= passRate) {
                        FaceRecoResDTO info = new FaceRecoResDTO();
                        info.setPs0201(userInfo.getPs0201());
                        info.setSimilar(faceSimilar.getScore());
                        resultUserInfoList.add(info);
                    }
                }
            } catch (Exception e) {
                logger.error("", e);
            } finally {
                if (faceEngine != null) {
                    faceEngineObjectPool.returnObject(faceEngine);
                }
            }
            return resultUserInfoList;
        }
    }

    /**
     * 从大到小排序对查询出来的人脸数据排序
     * @param resultUserInfoList
     */
    private void sortUserInfoList(List<FaceRecoResDTO> resultUserInfoList) {

        if (resultUserInfoList.size() == 0) {
            return;
        }

        resultUserInfoList.sort((h1, h2) -> h2.getSimilar()
                .compareTo(h1.getSimilar()));

        // 只存在提取前五名员工
        if (resultUserInfoList.size() > 5) {
            resultUserInfoList.subList(0, 5);
        }
    }
}
