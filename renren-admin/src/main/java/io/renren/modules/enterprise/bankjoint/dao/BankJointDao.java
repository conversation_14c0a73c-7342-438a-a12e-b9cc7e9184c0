package io.renren.modules.enterprise.bankjoint.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.admin.sys.entity.DictDTO;
import io.renren.modules.enterprise.bankjoint.dto.BankJointDTO;
import io.renren.modules.enterprise.bankjoint.entity.BankJointEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 诚信评价主体表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-14
 */
@Mapper
public interface BankJointDao extends BaseDao<BankJointEntity> {

    List<BankJointDTO> getList(Map<String, Object> params);


    List<DictDTO> getJointBanks();

}