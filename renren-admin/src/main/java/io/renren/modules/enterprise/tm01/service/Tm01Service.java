package io.renren.modules.enterprise.tm01.service;

import io.renren.common.common.dto.CommonDto;
import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.tm01.dto.Tm01DTO;
import io.renren.modules.enterprise.tm01.dto.Tm01DetailDTO;
import io.renren.modules.enterprise.tm01.dto.Tm01PageDTO;
import io.renren.modules.enterprise.tm01.entity.Tm01Entity;
import io.renren.modules.enterprise.tm01.vo.Tm01FileUploadVO;
import io.renren.modules.enterprise.tm01.vo.Tm01SaveVO;
import io.renren.modules.enterprise.tm01.vo.Tm01UpdateVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
public interface Tm01Service extends CrudService<Tm01Entity, Tm01DTO> {

    /**
     * 校验当前项目下，班组名称是否重复
     *
     * @param teamName 班组名称
     * @param pj0101   项目ID
     */
    void checkTeamName(String teamName, Long pj0101);

    /**
     * 分页查询
     * @param params
     * @return
     */
    PageData<Tm01PageDTO> pageList(Map<String, Object> params);

    /**
     * 保存班组信息
     * @param dto
     */
    void saveTeamInfo(Tm01DTO dto);

    /**
     * 保存班组信息
     * @param dto
     */
    void saveTeamInfo(Tm01SaveVO dto);

    /**
     *
     * @return
     */
    Tm01DetailDTO getTeamInfo(Long teamId) ;

    /**
     * 查询当前登录用户下边的班组
     *
     * @return
     */
    List<CommonDto> getTeamList();


    /**
     * 更新班组信息
     * @param dto
     * @return
     */
    void updateTeam(Tm01UpdateVO dto) ;


    /**
     * 进场
     */
    void entryTeam(Long[] ids) ;

    /**
     * 退场
     */
    void exitTeam(Long[] ids) ;

    /**
     * 班组安全承诺书上传
     * @param fileUploadVO
     * @return
     */
    Result teamPromiseUpload(Tm01FileUploadVO fileUploadVO);


    /**
     * 获取班组承诺书附件信息
     * @param tm0101
     * @return
     */
    Result getTeamPromiseUpload(String tm0101) ;


    /**
     * 下载班组承诺书
     * @param response
     */
    void downloadTeamPromise(HttpServletResponse response) ;
}