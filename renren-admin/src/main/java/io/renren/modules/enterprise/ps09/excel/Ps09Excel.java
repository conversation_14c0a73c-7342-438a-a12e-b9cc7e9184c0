package io.renren.modules.enterprise.ps09.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工人工资附件表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-22
 */
@Data
public class Ps09Excel {
    @Excel(name = "主键")
    private BigDecimal ps0901;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "发放月份")
    private Date yearmonth;
    @Excel(name = "是否上传")
    private Long isupload;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}