package io.renren.modules.enterprise.pj06.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PJ06")
public class Pj06Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Long pj0601;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 所属行业
     */
    private String industry;
    /**
     * 项目所在地
     */
    private String areacode;
    /**
     * 建设地址
     */
    private String address;
    /**
     * 经度
     */
    private BigDecimal lng;
    /**
     * 纬度
     */
    private BigDecimal lat;
    /**
     * 建设单位ID
     */
    private Long constructcode;
    /**
     * 总包单位ID
     */
    private Long contractcode;
    /**
     * 监理单位ID
     */
    private Long supervisecode;
    /**
     * 联系人姓名
     */
    private String linkman;
    /**
     * 联系人电话
     */
    private String linkphone;
    /**
     * 主管部门
     */
    private String virareacode;
    /**
     * 上报地
     */
    private String report;
    /**
     * 备注
     */
    private String memo;
    /**
     * 审核状态
     */
    private String auditstatus;
    /**
     * 审核时间
     */
    private Date auditdate;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核结果
     */
    private String auditresult;
    /**
     * 中心负责人id
     */
    private Long ps1201;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long  creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}