package io.renren.modules.enterprise.archives.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "参建单位档案分页")
public class PartUnitPage implements Serializable {
    private static final long serialVersionUID = 6940232877781126103L;

    @ApiModelProperty(value = "参建单位ID")
    private Long partUnitId;

    @ApiModelProperty(value = "参建单位名称")
    private String partUnitName;

    @ApiModelProperty(value = "参建单位类型")
    private String partUnitType;

    @ApiModelProperty(value = "状态")
    private String inOrOut;

    @ApiModelProperty(value = "是否上传")
    private String whether;
}
