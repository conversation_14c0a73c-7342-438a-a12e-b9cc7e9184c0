package io.renren.modules.enterprise.ps03.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "管理人员在职信息表")
public class Ps03PageDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0301;

    @ApiModelProperty(value = "所属项目")
    private String projectName;

    @ApiModelProperty(value = "所属项目ID")
    private String pj0101;

    /*@ApiModelProperty(value = "所属企业")
    private String cp0201;*/

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "是否进场")
    private String inOrOut;

    @ApiModelProperty(value = "审核状态")
    private String auditstatus;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "岗位类型")
    private String jobtype;

    @ApiModelProperty(value = "管理类型")
    private String manageType;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;

    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    @ApiModelProperty(value = "手机号码")
    private String cellphone;
}
