package io.renren.modules.enterprise.ps02.service.impl;

import cn.afterturn.easypoi.entity.ImageEntity;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import io.renren.common.constant.Constant;
import io.renren.common.constants.CommonConstants;
import io.renren.common.exception.RenException;
import io.renren.common.file.FileProper;
import io.renren.common.file.ImageUtil;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.FileDownloadUtil;
import io.renren.common.utils.Result;
import io.renren.modules.admin.sys.dao.SysDictDataDao;
import io.renren.modules.enterprise.cp01.dao.Cp01Dao;
import io.renren.modules.enterprise.cp01.entity.Cp01Entity;
import io.renren.modules.enterprise.cp02.dao.Cp02Dao;
import io.renren.modules.enterprise.cp02.entity.Cp02Entity;
import io.renren.modules.enterprise.kq05.dao.Kq05Dao;
import io.renren.modules.enterprise.kq05.entity.Kq05Entity;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.ps01.dao.Ps01Dao;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps02.dto.*;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.ps02.service.Ps02Service;
import io.renren.modules.enterprise.ps02.vo.Ps02FilePromiseUploadVO;
import io.renren.modules.enterprise.ps02.vo.Ps02FileUploadVO;
import io.renren.modules.enterprise.ps02face.service.IPs02FaceService;
import io.renren.modules.enterprise.ps02face.vo.FaceAddParamsVO;
import io.renren.modules.enterprise.ps06.dao.BPs06Dao;
import io.renren.modules.enterprise.ps06.entity.BPs06Entity;
import io.renren.modules.enterprise.ps06.service.BPs06Service;
import io.renren.modules.enterprise.ps08.dao.BPs08Dao;
import io.renren.modules.enterprise.ps08.dto.BPs08DTO;
import io.renren.modules.enterprise.ps08.entity.BPs08Entity;
import io.renren.modules.enterprise.ps08.service.BPs08Service;
import io.renren.modules.enterprise.tm01.dao.Tm01Dao;
import io.renren.modules.enterprise.tm01.entity.Tm01Entity;
import io.renren.modules.ot01.dao.Ot01Dao;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.entity.Ot01Entity;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.supdevice.dao.SupDeviceDao;
import io.renren.modules.supdevice.entity.SupDeviceEntity;
import io.renren.modules.supdevicetask.dao.SupDeviceTaskDao;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import io.renren.modules.supdevicetask.entity.SupDeviceTaskEntity;
import io.renren.modules.supdevicetask.service.SupDeviceTaskService;
import io.renren.modules.supdevicetaskdetail.dao.SupDeviceTaskDetailDao;
import io.renren.modules.supdevicetaskdetail.entity.SupDeviceTaskDetailEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class Ps02ServiceImpl extends CrudServiceImpl<Ps02Dao, Ps02Entity, Ps02DTO> implements Ps02Service {
    @Value("${expFile.rosterExport}")
    private String ROSTER_EXPORT;
    @Autowired
    private Ps02Service ps02Service;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Ps01Dao ps01Dao;
    @Autowired
    private Kq05Dao kq05Dao;
    @Autowired
    private Ps01Service ps01Service;
    @Autowired
    private Ps02Dao ps02Dao;
    @Autowired
    private Ot01Dao ot01Dao;
    @Autowired
    private Tm01Dao tm01Dao;
    @Autowired
    private BPs06Dao ps06Dao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private BPs08Service ps08Service;
    @Autowired
    private BPs08Dao ps08Dao;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private SupDeviceTaskService supDeviceTaskService;
    @Autowired
    private SupDeviceDao supDeviceDao;
    @Autowired
    private SupDeviceTaskDao supDeviceTaskDao;
    @Autowired
    private SupDeviceTaskDetailDao supDeviceTaskDetailDao;
    @Autowired
    private IPs02FaceService iPs02FaceService;
    @Autowired
    private BPs06Service bPs06Service;
    @Autowired
    private SysDictDataDao dictDataDao;
    private final static String IMAGE = "data:image";
    @Value("${expFile.contractExport}")
    private String CONTRACT_EXPORT;
    @Value("${expFile.promiseExport}")
    private String PROMISE_EXPORT;
    /**
     * 人员类型
     */
    private final static String PERSON_TYPE = "1";

    private static String filePath;

    public Ps02ServiceImpl(FileProper properties) {
        Ps02ServiceImpl.filePath = properties.getPath();
    }

    @Override
    public QueryWrapper<Ps02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Ps02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }

    @Override
    public PageData<Ps02PageDTO> pageList(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<Ps02PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Ps02PageDTO.class);
    }

    @Override
    public void savePs02Info(Ps02DTO dto) {
        Ps01DTO ps01dto = dto.getPs01DTO();
        //根据身份证倒数第二位判断男女，前端此处处理有问题，改用后台处理
        int genderByIdCard = IdcardUtil.getGenderByIdCard(ps01dto.getIdcardnumber());
        ps01dto.setGender(genderByIdCard == 1 ? "1" : "2");
        //处理读卡器民族问题
        if (!StringUtils.isNumeric(ps01dto.getNation())) {
            String nation = dictDataDao.getNationByLabel(ps01dto.getNation());
            ps01dto.setNation(nation);
        }
        //处理头像问题
        if (StringUtils.isNotBlank(ps01dto.getHeadimageurl())) {
            boolean file = StrUtil.startWith(ps01dto.getHeadimageurl(), IMAGE);
            if (file) {
                String imagePath = ImageUtil.base64ToImage(ps01dto.getHeadimageurl());
                ps01dto.setHeadimageurl(imagePath);
            }
        }

        // 查找当前人员是否是在人员库中
        Ps01Entity ps01 = ps01Dao.selectOne(new QueryWrapper<Ps01Entity>().eq("idcardnumber", ps01dto.getIdcardnumber()));
        if (ps01 == null) {
            ps01 = new Ps01Entity();
            BeanUtil.copyProperties(ps01dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01.setAreacode(StringUtils.isNotBlank(ps01dto.getIdcardnumber()) ? ps01dto.getIdcardnumber().substring(0, 6) : "");
            ps01.setWorkertype("1");
            ps01Dao.insert(ps01);
        } else {
            BeanUtil.copyProperties(ps01dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01Dao.updateById(ps01);
        }
        dto.setPs0101(ps01.getPs0101());
        // 写入工人人脸数据库
        FaceAddParamsVO faceAddParamsVO = new FaceAddParamsVO();
        //判断图片是否为空,图片base64字符串储存到服务器，路径保存到数据库
        String headImage = dto.getIssuecardpicurl();
        if (StringUtils.isNotBlank(headImage)) {
            // 如果这个是base64
            //更新PS02的信息
            boolean file = StrUtil.startWith(dto.getIssuecardpicurl(), IMAGE);
            if (file) {
                String imagePath = ImageUtil.base64ToImage(headImage);
                dto.setIssuecardpicurl(imagePath);
                faceAddParamsVO.setImage(headImage);
            } else {
                String imageSource = ImageUtil.netSourceToBase64(headImage);
                dto.setIssuecardpicurl(headImage);
                faceAddParamsVO.setImage(imageSource);
            }
        } else {
            dto.setIssuecardpicurl(ps01.getHeadimageurl());
            faceAddParamsVO.setImage(ImageUtil.netSourceToBase64(ps01.getHeadimageurl()));
        }
        //判断该项目下边人员是否存在
        Integer sum = baseDao.selectCountPs0101(CommonUtils.userProjectInfo().getPj0101(), ps01.getPs0101());
        if (sum > 0) {
            throw new RenException("项目中已存在该人员！请勿重复录入");
        }
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        //保存PS02的信息
        dto.setPj0101(pj0101);
        dto.setEntrytime(dto.getEntrytime() == null ? new Date() : dto.getEntrytime());
        dto.setInOrOut("1");
        //处理班组长问题
        if ("1".equals(dto.getIsteamleader())) {
            ps02Dao.updateTeamleader(dto.getTm0101());
            Tm01Entity tm01 = tm01Dao.selectById(dto.getTm0101());
            tm01.setResponsiblepersonname(dto.getPs01DTO().getName());
            tm01.setResponsiblepersonidnumber(dto.getPs01DTO().getIdcardnumber());
            tm01.setResponsiblepersonphone(dto.getPs01DTO().getCellphone());
            tm01Dao.updateById(tm01);
        }
        ps02Service.save(dto);

        faceAddParamsVO.setPs0201(dto.getPs0201());

        //保存进退场信息
        BPs06Entity ps06Entity = new BPs06Entity();
        ps06Entity.setEntryOrExitTime(DateUtil.parse(DateUtil.today())).setInOrOut("1").setPs0201(dto.getPs0201());
        ps06Dao.insert(ps06Entity);

        // 附件
        ot01Service.doFileRelation(dto.getOt01DTOList(), dto.getPs0201());

        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getSpecialWorkCertificateFiles())) {
            ot01Service.doFileRelation(dto.getSpecialWorkCertificateFiles(), dto.getPs0201());
        }

        List<PersonDTO> personDTOList = this.getPersonIntoDevices(dto.getPs0201(), ps01.getName(), dto.getIssuecardpicurl());
        //人员注册、下发到设备
        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.WORKER_TYPE);

        faceAddParamsVO.setProjectId(String.valueOf(pj0101));
        faceAddParamsVO.setInOrOut(dto.getInOrOut());
        faceAddParamsVO.setAreacode(CommonUtils.userProjectInfo().getAreacode());
        iPs02FaceService.faceFeatureAdd(faceAddParamsVO);
    }

    @Override
    public void newSave(Ps02DTO dto) {
        //查询人员基础信息
        Ps01Entity ps01Entity = ps01Dao.selectById(dto.getPs0101());
        //根据身份证倒数第二位判断男女，前端此处处理有问题，改用后台处理
        int genderByIdCard = IdcardUtil.getGenderByIdCard(ps01Entity.getIdcardnumber());
        ps01Entity.setGender(genderByIdCard == 1 ? "1" : "2");
        //判断该项目下边人员是否存在
        Integer sum = baseDao.selectCountPs0101(CommonUtils.userProjectInfo().getPj0101(), ps01Entity.getPs0101());
        if (sum > 0) {
            throw new RenException("项目中已存在该人员！请勿重复添加");
        }
        //保存PS02的信息
        dto.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        dto.setPs0101(ps01Entity.getPs0101());
        dto.setEntrytime(DateUtil.parse(DateUtil.today()));
        dto.setInOrOut("1");
        ps02Service.save(dto);
        //保存进退场信息
        BPs06Entity ps06Entity = new BPs06Entity();
        ps06Entity.setEntryOrExitTime(DateUtil.parse(DateUtil.today())).setInOrOut("1").setPs0201(dto.getPs0201());
        ps06Dao.insert(ps06Entity);
        //人员注册、下发到设备
//        kq05Service.saveCreatePerson(CommonUtils.userProjectInfo().getPj0101(), dto.getPs0201(), ps01Entity.getName(), filePath + ps01Entity.getHeadimageurl(), PERSON_TYPE);
    }

    @Override
    public void updatePs02Info(Ps02DTO dto) {
        Pj01Entity pj01 = pj01Dao.selectById(dto.getPj0101());
        Ps02Entity ps02 = ps02Dao.selectById(dto.getPs0201());
        Long tm0101 = ps02.getTm0101();
        //更新PS01信息
        int genderByIdCard = IdcardUtil.getGenderByIdCard(dto.getPs01DTO().getIdcardnumber());
        dto.getPs01DTO().setGender(genderByIdCard == 1 ? "1" : "2");
        ps01Service.update(dto.getPs01DTO());
        //更新PS02的信息
        boolean file = StrUtil.startWith(dto.getIssuecardpicurl(), IMAGE);
        // 写入工人人脸数据库
        FaceAddParamsVO faceAddParamsVO = new FaceAddParamsVO();
        //判断头像是否文件，如果不是默认为base64字符串，为修改了头像
        if (file) {
            faceAddParamsVO.setImage(dto.getIssuecardpicurl());
            String headImage = ImageUtil.base64ToImage(dto.getIssuecardpicurl());
            dto.setIssuecardpicurl(headImage);
            List<PersonDTO> personDTOS = ps02Dao.selectPersonByIds(CollectionUtil.toList(dto.getPs0201()));
            supDeviceTaskService.personIntoDevices(personDTOS, CommonConstants.PERSON_INTO, CommonConstants.WORKER_TYPE);
        } else {
            faceAddParamsVO.setImage(ImageUtil.netSourceToBase64(dto.getIssuecardpicurl()));
        }
        //处理班组长问题
        if ("1".equals(dto.getIsteamleader())) {
            ps02Dao.updateTeamleader(dto.getTm0101());
            Tm01Entity tm01 = tm01Dao.selectById(dto.getTm0101());
            tm01.setResponsiblepersonname(dto.getPs01DTO().getName());
            tm01.setResponsiblepersonidnumber(dto.getPs01DTO().getIdcardnumber());
            tm01.setResponsiblepersonphone(dto.getPs01DTO().getCellphone());
            tm01Dao.updateById(tm01);
        }
        ps02Service.update(dto);
        faceAddParamsVO.setIsAdd(false);
        faceAddParamsVO.setPs0201(dto.getPs0201());
        faceAddParamsVO.setProjectId(String.valueOf(dto.getPj0101()));
        iPs02FaceService.faceFeatureAdd(faceAddParamsVO);
        if (!"0".equals(pj01.getStupstatus())) {
            if ("1".equals(ps02.getInOrOut())) {
                if (!tm0101.equals(dto.getTm0101())) {
                    throw new RenException("已上报省厅，不允许修改所属班组！请退场后修改重新入场！");
                }
            }
        }
        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getSpecialWorkCertificateFiles())) {
            ot01Service.updateUnlessFiles("13", dto.getPs0201());
            ot01Service.doFileRelation(dto.getSpecialWorkCertificateFiles(), dto.getPs0201());
        }
    }

    @Override
    public Ps02DTO getPs02Info(Long id) {
        Ps02DTO ps02DTO = ps02Service.get(id);
        //ps01的基础信息
        Ps01DTO ps01Info = ps01Service.getPs01Info(ps02DTO.getPs0101());
        ps02DTO.setPs01DTO(ps01Info);
        ps02DTO.setIssuecardpicurl(ps02DTO.getIssuecardpicurl());
        //合同信息
        BPs08DTO ps08dto = ps08Dao.getWorkerContract(id);
        //合同附件
//        if (BeanUtil.isNotEmpty(ps08dto)) {
//            List<Ot01DTO> ot01DTOList = ot01Service.loadBusinessData(id, "10");
//            ps08dto.setOt01DTOList(ot01DTOList);
//        }
        List<Ot01DTO> ot01DTOList = ot01Service.loadBusinessData(id, "10");
        if (BeanUtil.isEmpty(ps08dto)) {
            ps08dto = new BPs08DTO();
            ps08dto.setOt01DTOList(ot01DTOList);
        }

        if (ObjectUtil.isNotNull(ps02DTO)) {
            List<Ot01DTO> ot01DTOS = ot01Service.loadBusinessData(ps02DTO.getPs0201(), "13");
            ps02DTO.setSpecialWorkCertificateFiles(ot01DTOS);
        }

        ps02DTO.setPs08DTO(ps08dto);
        return ps02DTO;
    }

    @Override
    public void updateHeadImage(Map<String, Object> params) {
        Long ps0201 = Long.valueOf(params.get("ps0201").toString());
        Ps02Entity ps02 = ps02Dao.selectById(ps0201);
        Ps01Entity ps01 = ps01Dao.selectById(ps02.getPs0101());
        Kq05Entity kq05 = kq05Dao.selectOne(new QueryWrapper<Kq05Entity>().eq("USER_ID", ps0201).eq("PERSON_TYPE", "1").eq("CMD_CODE", "ADD_PERSON_IMAGE"));
        kq05.setParams(filePath + ps01.getHeadimageurl());
        kq05Dao.updateById(kq05);
    }


    @Override
    public Result contractUpload(Ps02FileUploadVO fileUploadVO) {
//        // 如果未签订合同那么将不能上传成功
//        Integer ps08Count = ps08Dao.selectCount(new QueryWrapper<BPs08Entity>()
//                .eq("ps0201", fileUploadVO.getPs0201()));
//        if (ps08Count == 0) {
//
//            throw new RenException("上传失败,当前人员未在线签订合同");
//        }
        ot01Service.doFileRelation(fileUploadVO.getOt01DTOList(), Long.valueOf(fileUploadVO.getPs0201()));
        return new Result().ok("上传成功");
    }

    @Override
    public Result getContractUpload(String ps0201) {

        return new Result().ok(ot01Service.loadBusinessData(Long.valueOf(ps0201), Constant.FileType.WORKERCONTRACT.Value()));
    }

    @Override
    public Result getPromiseLetter(String ps0201) {

        return new Result().ok(ot01Service.loadBusinessData(Long.valueOf(ps0201), Constant.FileType.CONTRACTPROMISE.Value()));
    }

    @Override
    public void exitPerson(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        baseDao.updateInOrOutByIds(longs, "2");

        // 进退场记录
        bPs06Service.batchInsertInOrOut(longs, "2");
        List<PersonDTO> personDTOList = ps02Dao.selectPersonByIds(longs);

        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_DELETE, CommonConstants.WORKER_TYPE);
        ;
    }

    // 进场
    @Override
    public void enterPerson(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        baseDao.updateInOrOutByIds(longs, "1");
        if (ids.length > 0) {
            Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
            Pj01Entity pj01 = pj01Dao.selectById(pj0101);
            if (!"0".equals(pj01.getStupstatus())) {
                for (Long id : ids) {
                    Ps02Entity ps02 = baseDao.selectById(id);
                    Tm01Entity tm01 = tm01Dao.selectById(ps02.getTm0101());
                    Cp02Entity cp02 = cp02Dao.selectById(tm01.getCp0201());
                    Cp01Entity cp01 = cp01Dao.selectById(cp02.getCp0101());
                    Ps01Entity ps01 = ps01Dao.selectById(ps02.getPs0101());
                    //写入头像上报表
                    Map<String, Object> headImageParams = new HashMap<>();
                    Long att_id = ps02Dao.selectSeqAttInfo();
                    headImageParams.put("ATT_ID", att_id);
                    headImageParams.put("ATT_NAME", ps01.getPs0101() + ps02.getPs0201());
                    headImageParams.put("FILE_PATH", ps02.getIssuecardpicurl());
                    ps02Dao.insertAttInfo(headImageParams);
                    //写入建筑工人上报表
                    Map<String, Object> workerParams = new HashMap<>();
                    workerParams.put("PJ0101", pj01.getPj0101());
                    workerParams.put("CORP_NAME", cp01.getCorpname());
                    workerParams.put("CORP_CODE", cp01.getCorpcode());
                    workerParams.put("TEAM_SYS_NO", tm01.getTeamsysno());
                    workerParams.put("TEAM_NAME", tm01.getTeamname());
                    workerParams.put("WORKER_NAME", ps01.getName());
                    workerParams.put("IS_TEAM_LEADER", "1".equals(ps02.getIsteamleader()) ? "是" : "否");
                    workerParams.put("ID_CARD_NUMBER", ps01.getIdcardnumber());
                    if (IdcardUtil.isValidCard(ps01.getIdcardnumber())) {
                        workerParams.put("AGE", IdcardUtil.getAgeByIdCard(ps01.getIdcardnumber()));
                    } else {
                        workerParams.put("AGE", "30");
                    }
                    workerParams.put("GENDER", "1".equals(ps01.getGender()) ? "男" : "女");
                    workerParams.put("NATION", ps01.getNation());
                    workerParams.put("ADDRESS", ps01.getAddress());
                    workerParams.put("HEAD_IMAGE", att_id);
                    workerParams.put("POLITICS_TYPE", ps01.getPoliticstype());
                    workerParams.put("CULTURE_LEVEL_TYPE", ps01.getCultureleveltype());
                    workerParams.put("GRANT_ORG", ps01.getGrantorg());
                    workerParams.put("WORK_TYPE", ps02.getWorktypecode());
                    workerParams.put("NATIVE_PLACE", ps01.getAreacode());
                    workerParams.put("MOBILE", ps01.getCellphone());
                    workerParams.put("BUSIID", ps02.getPs0201());
                    ps02Dao.insertProjectWorkerInfo(workerParams);
                    //写入人员进场数据上报表
                    Map<String, Object> inoutParams = new HashMap<>();
                    inoutParams.put("PJ0101", pj01.getPj0101());
                    inoutParams.put("ID_CARD_NUMBER", ps01.getIdcardnumber());
                    inoutParams.put("OCCUR_TIME", new Date());
                    inoutParams.put("BUSIID", ps02.getPs0201());
                    ps02Dao.insertProjectWorkerInoutInfo(inoutParams);
                }
            }
        }
        // 进退场记录
        bPs06Service.batchInsertInOrOut(longs, "1");
        // 下发设备
        List<PersonDTO> personDTOList = ps02Dao.selectPersonByIds(longs);
        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.WORKER_TYPE);
    }

    @Override
    public void deviceAddPerson(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        List<PersonDTO> person = baseDao.selectPersonByIds(longs);
        supDeviceTaskService.personIntoDevices(person, CommonConstants.PERSON_INTO, CommonConstants.WORKER_TYPE);
    }

    @Override
    public Result contractPromiseUpload(Ps02FilePromiseUploadVO fileUploadVO) {

        ot01Service.doFileRelation(fileUploadVO.getOt01DTOList(), Long.valueOf(fileUploadVO.getPs0201()));
        return new Result().ok("上传成功");
    }

    @Override
    public void downloadPromiseLetter(HttpServletResponse response) {

        FileDownloadUtil.downloadFile(response, PROMISE_EXPORT);
    }

    /**
     * 添加到设备
     *
     * @param ids
     */
    @Override
    public void deviceIntoPerson(Long[] ids) {

        if (ArrayUtil.isEmpty(ids)) {
            return;
        }
        // 获取需要下发人员信息
        personIntoOrOutDevice(Arrays.asList(ids), CommonConstants.PERSON_INTO);

    }

    /**
     * 下发设备
     *
     * @param longs
     * @param type  类型
     */
    private void personIntoOrOutDevice(List<Long> longs, short type) {
        List<PersonDTO> personDTOList = baseDao.selectPersonByIds(longs);
        // 获取当前项目下的设备信息
        List<SupDeviceEntity> supDeviceEntityList = supDeviceDao.selectList(new QueryWrapper<SupDeviceEntity>()
                .eq("pj0101", CommonUtils.userProjectInfo().getPj0101())
                .eq("status", CommonConstants.DEVICE_PASS)
        );
        // 写入任务表
        // 单次任务上限设置10条
        for (SupDeviceEntity supDeviceEntity : supDeviceEntityList) {

            SupDeviceTaskEntity supDeviceTaskEntity = new SupDeviceTaskEntity();
            supDeviceTaskEntity.setDeviceKey(supDeviceEntity.getSn());
            supDeviceTaskEntity.setType(type);
            supDeviceTaskDao.insert(supDeviceTaskEntity);
            for (PersonDTO person : personDTOList) {

                SupDeviceTaskDetailEntity detailEntity = new SupDeviceTaskDetailEntity();
                detailEntity.setUserId(person.getUserId());
                detailEntity.setTaskId(supDeviceTaskEntity.getId());
                detailEntity.setPhoto(person.getImageUrl());
                detailEntity.setUserType(CommonConstants.WORKER_TYPE);
                detailEntity.setName(person.getName());
                supDeviceTaskDetailDao.insert(detailEntity);
            }
        }
    }

    @Override
    public PageData<Ps02EmpRecordDTO> empRecordPageList(Map<String, Object> params) {
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<Ps02EmpRecordDTO> list = baseDao.getEmpRecordListData(params);
        return getPageData(list, page.getTotal(), Ps02EmpRecordDTO.class);
    }

    @Override
    public BPs08Entity previewUserContract(String userId) {

        if (StringUtils.isBlank(userId)) {
            throw new RenException("请选择需要查看的人员");
        }
        // 如果ps08有数据则是ps0801否则是ps0201
        Long aLong = Long.valueOf(userId);
        // 判断在线合同表中是否有信息
        Integer ps08Count = ps08Dao.selectCount(new QueryWrapper<BPs08Entity>()
                .eq("ps0801", aLong));

        if (ps08Count > 0) {
            BPs08Entity ps08 = ps08Dao.selectById(aLong);
            return ps08;
        }
        //查询工人预览合同数据
        Ps02Entity ps02 = ps02Dao.selectById(aLong);
        Ps01Entity ps01 = ps01Dao.selectById(ps02.getPs0101());
        //查询甲方信息
        BPs08Entity ps08A = ps08Dao.selectContractA(ps02.getTm0101());
        //查询乙方信息
        BPs08Entity ps08B = ps08Dao.selectContractB(aLong);
        //项目信息
        Pj01Entity pj01 = pj01Dao.selectById(ps02.getPj0101());
        //甲方乙方信息合并
        BeanUtil.copyProperties(ps08B, ps08A, CopyOptions.create().setIgnoreNullValue(true));
        ps08A.setContractno(IdWorker.getId() + "");
        ps08A.setBirthday(IdcardUtil.getBirthDate(ps01.getIdcardnumber()));
        ps08A.setPj0101(ps02.getPj0101());
        ps08A.setWorkeraddress(pj01.getAddress());
        return ps08A;
    }

    @Override
    public void saveUserContract(BPs08DTO bPs08DTO) {
        if (Objects.isNull(bPs08DTO)) {
            throw new RenException("提交当前数据为空!");
        }
        if (bPs08DTO.getPs0801() != null) {
            List<Ot01Entity> ot01s = ot01Dao.selectList(new QueryWrapper<Ot01Entity>().eq("BUSITYPE", Constant.FileType.WORKERCONTRACT.Value()).eq("BUSISYSNO", bPs08DTO.getPs0801()));
            if (ot01s.size() > 0) {
                throw new RenException("该工人合同已上传，不允许修改！");
            }
        }
        BPs08Entity bPs08 = new BPs08Entity();
        Integer ps08Count = ps08Dao.selectCount(
                new QueryWrapper<BPs08Entity>().eq("ps0201", bPs08DTO.getPs0201()));
        BeanUtils.copyProperties(bPs08DTO, bPs08);
        if (ps08Count == 0) {
            ps08Service.save(bPs08);
        } else {
            ps08Service.update(bPs08, new UpdateWrapper<BPs08Entity>()
                    .eq("ps0201", bPs08DTO.getPs0201()));
        }
    }

    @Override
    public void exportUserContract(Long[] ids, HttpServletResponse response) {
        // 校验是否以上人员都签订了合同
        OutputStream os = null;
        List<String> files = new ArrayList();
        File file = new File(CONTRACT_EXPORT);
        if (!file.exists()) {
            throw new RenException("未找到建筑工人劳动合同书模板");
        }
        Pj01Entity pj01 = pj01Dao.selectById(CommonUtils.userProjectInfo().getPj0101());
        String parentPath = file.getParent() + "/contract/" + pj01.getName();
        File dir = new File(parentPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        try {
            for (Long ps0201 : ids) {
                BPs08Entity bPs08Entity = ps08Dao.selectOne(new QueryWrapper<BPs08Entity>()
                        .eq("ps0201", ps0201));
                if (Objects.isNull(bPs08Entity)) {
                    throw new RenException("请勿选择未签订合同的人员");
                }
                //生成合同唯一二维码
                byte[] bytes = QrCodeUtil.generatePng(bPs08Entity.getContractno().toString(), 100, 100);
                ImageEntity image = new ImageEntity();
                image.setHeight(100);
                image.setWidth(100);
                image.setData(bytes);
                image.setType(ImageEntity.Data);
                // 填充参数
                Map<String, Object> map = new HashMap<>();
                map.put("qrcode", image);
                map.put("corpname", StringUtils.isBlank(bPs08Entity.getCorpname()) ? "   " : bPs08Entity.getCorpname());
                map.put("legalman", StringUtils.isBlank(bPs08Entity.getLegalman()) ? "   " : bPs08Entity.getLegalman());
                map.put("corpaddress", StringUtils.isBlank(bPs08Entity.getCorpaddress()) ? "   " : bPs08Entity.getCorpaddress());
                map.put("name", StringUtils.isBlank(bPs08Entity.getName()) ? "   " : bPs08Entity.getName());
                map.put("gender", StringUtils.isBlank(bPs08Entity.getGender()) ? "   " : bPs08Entity.getGender());
                map.put("birthday", DateUtil.format(bPs08Entity.getBirthday(), "yyyy-MM-dd"));
                map.put("address", StringUtils.isBlank(bPs08Entity.getAddress()) ? "   " : bPs08Entity.getAddress());
                map.put("idcardnumber", StringUtils.isBlank(bPs08Entity.getIdcardnumber()) ? "   " : bPs08Entity.getIdcardnumber());
                map.put("cellphone", StringUtils.isBlank(bPs08Entity.getCellphone()) ? "   " : bPs08Entity.getCellphone());
                map.put("corpcode", StringUtils.isBlank(bPs08Entity.getCorpcode()) ? "   " : bPs08Entity.getCorpcode());
                map.put("linkcellphone", StringUtils.isBlank(bPs08Entity.getLinkcellphone()) ? "   " : bPs08Entity.getLinkcellphone());
                map.put("projectname", StringUtils.isBlank(bPs08Entity.getProjectname()) ? "   " : bPs08Entity.getProjectname());
                map.put("workirregular", StringUtils.isBlank(bPs08Entity.getWorkirregular()) ? "   " : bPs08Entity.getWorkirregular());
                map.put("worktypecode", StringUtils.isBlank(bPs08Entity.getWorktypecode()) ? "   " : bPs08Entity.getWorktypecode());
                if (StringUtils.isNotBlank(bPs08Entity.getContractbegin())) {
                    String begintime[] = bPs08Entity.getContractbegin().split("-");
                    map.put("beginyear", begintime[0]);
                    map.put("beginmonth", begintime[1]);
                    map.put("beginday", begintime[2]);
                }
                map.put("duty", StringUtils.isBlank(bPs08Entity.getDuty()) ? "   " : bPs08Entity.getDuty());
                map.put("salarypaymentmethod", StringUtils.isBlank(bPs08Entity.getSalarypaymentmethod()) ? "   " : bPs08Entity.getSalarypaymentmethod());
                map.put("timewage", StringUtils.isBlank(bPs08Entity.getTimewage()) ? "   " : bPs08Entity.getTimewage());
                map.put("prjname", StringUtils.isBlank(bPs08Entity.getPrjname()) ? "   " : bPs08Entity.getPrjname());
                map.put("workload", StringUtils.isBlank(bPs08Entity.getWorkload()) ? "   " : bPs08Entity.getWorkload());
                map.put("workloadwage", StringUtils.isBlank(bPs08Entity.getWorkloadwage()) ? "   " : bPs08Entity.getWorkloadwage());
                map.put("otherwage", StringUtils.isBlank(bPs08Entity.getOtherwage()) ? "   " : bPs08Entity.getOtherwage());
                map.put("payday", StringUtils.isBlank(bPs08Entity.getPayday()) ? "   " : bPs08Entity.getPayday());
                map.put("workhoursystem", StringUtils.isBlank(bPs08Entity.getWorkhoursystem()) ? "   " : bPs08Entity.getWorkhoursystem());
                map.put("workhour", StringUtils.isBlank(bPs08Entity.getWorkhour()) ? "   " : bPs08Entity.getWorkhour());
                map.put("workday", StringUtils.isBlank(bPs08Entity.getWorkday()) ? "   " : bPs08Entity.getWorkday());
                map.put("welfare", StringUtils.isBlank(bPs08Entity.getWelfare()) ? "   " : bPs08Entity.getWelfare());
                map.put("otherthings", StringUtils.isBlank(bPs08Entity.getOtherthings()) ? "   " : bPs08Entity.getOtherthings());
                map.put("consignor", StringUtils.isBlank(bPs08Entity.getConsignor()) ? "   " : bPs08Entity.getConsignor());
                if (StringUtils.isNotBlank(bPs08Entity.getSigndate())) {
                    String signtime[] = bPs08Entity.getSigndate().split("-");
                    map.put("signyear", signtime[0]);
                    map.put("signmonth", signtime[1]);
                    map.put("signday", signtime[2]);
                }
                XWPFDocument doc = WordExportUtil.exportWord07(CONTRACT_EXPORT, map);
                String fileName = parentPath + "/" + bPs08Entity.getName() + "-" + bPs08Entity.getIdcardnumber() + ".docx";
                os = new FileOutputStream(fileName);
                files.add(fileName);
                doc.write(os);
                doc.close();
            }
            os.flush();
        } catch (Exception e) {
            log.error(e.toString());
            throw new RenException(e.getMessage());
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        CommonUtils.writePdfZip(files, pj01.getName() + "工人合同", response);
    }


    @Override
    public PageData<Ps02ExportDTO> exportRosterPage(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<Ps02ExportDTO> list = baseDao.exportRosterPage(params);
        return getPageData(list, page.getTotal(), Ps02ExportDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchExit(String[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return;
        }
        List<String> longs = Arrays.asList(ids);
        baseDao.batchExit(longs);

        List<Long> resultList = longs.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<PersonDTO> personDTOList = ps02Dao.selectPersonByIds(resultList);

        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_DELETE, CommonConstants.WORKER_TYPE);
    }

    @Override
    public void exportRoster(Map<String, Object> params, HttpServletResponse response) throws IOException {
        File file = new File(ROSTER_EXPORT);
        if (!file.exists()) {
            throw new IOException("配置的模板文件不存在");
        }
        //查询导出数据
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        List<Ps02ExportDTO> list = baseDao.exportRosterPage(params);
        TemplateExportParams exportParams = new TemplateExportParams(ROSTER_EXPORT);
        Map<String, Object> data = new HashMap<>();
        String name = CommonUtils.userProjectInfo().getName();
        String exportday = DateUtil.format(new Date(), "yyyy年MM月dd日");
        //查询人员进退场概述
//        String memo = baseDao.selectMemo(params);
        data.put("name", name);
//        data.put("memo", memo);
        data.put("exportday", exportday);
        data.put("list", list);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(exportday + "-" + name + "-" + "-工人花名册", "UTF-8") + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    /**
     * 批量导入工人合同信息
     *
     * @param request
     * @param response
     * @return
     */
    @Override
    public Result batchImportContract(MultipartHttpServletRequest request, HttpServletResponse response) {
        MultiValueMap<String, MultipartFile> map = request.getMultiFileMap();
        List<MultipartFile> list = map.get("file");
        Map<String, Object> msgData = new HashMap<>();

        if (list.size() > 50) {
            throw new RenException("单次批量上传不能大于50份");
        }
        if (CollectionUtil.isNotEmpty(list)) {
            int count = 0;
            StringBuilder sb = new StringBuilder();
            Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
            StringBuilder resBuilder = new StringBuilder();

            for (MultipartFile multipartFile : list) {
                String fileName = multipartFile.getOriginalFilename();

                if (StringUtils.isBlank(fileName) || !fileName.contains("-")) {
                    appendErrorMessage(sb, fileName + "格式不正确，请重新选择上传！<br/>");
                    count++;
                    continue;
                }

                String[] fileData = fileName.split("-");
                String userName = getSafeStringValue(fileData, 0);
                String idcardnumber = getSafeStringValue(fileData, 1);

                Ps02BatchImportContractDTO batchImportContract = baseDao.getBatchImportContract(idcardnumber, pj0101);

                if (ObjectUtil.isNull(batchImportContract)) {
                    appendErrorMessage(sb, "【" + userName + "】工人在本项目下不存在<br/>");
                    count++;
                    continue;
                }

                // 上传文件并且保存
                uploadFile(multipartFile, batchImportContract.getUserId());
            }

            if (count > 0) {
                appendErrorMessage(resBuilder, "本次上传共有【" + count + "】份上传失败，原因如下：<br/>");
                msgData.put("code", 500);
            } else {
                appendErrorMessage(resBuilder, "上传成功");
                msgData.put("code", 0);
            }

            msgData.put("message", resBuilder.append(sb).toString());
        }

        return new Result().ok(msgData);
    }

    /**
     * 获取工人进退场列表
     *
     * @param params
     * @return
     */
    @Override
    public PageData<Ps02InOrOutDTO> getWorkerInOrOutData(Map<String, Object> params) {
        IPage<Ps02Entity> page = getPage(params, "", false);
        IPage<Ps02InOrOutDTO> workerInOrOutData = baseDao.getWorkerInOrOutData(page, params);
        return getPageData(workerInOrOutData, Ps02InOrOutDTO.class);
    }

    /**
     * 获取当前项目工人超龄数量（男大于60，女大于55）
     *
     * @return
     */
    @Override
    public String getWorkerOverageNum() {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        return baseDao.getWorkerOverageNum(pj0101);
    }

    /**
     * 获取工人超龄人员
     *
     * @param params
     * @return
     */
    @Override
    public PageData<Ps02WorkerOverageDTO> getWorkerOveragePage(Map<String, Object> params) {
        IPage<Ps02Entity> page = getPage(params, "", false);
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<Ps02WorkerOverageDTO> workerOverageDTOIPage = baseDao.getWorkerOveragePage(page, params);
        return getPageData(workerOverageDTOIPage, Ps02WorkerOverageDTO.class);
    }

    @Override
    public Ps02Entity getByIdcardnumberAndName(String idcardnumber, String name) {
        return baseDao.getByIdcardnumberAndName(idcardnumber, name);
    }

    /**
     * 拼接异常消息
     *
     * @param builder
     * @param message
     */
    private void appendErrorMessage(StringBuilder builder, String message) {
        builder.append(" ").append(message);
    }

    /**
     * 获取文件信息
     *
     * @param array
     * @param index
     * @return
     */
    private String getSafeStringValue(String[] array, int index) {
        if (array != null && array.length > index) {
            String value = array[index];

            // 处理文件后缀
            int dotIndex = value.lastIndexOf('.');
            if (dotIndex > 0) {
                value = value.substring(0, dotIndex);
            }

            return value;
        } else {
            return null;
        }
    }

    /**
     * 上传文件
     *
     * @param file
     * @param userId 人员id
     * @return 业务id
     */
    private void uploadFile(MultipartFile file, String userId) {

        ot01Service.uploadFile(file, "10", FileNameUtil.getSuffix(file.getOriginalFilename()), userId);
    }

    private List<PersonDTO> getPersonIntoDevices(Long userId, String name, String imageUrl) {
        ArrayList<PersonDTO> personDTOList = new ArrayList<>();
        PersonDTO personDTO = new PersonDTO();
        personDTO.setUserId(userId);
        personDTO.setName(name);
        personDTO.setImageUrl(imageUrl);
        personDTOList.add(personDTO);
        return personDTOList;
    }
}