package io.renren.modules.enterprise.rpj01dept.service;

import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.rpj01dept.dto.InsertRpdDTO;
import io.renren.modules.enterprise.rpj01dept.dto.Pj0101NameDTO;
import io.renren.modules.enterprise.rpj01dept.dto.RPj01DeptDTO;
import io.renren.modules.enterprise.rpj01dept.entity.RPj01DeptEntity;
import java.util.List;

/**
 * 项目和机构的关系表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-11
 */
public interface RPj01DeptService extends CrudService<RPj01DeptEntity, RPj01DeptDTO> {
    /**
     * 添加项目和机构的关系
     */
    Result insertByInsertRpdDTO(InsertRpdDTO dto);

    /**
     * 获取项目名称和id
     */
    List<Pj0101NameDTO> getPj0101List();

    /**
     * 获取此机构下的所有项目id
     */
    List<String> getOwnPj0101List(Long deptId);


}