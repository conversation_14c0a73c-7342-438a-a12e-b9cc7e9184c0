package io.renren.modules.enterprise.pw02.excel;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class Pw02ImportExcel implements IExcelDataModel, IExcelModel {
    @Excel(name = "工资表天数")
    @NotNull(message = "不能为空！")
    @Max(value = 31, message = "最大为31天！")
    @Min(value = 1, message = "不能小于0！")
    private Integer kqts;
    @Excel(name = "应发工资")
    private double yfgz;
    @Excel(name = "银行卡号")
    @NotNull(message = "不能为空！")
    private String kh;
    @Excel(name = "开户行")
    @NotNull(message = "不能为空！")
    private String khh;
    @Excel(name = "实发工资")
    @NotNull(message = "不能为空！")
    @Min(value = 0, message = "不能小于等于0！")
    private double sfgz;
    @Excel(name = "身份证号")
    @NotNull(message = "不能为空！")
    private String idcardnumber;
    @Excel(name = "姓名")
    @NotNull(message = "不能为空！")
    private String name;
    @Excel(name = "工资所属年月")
    private Integer month;
    @Excel(name = "手机号")
    @NotNull(message = "不能为空！")
    private String mobile;
    @Excel(name = "补录说明")
    private String reason;
    @Excel(name = "系统考勤天数")
    private Integer xtkqts;

    /**
     * 行号
     */
    private int rowNum;

    /**
     * 错误消息
     */
    private String errorMsg;

    @Override
    public Integer getRowNum() {
        return rowNum;
    }

    @Override
    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
