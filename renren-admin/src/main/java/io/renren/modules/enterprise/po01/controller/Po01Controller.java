package io.renren.modules.enterprise.po01.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.po01.dto.Po01DTO;
import io.renren.modules.enterprise.po01.excel.Po01Excel;
import io.renren.modules.enterprise.po01.service.Po01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 用油管理
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("enterprise/po01")
@Api(tags="用油管理")
public class Po01Controller {
    @Autowired
    private Po01Service po01Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:po01:page")
    public Result<PageData<Po01DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Po01DTO> page = po01Service.page(params);

        return new Result<PageData<Po01DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:po01:info")
    public Result<Po01DTO> get(@PathVariable("id") Long id){
        Po01DTO data = po01Service.get(id);

        return new Result<Po01DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:po01:save")
    public Result save(@RequestBody Po01DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);
        dto.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        po01Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:po01:update")
    public Result update(@RequestBody Po01DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        po01Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:po01:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        po01Service.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:po01:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Po01DTO> list = po01Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Po01Excel.class);
    }

}