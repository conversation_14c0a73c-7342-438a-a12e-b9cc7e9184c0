package io.renren.modules.enterprise.ps03.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps03.dto.Ps03PageDTO;
import io.renren.modules.enterprise.ps03.entity.Ps03Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 管理人员在职信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@Mapper
public interface Ps03Dao extends BaseDao<Ps03Entity> {

    List<Ps03PageDTO> getListData(Map<String, Object> params);
}