package io.renren.modules.enterprise.ps04audit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.ps04audit.dao.Ps04AuditDao;
import io.renren.modules.enterprise.ps04audit.dto.Ps04AuditDTO;
import io.renren.modules.enterprise.ps04audit.entity.Ps04AuditEntity;
import io.renren.modules.enterprise.ps04audit.service.Ps04AuditService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 管理人员审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-02
 */
@Service
public class Ps04AuditServiceImpl extends CrudServiceImpl<Ps04AuditDao, Ps04AuditEntity, Ps04AuditDTO> implements Ps04AuditService {

    @Override
    public QueryWrapper<Ps04AuditEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps04AuditEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}