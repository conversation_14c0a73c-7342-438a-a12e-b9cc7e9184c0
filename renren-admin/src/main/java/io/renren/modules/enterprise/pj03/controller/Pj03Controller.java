package io.renren.modules.enterprise.pj03.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.admin.sys.entity.DictDTO;
import io.renren.modules.enterprise.pj03.dto.AddExamsDTO;
import io.renren.modules.enterprise.pj03.dto.PagePs02Params;
import io.renren.modules.enterprise.pj03.dto.Pj03DTO;
import io.renren.modules.enterprise.pj03.dto.Pj03DoConfirmDTO;
import io.renren.modules.enterprise.pj03.excel.Pj03Excel;
import io.renren.modules.enterprise.pj03.service.Pj03Service;
import io.renren.modules.enterprise.pj05.dto.Pj05DTO;
import io.renren.modules.enterprise.ps02.dto.Ps02PageDTO;
import io.renren.modules.ot01.service.Ot01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 项目培训信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("enterprise/pj03")
@Api(tags="项目培训信息")
public class Pj03Controller {
    @Autowired
    private Pj03Service pj03Service;
    @Autowired
    private Ot01Service ot01Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pj03:page")
    public Result<PageData<Pj03DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Pj03DTO> page = pj03Service.pageList(params);

        return new Result<PageData<Pj03DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("enterprise:pj03:info")
    public Result<Pj03DTO> get(@PathVariable("id") Long id){
        Pj03DTO data = pj03Service.get(id);
        data.setOt01DTOList(ot01Service.loadBusinessData(id, "08"));

        return new Result<Pj03DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
//    @RequiresPermissions("enterprise:pj03:save")
    public Result save(@RequestBody Pj03DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj03Service.savePj03Info(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
//    @RequiresPermissions("enterprise:pj03:update")
    public Result update(@RequestBody Pj03DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj03Service.updatePj03Info(dto);

        return new Result();
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    @LogOperation("删除")
//    @RequiresPermissions("enterprise:pj03:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj03Service.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
//    @RequiresPermissions("enterprise:pj03:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Pj03DTO> list = pj03Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Pj03Excel.class);
    }

    @PostMapping("pagePs02ByTm0101")
    @ApiOperation("工人分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    public Result<PageData<Ps02PageDTO>> pagePs02ByTm0101(@RequestBody PagePs02Params params){
        PageData<Ps02PageDTO> page = pj03Service.pagePs02ByTm0101(params);

        return new Result<PageData<Ps02PageDTO>>().ok(page);
    }

    @GetMapping("pagePs02")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    public Result<PageData<Ps02PageDTO>> pagePs02(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps02PageDTO> page = pj03Service.pagePs02(params);

        return new Result<PageData<Ps02PageDTO>>().ok(page);
    }

    @GetMapping("getTeamList")
    @ApiOperation("获取班组下拉框")
    public Result<List<DictDTO>> getTeamList(){
        List<DictDTO> data = pj03Service.getTeamList();

        return new Result<List<DictDTO>>().ok(data);
    }

    @PostMapping("addExams")
    @ApiOperation("添加考题")
    @LogOperation("添加考题")
    public Result addExams(@RequestBody AddExamsDTO dto){

        return pj03Service.addExams(dto);
    }

    @GetMapping("ExamInfo/{pj0301}")
    @ApiOperation("考题详情")
    @LogOperation("考题详情")
    public Result<List<Pj05DTO>> ExamInfo(@PathVariable("pj0301") Long pj0301){

        return pj03Service.examInfo(pj0301);
    }

    @PostMapping("doConfirm")
    @ApiOperation("确认培训资料")
    @LogOperation("确认培训资料")
    public Result doConfirm(@RequestBody @Validated Pj03DoConfirmDTO dto) {

        return pj03Service.doConfirm(dto);
    }
}