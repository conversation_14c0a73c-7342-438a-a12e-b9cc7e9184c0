package io.renren.modules.enterprise.rpj01dept.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目和机构的关系表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-11
 */
@Data
@ApiModel(value = "项目和机构的关系表")
public class RPj01DeptDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "机构ID")
    private Long deptId;

}