package io.renren.modules.enterprise.pj08.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目竣工验收信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ08")
public class Pj08Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long pj0801;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 工程名称
     */
	private String prjname;
    /**
     * 竣工验收编号
     */
	private String prjfinishchecknum;
    /**
     * 竣工验收日期
     */
	private Date edate;
    /**
     * 申报状态(dic)
     */
	private String finishstatus;
	/**
	 * 项目状态(dic)
	 */
	private String prjstatus;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}