package io.renren.modules.enterprise.ps03audit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.ps03audit.dao.Ps03AuditDao;
import io.renren.modules.enterprise.ps03audit.dto.Ps03AuditDTO;
import io.renren.modules.enterprise.ps03audit.entity.Ps03AuditEntity;
import io.renren.modules.enterprise.ps03audit.service.Ps03AuditService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-28
 */
@Service
public class Ps03AuditServiceImpl extends CrudServiceImpl<Ps03AuditDao, Ps03AuditEntity, Ps03AuditDTO> implements Ps03AuditService {

    @Override
    public QueryWrapper<Ps03AuditEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps03AuditEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}