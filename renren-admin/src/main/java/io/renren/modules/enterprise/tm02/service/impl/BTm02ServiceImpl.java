package io.renren.modules.enterprise.tm02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.tm02.dao.BTm02Dao;
import io.renren.modules.enterprise.tm02.dto.BTm02DTO;
import io.renren.modules.enterprise.tm02.entity.BTm02Entity;
import io.renren.modules.enterprise.tm02.service.BTm02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 班组进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-26
 */
@Service
public class BTm02ServiceImpl extends CrudServiceImpl<BTm02Dao, BTm02Entity, BTm02DTO> implements BTm02Service {

    @Override
    public QueryWrapper<BTm02Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<BTm02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

}