package io.renren.modules.enterprise.ps11.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员/工人预约进场
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS11")
public class Ps11Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * id
     */
	private Long ps1101;
    /**
     * 项目Id
     */
	private Long pj0101;
    /**
     * 基础人员信息
     */
	private Long ps0101;
    /**
     * 班组（人员类型为工人才写入）
     */
	private Long tm0101;
    /**
     * 参见单位（人员类型为管理人员才写入）
     */
	private Long cp0201;
    /**
     * $column.comments
     */
	private String worktypecode;
    /**
     * 1工人 2管理人员
     */
	private Integer persontype;
    /**
     * 进场时间
     */
	private Date entrydate;
    /**
     * 0未处理1已处理
     */
	private Integer status;
    /**
     * 岗位类型（类型为管理人员才写入）
     */
	private String jobtype;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
}