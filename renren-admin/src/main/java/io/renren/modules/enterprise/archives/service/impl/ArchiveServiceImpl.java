package io.renren.modules.enterprise.archives.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.enterprise.archives.dao.ArchiveDao;
import io.renren.modules.enterprise.archives.dto.ArchiveInfo;
import io.renren.modules.enterprise.archives.dto.PartUnitPage;
import io.renren.modules.enterprise.archives.dto.PersonPage;
import io.renren.modules.enterprise.archives.dto.TeamPage;
import io.renren.modules.enterprise.archives.service.ArchiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ArchiveServiceImpl extends BaseServiceImpl<ArchiveDao, ArchiveInfo> implements ArchiveService {
    @Autowired
    private ArchiveDao archiveDao;


    @Override
    public PageData<PersonPage> personPageList(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<PersonPage> list = archiveDao.selectPersonPageList(params);
        return getPageData(list, page.getTotal(), PersonPage.class);
    }

    @Override
    public PageData<PartUnitPage> partUnitPageList(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        if (ObjectUtil.isNotEmpty(params.get("corpType"))) {
            String corpType = (String) params.get("corpType");
            params.put("corpType", Arrays.asList(corpType.split(",")));
        }
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<PartUnitPage> list = archiveDao.selectPartUnitPageList(params);
        return getPageData(list, page.getTotal(), PartUnitPage.class);
    }

    @Override
    public PageData<TeamPage> teamPageList(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<TeamPage> list = archiveDao.selectTeamPageList(params);
        return getPageData(list, page.getTotal(), TeamPage.class);
    }
}
