package io.renren.modules.enterprise.ps02.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.annotation.cachelockvalidator.CacheLock;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps02.dto.*;
import io.renren.modules.enterprise.ps02.service.Ps02Service;
import io.renren.modules.enterprise.ps02.vo.Ps02FilePromiseUploadVO;
import io.renren.modules.enterprise.ps02.vo.Ps02FileUploadVO;
import io.renren.modules.enterprise.ps08.dto.BPs08DTO;
import io.renren.modules.enterprise.ps08.entity.BPs08Entity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("enterprise/ps02")
@Api(tags = "建筑工人信息")
public class Ps02Controller {
    @Autowired
    private Ps02Service ps02Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:ps02:page")
    public Result<PageData<Ps02PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps02PageDTO> page = ps02Service.pageList(params);

        return new Result<PageData<Ps02PageDTO>>().ok(page);
    }

    @GetMapping("empRecordPage")
    @ApiOperation("用工记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "ps0101", value = "人员ID", paramType = "query", required = true, dataType = "String")
    })
    public Result<PageData<Ps02EmpRecordDTO>> empRecordPage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps02EmpRecordDTO> page = ps02Service.empRecordPageList(params);

        return new Result<PageData<Ps02EmpRecordDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps02:info")
    public Result<Ps02DTO> get(@PathVariable("id") Long id) {
        Ps02DTO data = ps02Service.getPs02Info(id);

        return new Result<Ps02DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @CacheLock(prefix = "ps02")
    @RequiresPermissions("enterprise:ps02:save")
    public Result save(@CacheParam @RequestBody Ps02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps02Service.savePs02Info(dto);

        return new Result();
    }

    @PostMapping("newSave")
    @ApiOperation("保存")
    @LogOperation("保存")
    @CacheLock(prefix = "ps02")
    @RequiresPermissions("enterprise:ps02:newSave")
    public Result newSave(@CacheParam @RequestBody Ps02DTO dto) {

        ps02Service.newSave(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @CacheLock(prefix = "ps02")
    @RequiresPermissions("enterprise:ps02:update")
    public Result update(@RequestBody Ps02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps02Service.updatePs02Info(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:ps02:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps02Service.delete(ids);

        return new Result();
    }

    @PutMapping("exitPerson")
    @ApiOperation("退场")
    @LogOperation("退场")
    @RequiresPermissions("enterprise:ps02:exitPerson")
    public Result exitTeam(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps02Service.exitPerson(ids);

        return new Result();
    }

    @PutMapping("enterPerson")
    @ApiOperation("进场")
    @LogOperation("进场")
    @RequiresPermissions("enterprise:ps02:enterPerson")
    public Result enterTeam(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps02Service.enterPerson(ids);

        return new Result();
    }

    @PostMapping("updateHeadImage")
    @ApiOperation("更新头像")
    @LogOperation("更新头像")
    public Result updateHeadImage(@ApiIgnore @RequestBody Map<String, Object> params) {
        ps02Service.updateHeadImage(params);
        return new Result();
    }

    @PutMapping("personAuthor")
    @ApiOperation("工人下发")
    @RequiresPermissions("enterprise:ps02:personAuthor")
    public Result personAuthor(@RequestBody Long[] ids) {
        ps02Service.deviceAddPerson(ids);
        return new Result();
    }

    @GetMapping("/previewUserContract/{userId}")
    @ApiOperation("预览工人合同")
    @RequiresPermissions("enterprise:ps02:previewUserContract")
    public Result<BPs08Entity> previewUserContract(@PathVariable("userId") String userId) {

        BPs08Entity bPs08Entity = ps02Service.previewUserContract(userId);
        return new Result<BPs08Entity>().ok(bPs08Entity);
    }

    @PostMapping("/saveUserContract")
    @ApiOperation("保存工人在线合同信息")
    @RequiresPermissions("enterprise:ps02:saveUserContract")
    public Result saveUserContract(@ApiIgnore @RequestBody BPs08DTO bPs08DTO) throws Exception {

        ps02Service.saveUserContract(bPs08DTO);
        return new Result().ok("生成成功!");
    }


    @PostMapping("/exportUserContract")
    @ApiOperation("导出工人在线合同")
    @RequiresPermissions("enterprise:ps02:exportUserContract")
    public void exportUserContract(@ApiIgnore @RequestBody Long[] ids, HttpServletResponse response) {

        ps02Service.exportUserContract(ids, response);
    }

    @PostMapping("/contractUpload")
    @ApiOperation("上传工人合同")
    @RequiresPermissions("enterprise:ps02:contractUpload")
    public Result contractUpload(@ApiIgnore @RequestBody Ps02FileUploadVO fileUploadVO) {

        ValidatorUtils.validateEntity(fileUploadVO);
        return ps02Service.contractUpload(fileUploadVO);
    }

    @GetMapping("/getContractUpload/{ps0201}")
    @ApiOperation("获取工人合同")
//    @RequiresPermissions("enterprise:ps02:getContractUpload")
    public Result getContractUpload(@ApiIgnore @PathVariable("ps0201") String ps0201) {

        return ps02Service.getContractUpload(ps0201);
    }

    @PostMapping("/contractPromiseUpload")
    @ApiOperation("建筑工人承诺书信息上传")
//    @RequiresPermissions("enterprise:ps02:contractPromiseUpload")
    public Result contractPromiseUpload(@ApiIgnore @RequestBody Ps02FilePromiseUploadVO fileUploadVO) {

        ValidatorUtils.validateEntity(fileUploadVO);
        return ps02Service.contractPromiseUpload(fileUploadVO);
    }

    @GetMapping("/downloadPromiseLetter")
    @ApiOperation("建筑工人承诺书模板下载")
//    @RequiresPermissions("enterprise:ps02:downloadPromiseLetter")
    public void downloadPromiseLetter(HttpServletResponse response) {

        ps02Service.downloadPromiseLetter(response);
    }

    @GetMapping("/getPromiseLetter/{ps0201}")
    @ApiOperation("获取建筑工人承诺书")
//    @RequiresPermissions("enterprise:ps02:getPromiseLetter")
    public Result getPromiseLetter(@ApiIgnore @PathVariable("ps0201") String ps0201) {

        return ps02Service.getPromiseLetter(ps0201);
    }

    @GetMapping("exportRosterPage")
    @ApiOperation("工人花名册导出分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "tm0101", value = "班组", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始月份", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "开始月份", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:ps02:exportRosterPage")
    public Result<PageData<Ps02ExportDTO>> exportRosterPage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps02ExportDTO> page = ps02Service.exportRosterPage(params);

        return new Result<PageData<Ps02ExportDTO>>().ok(page);
    }

    @GetMapping("exportRoster")
    @ApiOperation("工人花名册导出")
    @RequiresPermissions("enterprise:ps02:exportRoster")
    public void exportRoster(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        ps02Service.exportRoster(params, response);
    }

    @PostMapping("/batchExit")
    @ApiOperation("批量退场")
//    @RequiresPermissions("enterprise:ps02:batchExit")
    public void batchExit(@ApiIgnore @RequestBody String[] ids) {

        ps02Service.batchExit(ids);
    }

    @PostMapping("/batchImportContract")
    @ApiOperation("批量导入合同")
//    @RequiresPermissions("enterprise:ps02:batchImportContract")
    public Result batchImportContract(
            MultipartHttpServletRequest request,
            HttpServletResponse response
    ) {
        return ps02Service.batchImportContract(request, response);
    }

    @GetMapping("/getWorkerInOrOutData")
    @ApiOperation("获取工人进退场列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "name", value = "工人名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "inOrOut", value = "进退场状态", paramType = "query", dataType = "String")
    })
//    @RequiresPermissions("enterprise:ps02:getWorkerInOrOutData")
    public Result getWorkerInOrOutData(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps02InOrOutDTO> page = ps02Service.getWorkerInOrOutData(params);
        return new Result<PageData<Ps02InOrOutDTO>>().ok(page);
    }

    @GetMapping("/getWorkerOverageNum")
    @ApiOperation("获取工人超龄人数")
//    @RequiresPermissions("enterprise:ps02:getWorkerOverageNum")
    public Result getWorkerOverageNum() {
        String workerOverageNum = ps02Service.getWorkerOverageNum();
        return new Result<>().ok(workerOverageNum);
    }

    @GetMapping("/getWorkerOveragePage")
    @ApiOperation("获取工人超龄列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
    })
//    @RequiresPermissions("enterprise:ps02:getWorkerOveragePage")
    public Result<PageData<Ps02WorkerOverageDTO>> getWorkerOveragePage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps02WorkerOverageDTO> page = ps02Service.getWorkerOveragePage(params);
        return new Result<PageData<Ps02WorkerOverageDTO>>().ok(page);
    }
}