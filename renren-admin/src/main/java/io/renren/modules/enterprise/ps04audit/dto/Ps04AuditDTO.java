package io.renren.modules.enterprise.ps04audit.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-02
 */
@Data
@ApiModel(value = "管理人员审核表")
public class Ps04AuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键ID")
	private BigDecimal ps0401;
		@ApiModelProperty(value = "项目ID")
	private BigDecimal pj0101;
		@ApiModelProperty(value = "参建单位ID")
	private BigDecimal cp0201;
		@ApiModelProperty(value = "人员ID")
	private BigDecimal ps0301;
		@ApiModelProperty(value = "岗位类型")
	private String jobtype;
		@ApiModelProperty(value = "项目采集照片")
	private String photo;
		@ApiModelProperty(value = "进场时间")
	private Date entrytime;
		@ApiModelProperty(value = "退场时间")
	private Date exittime;
		@ApiModelProperty(value = "进退场状态")
	private String inOrOut;
		@ApiModelProperty(value = "审核状态")
	private String auditstatus;
		@ApiModelProperty(value = "审核人")
	private String auditor;
		@ApiModelProperty(value = "审核时间")
	private Date auditdate;
		@ApiModelProperty(value = "审核结果")
	private String auditresult;
				
}