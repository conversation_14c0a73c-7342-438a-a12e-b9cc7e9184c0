package io.renren.modules.enterprise.ps02face.service.impl;

import io.renren.common.redis.RedisUtils;
import io.renren.common.utils.Base64Utils;
import io.renren.modules.enterprise.ps02face.entity.Ps02FaceEntity;
import io.renren.modules.enterprise.ps02face.service.ISaveOrUpdateRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chris
 * @Date : 2023-02-20
 **/
@Slf4j
@Service
public class SaveOrUpdateRedisServiceImpl implements ISaveOrUpdateRedisService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void insertFaceFeature(String projectId, Ps02FaceEntity ps02Face) {

        String byteArray2Base = Base64Utils.byteArray2Base(ps02Face.getFaceFeature());
        redisUtils.hSet(projectId, String.valueOf(ps02Face.getPs0201()), byteArray2Base, -1);

    }

    @Override
    public List<Ps02FaceEntity> batchGetFaceFeature(String projectId) {

        Map<String, Object> entries = redisTemplate.opsForHash().entries(projectId);
        List<Ps02FaceEntity> result = entries.entrySet().stream()
                .map(c -> {
                    Ps02FaceEntity faceData = new Ps02FaceEntity();
                    faceData.setPs0201(Long.valueOf(c.getKey()));
                    faceData.setFaceFeature(Base64Utils.base64ToBytes(c.getValue().toString()));
                    return faceData;
                })
                .collect(Collectors.toList());
        return result;
    }
}
