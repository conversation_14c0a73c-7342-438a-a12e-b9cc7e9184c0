package io.renren.modules.enterprise.pj13.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.pj13.dto.Pj13DTO;
import io.renren.modules.enterprise.pj13.dto.Ps02ExitPageDTO;
import io.renren.modules.enterprise.pj13.dto.Ps04ExitPageDTO;
import io.renren.modules.enterprise.pj13.dto.Tm01ExitPageDTO;
import io.renren.modules.enterprise.pj13.entity.Pj13Entity;

import java.util.Map;

/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
public interface Pj13Service extends CrudService<Pj13Entity, Pj13DTO> {

    // 工人退场分页
    PageData<Ps02ExitPageDTO> ps02ExitPage(Map<String, Object> params);
    // 管理人员退场分页
    PageData<Ps04ExitPageDTO> ps04ExitPage(Map<String, Object> params);
    // 班组退场分页
    PageData<Tm01ExitPageDTO> tm01ExitPage(Map<String, Object> params);
}