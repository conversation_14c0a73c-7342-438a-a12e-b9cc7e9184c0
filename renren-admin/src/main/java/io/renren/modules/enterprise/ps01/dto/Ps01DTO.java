package io.renren.modules.enterprise.ps01.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.annotation.iscitizenidvalidator.IsCitizenIdValidator;
import io.renren.common.annotation.ismobilevalidator.IsMobileValidator;
import io.renren.common.enums.DesenTypeEum;
import io.renren.common.validator.group.DefaultGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "人员实名基础信息")
public class Ps01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0101;

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空",groups = DefaultGroup.class)
    @Length(max = 50 ,message = "姓名长度不能超过50个汉字")
    private String name;

    @ApiModelProperty(value = "证件类型_Select选择器", required = true)
    @NotBlank(message = "证件类型不能为空")
    private String idcardtype;

    @ApiModelProperty(value = "证件号码", required = true)
    @NotBlank(message = "证件号码不能为空",groups = DefaultGroup.class)
    @IsCitizenIdValidator(message = "身份证号码格式不正确")
//    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;

    @ApiModelProperty(value = "性别_Select选择器", required = true)
    @NotBlank(message = "性别不能为空")
    private String gender;

    @ApiModelProperty(value = "民族_Select选择器", required = true)
    @NotBlank(message = "民族不能为空")
    private String nation;

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    @ApiModelProperty(value = "住址", required = true)
    @NotBlank(message = "住址不能为空")
    private String address;

    @ApiModelProperty(value = "学历_Select选择器")
    private String edulevel;

    @ApiModelProperty(value = "学位_Select选择器")
    private String degree;

    /*@ApiModelProperty(value = "籍贯（身份证号前6位）", required = true)
    @NotBlank(message = "籍贯不能为空")
    private String areacode;*/

    @ApiModelProperty(value = "身份证头像")
    private String headimageurl;

    @ApiModelProperty(value = "政治面貌", required = true)
//    @NotBlank(message = "政治面貌不能为空")
    private String politicstype;

    @ApiModelProperty(value = "人员类别", required = true)
    private String workertype;

    @ApiModelProperty(value = "是否加入工会_Select选择器")
    private String isjoined;

    @ApiModelProperty(value = "加入工会时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date joinedtime;

    @ApiModelProperty(value = "手机号码", required = true)
    @NotBlank(message = "手机号码不能为空")
    @IsMobileValidator(message = "手机号码格式不正确")
//    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String cellphone;

    @ApiModelProperty(value = "文化程度_Select选择器")
//    @NotBlank(message = "文化程度不能为空")
    private String cultureleveltype;

    @ApiModelProperty(value = "特长")
    @Length(max = 200,message = "特长长度不能超过200个汉字")
    private String specialty;

    @ApiModelProperty(value = "是否有重大病史_Select选择器")
    private String hasbadmedicalhistory;

    @ApiModelProperty(value = "紧急联系人姓名")
//    @NotBlank(message  = "紧急联系人姓名不能为空",groups = DefaultGroup.class)
    @Length(max = 50,message = "紧急联系人姓名长度不能超过50个汉字")
    private String urgentlinkman;

    @ApiModelProperty(value = "紧急联系电话")
//    @NotBlank(message  = "紧急联系人电话不能为空")
//    @IsMobileValidator(message = "紧急联系人电话格式不正确")
//    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String urgentlinkmanphone;

    @ApiModelProperty(value = "开始工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workdate;

    @ApiModelProperty(value = "婚姻状况")
    private String maritalstatus;

    @ApiModelProperty(value = "发证机关")
    @Length(max = 50,message = "发证机关长度不能超过50个汉字")
    private String grantorg;

    @ApiModelProperty(value = "正面照 URL")
    private String positiveidcardimageurl;

    @ApiModelProperty(value = "反面照 URL")
    private String negativeidcardimageurl;

    @ApiModelProperty(value = "有效期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "有效期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expirydate;

    @ApiModelProperty(value = "ps0201")
    private String ps0201;

    @ApiModelProperty(value = "籍贯（身份证号前6位）", required = true)
    @NotBlank(message = "籍贯不能为空")
    private String areacode;

}