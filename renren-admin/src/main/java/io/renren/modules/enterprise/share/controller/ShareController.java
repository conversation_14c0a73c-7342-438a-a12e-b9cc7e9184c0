package io.renren.modules.enterprise.share.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.share.dto.ShareDTO;
import io.renren.modules.enterprise.share.service.ShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 三方对接授权
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@RestController
@RequestMapping("enterprise/share")
@Api(tags = "三方对接授权")
public class ShareController {
    @Autowired
    private ShareService shareService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:share:page")
    public Result<PageData<ShareDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ShareDTO> page = shareService.pageList(params);
        return new Result<PageData<ShareDTO>>().ok(page);
    }


    @PostMapping("bind")
    @ApiOperation("授权")
    @LogOperation("授权")
    @RequiresPermissions("enterprise:share:bind")
    public Result bind(@RequestBody ShareDTO dto) {
        Result result = shareService.bind(dto);
        return result;
    }

    @PostMapping("unbind")
    @ApiOperation("解除授权")
    @LogOperation("解除授权")
    @RequiresPermissions("enterprise:share:unbind")
    public Result unbind(@RequestBody ShareDTO dto) {
        Result result = shareService.unbind(dto);
        return result;
    }

}