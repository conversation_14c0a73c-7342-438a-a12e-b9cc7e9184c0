package io.renren.modules.enterprise.tm01.vo;

import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 班组附件信息上传
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "班组附件信息上传")
public class Tm01FileUploadVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "tm0101")
    @NotBlank(message = "请选择一个班组进行信息上传")
    private String tm0101;

    @ApiModelProperty(value = "工人合同", required = true)
    @Valid
    private List<Ot01DTO> ot01DTOList;

}