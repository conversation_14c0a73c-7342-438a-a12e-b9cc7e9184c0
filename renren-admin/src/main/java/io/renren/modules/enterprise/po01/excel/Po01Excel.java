package io.renren.modules.enterprise.po01.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用油管理
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
public class Po01Excel {
    @Excel(name = "主键ID")
    private BigDecimal po0101;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "采购单位")
    private String purchaseunit;
    @Excel(name = "统一社会信用代码")
    private String creditcode;
    @Excel(name = "油品供货单位")
    private String supplyunit;
    @Excel(name = "购买时间")
    private Date purchasedate;
    @Excel(name = "油品类型")
    private String oiltype;
    @Excel(name = "购买量(升)")
    private BigDecimal purchaseno;
    @Excel(name = "备注")
    private String memo;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}