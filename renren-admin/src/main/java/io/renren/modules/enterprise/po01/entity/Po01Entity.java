package io.renren.modules.enterprise.po01.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用油管理
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PO01")
public class Po01Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long po0101;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 采购单位
     */
    private String purchaseunit;
    /**
     * 统一社会信用代码
     */
    private String creditcode;
    /**
     * 油品供货单位
     */
    private String supplyunit;
    /**
     * 购买时间
     */
    private Date purchasedate;
    /**
     * 油品类型
     */
    private String oiltype;
    /**
     * 购买量(升)
     */
    private BigDecimal purchaseno;
    /**
     * 备注
     */
    private String memo;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}