package io.renren.modules.enterprise.ps11.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.ps11.dao.Ps11Dao;
import io.renren.modules.enterprise.ps11.dto.Ps11DTO;
import io.renren.modules.enterprise.ps11.entity.Ps11Entity;
import io.renren.modules.enterprise.ps11.service.Ps11Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 管理人员/工人预约进场
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-25
 */
@Service
public class Ps11ServiceImpl extends CrudServiceImpl<Ps11Dao, Ps11Entity, Ps11DTO> implements Ps11Service {

    @Override
    public QueryWrapper<Ps11Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps11Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }
}