package io.renren.modules.enterprise.tjattendanceinfo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.renren.modules.enterprise.tjattendanceinfo.annotation.AttendanceFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤统计表详情
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-14
 */
@Data
@ApiModel(value = "考勤统计表详情")
public class TjAttendanceInfoPageExportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "序号", orderNum = "0", format = "isAddIndex")
    @JsonIgnore
    private Integer index = 1;

    @Excel(name = "参建企业", orderNum = "1", width = 20)
    @ApiModelProperty(value = "参建企业")
    private String corpname;

    @Excel(name = "班组名称", orderNum = "2", width = 20)
    @ApiModelProperty(value = "班组名称")
    private String teamname;

    @Excel(name = "人员姓名", orderNum = "3", width = 20)
    @ApiModelProperty(value = "人员姓名")
    private String workername;

    @Excel(name = "证件号码", orderNum = "4", width = 20)
    @ApiModelProperty(value = "证件号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;

    @Excel(name = "身份证住址", orderNum = "5", width = 20)
    @ApiModelProperty(value = "身份证住址")
    private String address;

    @Excel(name = "联系电话", orderNum = "6", width = 20)
    @ApiModelProperty(value = "联系电话")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String cellphone;

    @Excel(name = "紧急联系电话", orderNum = "7", width = 20)
    @ApiModelProperty(value = "紧急联系电话")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String urgentlinkmanphone;

    @Excel(name = "人员类型", orderNum = "8", width = 20, replace = {"建筑工人_1", "项目管理人员_2"})
    @ApiModelProperty(value = "人员类型")
    private String personType;

    @Excel(name = "岗位", orderNum = "9", width = 20)
    @ApiModelProperty(value = "岗位")
    private String jobtype;

    @Excel(name = "当前进退场状态", orderNum = "10", width = 20, replace = {"在场_1", "离场_2"})
    @ApiModelProperty(value = "当前进退场状态")
    private String inOrOut;

    @Excel(name = "进场时间", orderNum = "11", width = 20, exportFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;

    @Excel(name = "退场时间", orderNum = "12", width = 20, exportFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退场时间")
    private Date exittime;

    @Excel(name = "工资卡开户行名称", orderNum = "13", width = 20)
    @ApiModelProperty(value = "工资卡开户行名称")
    private String payrollbankname;

    @Excel(name = "工资卡帐号", orderNum = "14", width = 20)
    @ApiModelProperty(value = "工资卡帐号")
    private String payrollbankcardnumber;

    @Excel(name = "统计时间", orderNum = "15", width = 20, exportFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "统计时间")
    private Date tjdate;

    @Excel(name = "考勤月份", orderNum = "16", width = 20)
    @ApiModelProperty(value = "考勤月份")
    private String kqmonth;

    @Excel(name = "考勤天数", orderNum = "17", width = 20)
    @ApiModelProperty(value = "考勤天数")
    private Long kqdays;

    @Excel(name = "1号", orderNum = "18", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "1号")
    private String day1;

    @Excel(name = "2号", orderNum = "19", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "2号")
    private String day2;

    @Excel(name = "3号", orderNum = "20", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "3号")
    private String day3;

    @Excel(name = "4号", orderNum = "21", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "4号")
    private String day4;

    @Excel(name = "5号", orderNum = "22", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "5号")
    private String day5;

    @Excel(name = "6号", orderNum = "23", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "6号")
    private String day6;

    @Excel(name = "7号", orderNum = "24", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "7号")
    private String day7;

    @Excel(name = "8号", orderNum = "25", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "8号")
    private String day8;

    @Excel(name = "9号", orderNum = "26", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "9号")
    private String day9;

    @Excel(name = "10号", orderNum = "27", width = 20, height = 12)
    @AttendanceFormat()
    @ApiModelProperty(value = "10号")
    private String day10;

    @Excel(name = "11号", orderNum = "28", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "11号")
    private String day11;

    @Excel(name = "12号", orderNum = "29", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "12号")
    private String day12;

    @Excel(name = "13号", orderNum = "30", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "13号")
    private String day13;

    @Excel(name = "14号", orderNum = "31", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "14号")
    private String day14;

    @Excel(name = "15号", orderNum = "32", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "15号")
    private String day15;

    @Excel(name = "16号", orderNum = "33", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "16号")
    private String day16;

    @Excel(name = "17号", orderNum = "34", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "17号")
    private String day17;

    @Excel(name = "18号", orderNum = "35", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "18号")
    private String day18;

    @Excel(name = "19号", orderNum = "36", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "19号")
    private String day19;

    @Excel(name = "20号", orderNum = "37", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "20号")
    private String day20;

    @Excel(name = "21号", orderNum = "38", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "21号")
    private String day21;

    @Excel(name = "22号", orderNum = "39", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "22号")
    private String day22;

    @Excel(name = "23号", orderNum = "40", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "23号")
    private String day23;

    @Excel(name = "24号", orderNum = "41", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "24号")
    private String day24;

    @Excel(name = "25号", orderNum = "42", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "25号")
    private String day25;

    @Excel(name = "26号", orderNum = "43", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "26号")
    private String day26;

    @Excel(name = "27号", orderNum = "44", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "27号")
    private String day27;

    @Excel(name = "28号", orderNum = "45", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "28号")
    private String day28;

    @Excel(name = "29号", orderNum = "46", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "29号")
    private String day29;

    @Excel(name = "30号", orderNum = "47", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "30号")
    private String day30;

    @Excel(name = "31号", orderNum = "48", width = 20)
    @AttendanceFormat()
    @ApiModelProperty(value = "31号")
    private String day31;
}