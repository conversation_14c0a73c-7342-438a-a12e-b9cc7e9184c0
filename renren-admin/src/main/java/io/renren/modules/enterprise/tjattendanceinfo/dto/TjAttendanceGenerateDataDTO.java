package io.renren.modules.enterprise.tjattendanceinfo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 生成数据参数
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/5/14 15:33
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TjAttendanceGenerateDataDTO {

    @NotBlank(message = "生成数据月份不能为空")
    private String tjMonth;
}
