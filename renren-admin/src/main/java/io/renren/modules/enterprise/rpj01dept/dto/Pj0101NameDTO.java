package io.renren.modules.enterprise.rpj01dept.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目主键名称
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-11
 */
@Data
@ApiModel(value = "项目主键名称")
public class Pj0101NameDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "项目名称")
    private String name;

}