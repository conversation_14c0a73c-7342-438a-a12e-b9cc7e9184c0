package io.renren.modules.enterprise.pw02.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pw02.dto.Pw02DTO;
import io.renren.modules.enterprise.pw02.entity.Pw02Entity;
import io.renren.modules.enterprise.pw02.excel.Pw02ImportExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工资导入明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Mapper
public interface Pw02Dao extends BaseDao<Pw02Entity> {

    List<Pw02DTO> getList(@Param("params") Map<String, Object> params);

    void batchInsert(List<Pw02Entity> pw02List);
    // 获取项目在场人员
    List<Pw02ImportExcel> listByPj0101AndMonth(@Param("pj0101") Long pj0101, @Param("month") Long month);

    List<Pw02ImportExcel> getExportList(Long pw0101);

    double getSfgzByPw0101(Long pw0101);

    void updateAuditStatusByPw0101(Long pw0101);
}