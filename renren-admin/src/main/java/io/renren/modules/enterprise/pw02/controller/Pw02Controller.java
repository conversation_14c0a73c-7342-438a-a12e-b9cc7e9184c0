package io.renren.modules.enterprise.pw02.controller;

import cn.hutool.core.util.StrUtil;
import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.ps02.service.Ps02Service;
import io.renren.modules.enterprise.pw01.entity.Pw01Entity;
import io.renren.modules.enterprise.pw01.service.Pw01Service;
import io.renren.modules.enterprise.pw02.dto.Pw02DTO;
import io.renren.modules.enterprise.pw02.dto.Pw02DeleteDTO;
import io.renren.modules.enterprise.pw02.entity.Pw02Entity;
import io.renren.modules.enterprise.pw02.excel.Pw02Excel;
import io.renren.modules.enterprise.pw02.service.Pw02Service;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceGenerateDataDTO;
import io.renren.modules.enterprise.tjattendanceinfo.service.TjAttendanceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 工资导入明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@RestController
@RequestMapping("enterprise/pw02")
@Api(tags="工资导入明细表")
public class Pw02Controller {
    @Autowired
    private Pw02Service pw02Service;
    @Autowired
    private Pw01Service pw01Service;
    @Autowired
    private TjAttendanceInfoService tjAttendanceInfoService;
    @Autowired
    private Ps02Service ps02Service;
    @Autowired
    private Ps01Service ps01Service;

    @GetMapping("list")
    @ApiOperation("列表")
    @RequiresPermissions("enterprise:pw02:list")
    public Result<List<Pw02DTO>> list(@ApiIgnore @RequestParam Map<String, Object> params){
        List<Pw02DTO> list = pw02Service.getList(params);

        return new Result<List<Pw02DTO>>().ok(list);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pw02:info")
    public Result<Pw02DTO> get(@PathVariable("id") Long id){
        Pw02DTO data = pw02Service.get(id);

        return new Result<Pw02DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:pw02:save")
    public Result save(@RequestBody Pw02DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);
        HashMap<String, Object> params = new HashMap<>();
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        String tjMonth = null;
        SimpleDateFormat inputSimpleDateFormat = new SimpleDateFormat("yyyyMM");
        try {
            tjMonth = new SimpleDateFormat("yyyy-MM")
                    .format(inputSimpleDateFormat.parse(String.valueOf(dto.getMonth())));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        params.put("tjMonth", tjMonth);
        params.put("inOrOut", "1");
        boolean exist = tjAttendanceInfoService.existTjAttendanceData(params);
        if (!exist) {
            TjAttendanceGenerateDataDTO tjAttendanceGenerateDataDTO = new TjAttendanceGenerateDataDTO();
            tjAttendanceGenerateDataDTO.setTjMonth(tjMonth);
            tjAttendanceInfoService.generateTjAttendanceData(tjAttendanceGenerateDataDTO);
        }

        params.put("idcardnumber", dto.getIdcardnumber());
        Ps02Entity ps02 = ps02Service.getByIdcardnumberAndName(dto.getIdcardnumber(), dto.getName());
        dto.setXtkqts(tjAttendanceInfoService.getKqdaysByIdcardnumber(params));
        if (Objects.isNull(ps02)) {
            dto.setImportstatus("1");
        } else {
            dto.setImportstatus(getImportStatus(dto.getKqts(), dto.getXtkqts()));
        }
        // 检验异常数据说明是否填写
        if (!"0".equals(dto.getImportstatus()) && StrUtil.isEmpty(dto.getReason())) {
            return new Result().error("说明未填写!");
        }
        dto.setAuditstatus("0");
        pw02Service.save(dto);

        // 更新pw01中的发放人数
        Pw01Entity pw01Entity = pw01Service.selectById(dto.getPw0101());
        pw01Entity.setFfzrs(pw01Entity.getFfzrs() + 1);
        pw01Entity.setSfgz(pw02Service.getSfgzByPw0101(dto.getPw0101()));
        pw01Service.updateById(pw01Entity);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:pw02:update")
    public Result update(@RequestBody Pw02DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);
        // 补录人员不会改变录入状态
        if (!"1".equals(dto.getImportstatus())) {
            dto.setImportstatus(getImportStatus(dto.getKqts(), dto.getXtkqts()));
        }

        pw02Service.update(dto);
        Pw01Entity pw01Entity = pw01Service.selectById(dto.getPw0101());
        pw01Entity.setSfgz(pw02Service.getSfgzByPw0101(dto.getPw0101()));
        pw01Service.updateById(pw01Entity);
        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pw02:delete")
    public Result delete(@RequestBody Pw02DTO dto){

        // 更新pw01中的发放人数
        Pw01Entity pw01Entity = pw01Service.selectById(dto.getPw0101());
        pw01Entity.setFfzrs(pw01Entity.getFfzrs() - 1);
        pw02Service.deleteById(dto.getPw0201());
        pw01Entity.setSfgz(pw02Service.getSfgzByPw0101(dto.getPw0101()));
        pw01Service.updateById(pw01Entity);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:pw02:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Pw02DTO> list = pw02Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Pw02Excel.class);
    }

    private String getImportStatus(Integer kqts, Integer xtkqts) {
        if (Objects.equals(kqts, xtkqts)) {
            return "0";
        } else {
            return "2";
        }
    }

}