package io.renren.modules.enterprise.cp02audit.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 参建单位审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_CP02_AUDIT")
public class Cp02AuditEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long cp0201;
    /**
     * 企业ID
     */
	private Long cp0101;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 参建类型
     */
	private String corptype;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 审核状态(0待审核，1通过，2不通过)
     */
	private String auditstatus;
    /**
     * 审核人
     */
	private String auditor;
    /**
     * 审核时间
     */
	private Date auditdate;
    /**
     * 审核结果
     */
	private String auditresult;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}