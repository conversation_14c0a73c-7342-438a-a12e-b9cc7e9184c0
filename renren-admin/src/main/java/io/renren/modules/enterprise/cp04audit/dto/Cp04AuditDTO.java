package io.renren.modules.enterprise.cp04audit.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 分包信息审核表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-03
 */
@Data
@ApiModel(value = "分包信息审核表")
public class Cp04AuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long cp0401;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "分包单位ID")
    private Long cp0201;
    @ApiModelProperty(value = "分包工程名称")
    private String subcontractname;
    @ApiModelProperty(value = "分包工程内容")
    private String subcontractcontent;
    @ApiModelProperty(value = "开工时间")
    private Date startdate;
    @ApiModelProperty(value = "计划完工时间")
    private Date completedate;
    @ApiModelProperty(value = "分包价格（万元）")
    private BigDecimal subcontractprice;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核人")
    private String auditor;

}