package io.renren.modules.enterprise.cp02audit.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDTO;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDetailDTO;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditPageDTO;
import io.renren.modules.enterprise.cp02audit.service.Cp02AuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 参建单位审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@RestController
@RequestMapping("enterprise/cp02audit")
@Api(tags="参建单位审核表")
public class Cp02AuditController {
    @Autowired
    private Cp02AuditService cp02AuditService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:cp02audit:page")
    public Result<PageData<Cp02AuditPageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Cp02AuditPageDTO> page = cp02AuditService.getPageList(params);

        return new Result<PageData<Cp02AuditPageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:cp02audit:info")
    public Result<Cp02AuditDetailDTO> get(@PathVariable("id") Long id){

        return new Result<Cp02AuditDetailDTO>().ok(cp02AuditService.getInfo(id));
    }

}