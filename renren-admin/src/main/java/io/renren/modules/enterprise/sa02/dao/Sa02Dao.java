package io.renren.modules.enterprise.sa02.dao;


import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.sa02.dto.Sa02DTO;
import io.renren.modules.enterprise.sa02.entity.Sa02Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
*<AUTHOR>
*@date 2021/10/9
*/
@Mapper
public interface Sa02Dao extends BaseDao<Sa02Entity> {

    List<Sa02DTO> page(Map<String, Object> params);

}
