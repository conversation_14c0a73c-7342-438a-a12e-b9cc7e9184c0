package io.renren.modules.enterprise.pj13.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.pj13.dto.*;
import io.renren.modules.enterprise.pj13.service.Pj13Service;
import io.renren.modules.enterprise.ps02.service.Ps02Service;
import io.renren.modules.enterprise.ps04.service.Ps04Service;
import io.renren.modules.enterprise.tm01.service.Tm01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.Map;


/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@RestController
@RequestMapping("enterprise/pj13")
@Api(tags="退场审核表")
public class Pj13Controller {
    @Autowired
    private Pj13Service pj13Service;
    @Autowired
    private Ps02Service ps02Service;
    @Autowired
    private Ps04Service ps04Service;
    @Autowired
    private Tm01Service tm01Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pj13:page")
    public Result<PageData<Pj13DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Pj13DTO> page = pj13Service.page(params);

        return new Result<PageData<Pj13DTO>>().ok(page);
    }

    @GetMapping("ps02ExitPage")
    @ApiOperation("工人退场分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pj13:ps02ExitPage")
    public Result<PageData<Ps02ExitPageDTO>> ps02ExitPage(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps02ExitPageDTO> page = pj13Service.ps02ExitPage(params);

        return new Result<PageData<Ps02ExitPageDTO>>().ok(page);
    }

    @GetMapping("ps04ExitPage")
    @ApiOperation("管理人员退场分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pj13:ps04ExitPage")
    public Result<PageData<Ps04ExitPageDTO>> ps04ExitPage(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps04ExitPageDTO> page = pj13Service.ps04ExitPage(params);

        return new Result<PageData<Ps04ExitPageDTO>>().ok(page);
    }

    @GetMapping("tm01ExitPage")
    @ApiOperation("班组退场分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pj13:tm01ExitPage")
    public Result<PageData<Tm01ExitPageDTO>> tm01ExitPage(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Tm01ExitPageDTO> page = pj13Service.tm01ExitPage(params);

        return new Result<PageData<Tm01ExitPageDTO>>().ok(page);
    }

    @PostMapping("exit")
    @ApiOperation("退场")
    @LogOperation("退场")
    @RequiresPermissions("enterprise:pj13:exit")
    public Result exit(@RequestBody ExitDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        Long[] list = dto.getIds();
        for (Long id : list) {
            Pj13DTO exitAuditDTO = new Pj13DTO();
            exitAuditDTO.setExitId(id);
            exitAuditDTO.setExitType(dto.getExitType());
            exitAuditDTO.setEntrytime(getEntryTimeByExitType(id, dto.getExitType()));
            exitAuditDTO.setExittime(new Date());
            exitAuditDTO.setApplydate(new Date());
            exitAuditDTO.setAuditstatus("0");
            pj13Service.save(exitAuditDTO);
        }
        return new Result();
    }

    private Date getEntryTimeByExitType(Long id, String exitType) {
        if ("1".equals(exitType)) {
            return ps02Service.selectById(id).getEntrytime();
        } else if ("2".equals(exitType)) {
            return ps04Service.selectById(id).getEntrytime();
        } else {
            return tm01Service.selectById(id).getEntrytime();
        }
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pj13:info")
    public Result<Pj13DTO> get(@PathVariable("id") Long id){
        Pj13DTO data = pj13Service.get(id);

        return new Result<Pj13DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:pj13:save")
    public Result save(@RequestBody Pj13DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj13Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:pj13:update")
    public Result update(@RequestBody Pj13DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj13Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pj13:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj13Service.delete(ids);

        return new Result();
    }

}