package io.renren.modules.enterprise.sa02.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.sa02.dto.Sa02DTO;
import io.renren.modules.enterprise.sa02.entity.Sa02Entity;

import java.util.Map;

/**
*<AUTHOR>
*@date 2021/10/9
*/

public interface Sa02Service extends CrudService<Sa02Entity,Sa02DTO> {

    void add(Sa02DTO sa02DTO);

    void updateDto(Sa02DTO sa02DTO);

    PageData<Sa02DTO> pageSa02(Map<String, Object> params);
}
