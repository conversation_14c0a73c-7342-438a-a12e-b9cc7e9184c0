package io.renren.modules.enterprise.po01.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.iscreditcodevalidator.IsCreditCodeValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用油管理
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "用油管理")
public class Po01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long po0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "采购单位",required =true)
    @NotBlank(message = "采购单位不能为空")
    @Length(max = 100,message = "采购单位名称长度不能超过100个汉字")
    private String purchaseunit;

    @ApiModelProperty(value = "统一社会信用代码",required = true)
    @NotBlank(message = "统一社会信用代码不能为空")
    @IsCreditCodeValidator(message = "统一社会信用代码格式不正确")
    private String creditcode;

    @ApiModelProperty(value = "油品供货单位",required = true)
    @NotBlank(message = "油品供货单位不能为空")
    @Length(max = 100,message = "油品供货单位长度不能超过100个汉字")
    private String supplyunit;

    @ApiModelProperty(value = "购买时间",required = true)
    @NotNull(message = "购买时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date purchasedate;

    @ApiModelProperty(value = "油品类型",required = true)
    @NotBlank(message = "油品类型不能为空")
    @Length(max = 50,message = "油品类型的长度不能超过50汉字")
    private String oiltype;

    @ApiModelProperty(value = "购买量(升)",required = true)
    @NotNull(message = "购买量不能为空")
    private BigDecimal purchaseno;

    @ApiModelProperty(value = "备注")
    private String memo;


}