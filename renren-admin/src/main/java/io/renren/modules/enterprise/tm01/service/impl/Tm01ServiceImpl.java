package io.renren.modules.enterprise.tm01.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.constant.Constant;
import io.renren.common.constants.CommonConstants;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.FileDownloadUtil;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.cp02.dao.Cp02Dao;
import io.renren.modules.enterprise.cp02.entity.Cp02Entity;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.tm01.dao.Tm01Dao;
import io.renren.modules.enterprise.tm01.dto.Tm01DTO;
import io.renren.modules.enterprise.tm01.dto.Tm01DetailDTO;
import io.renren.modules.enterprise.tm01.dto.Tm01PageDTO;
import io.renren.modules.enterprise.tm01.entity.Tm01Entity;
import io.renren.modules.enterprise.tm01.service.Tm01Service;
import io.renren.modules.enterprise.tm01.vo.Tm01FileUploadVO;
import io.renren.modules.enterprise.tm01.vo.Tm01SaveVO;
import io.renren.modules.enterprise.tm01.vo.Tm01UpdateVO;
import io.renren.modules.enterprise.tm01audit.dao.Tm01AuditDao;
import io.renren.modules.enterprise.tm01audit.entity.Tm01AuditEntity;
import io.renren.modules.enterprise.tm02.dto.BTm02DTO;
import io.renren.modules.enterprise.tm02.service.BTm02Service;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import io.renren.modules.supdevicetask.service.SupDeviceTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Tm01ServiceImpl extends CrudServiceImpl<Tm01Dao, Tm01Entity, Tm01DTO> implements Tm01Service {

    @Autowired
    private Tm01Service tm01Service;
    @Autowired
    private Tm01Dao tm01Dao;
    @Autowired
    private BTm02Service tm02Service;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private SupDeviceTaskService supDeviceTaskService;
    @Autowired
    private Tm01AuditDao tm01AuditDao;
    @Value("${expFile.teamPromiseExport}")
    private String TEAM_PROMISE_EXPORT;

    @Override
    public QueryWrapper<Tm01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Tm01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public void update(Tm01DTO dto) {
        //当班组名称发生变化时候,需要校验班组是否存在
        Tm01DTO tm01DTO = tm01Service.get(dto.getTm0101());
        if (!tm01DTO.getTeamname().equals(dto.getTeamname())) {
            tm01Service.checkTeamName(dto.getTeamname(), dto.getPj0101());
        }
        Tm01Entity tm01Entity = ConvertUtils.sourceToTarget(dto, Tm01Entity.class);
        baseDao.updateById(tm01Entity);
    }

    @Override
    public void checkTeamName(String teamName, Long pj0101) {
        Integer teamNameCount = baseDao.queryByName(teamName, pj0101);
        if (teamNameCount > 0) {
            throw new RenException("该班组已存在,请勿重复添加");
        }
    }


    @Override
    public PageData<Tm01PageDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Tm01Entity> page = getPage(params, "", false);
        List<Tm01PageDTO> list = baseDao.getList(params);
        return getPageData(list, page.getTotal(), Tm01PageDTO.class);
    }

    @Override
    public void saveTeamInfo(Tm01DTO dto) {
        //获取当前登录用户的项目ID
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        dto.setPj0101(pj0101);
        dto.setEntrytime(DateUtil.parse(DateUtil.today()));
        dto.setInOrOut("1");
        //校验当前项目下，班组是否存在
        tm01Service.checkTeamName(dto.getTeamname(), pj0101);
        Tm01Entity tm01Entity = ConvertUtils.sourceToTarget(dto, Tm01Entity.class);
        baseDao.insert(tm01Entity);
        //保存进退场信息，新增的时候默认为进场,tm02增加一条数据
        BTm02DTO bTm02DTO = new BTm02DTO();
        bTm02DTO.setTm0101(tm01Entity.getTm0101());
        bTm02DTO.setInOrOut("1");
        bTm02DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
        tm02Service.save(bTm02DTO);
    }

    @Override
    public void saveTeamInfo(Tm01SaveVO dto) {
        dto.setIdcardtype("1");
        //获取当前登录用户的项目ID
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        dto.setPj0101(pj0101);
        dto.setEntrytime(new Date());
        dto.setInOrOut("1");
        //生成班组编号
        String teamsysno = baseDao.createTeamsysno();
        dto.setTeamsysno(teamsysno);
        //校验当前项目下，班组是否存在
        tm01Service.checkTeamName(dto.getTeamname(), pj0101);
        // 如果是分包单位下班组需要上传分包合同至对应分包单位审核
        /*Integer issuppackage = cp02Dao.selectCount(new QueryWrapper<Cp02Entity>()
                .eq("cp0201", dto.getCp0201()).and(
                        query -> query.in("corptype", "1")
                )
        );
        if (issuppackage > 0) {
            Integer tm01AuditCount = tm01AuditDao.selectCount(new QueryWrapper<Tm01AuditEntity>()
                    .eq("pj0101", pj0101)
                    .eq("cp0201", dto.getCp0201())
                    .eq("auditstatus", CommonConstants.TEAM_WAIT_PASS)
                    .eq("teamname", dto.getTeamname())
            );
            if (tm01AuditCount > 0) {
                throw new RenException("当前班组信息已提交企业审核,请勿重复提交");
            }
            Tm01AuditEntity tm01AuditEntity = ConvertUtils.sourceToTarget(dto, Tm01AuditEntity.class);
            tm01AuditDao.insert(tm01AuditEntity);
            List<Ot01DTO> ot01DTOList = dto.getOt01DTOList();
            if (CollectionUtil.isEmpty(ot01DTOList)) {
                throw new RenException("参建单位为分包公司需要上传分包合同");
            } else {
                ot01Service.doFileRelation(ot01DTOList, tm01AuditEntity.getTm0101());
            }
        } else {
            Tm01Entity tm01Entity = ConvertUtils.sourceToTarget(dto, Tm01Entity.class);
            baseDao.insert(tm01Entity);
        }*/
        Tm01Entity tm01Entity = ConvertUtils.sourceToTarget(dto, Tm01Entity.class);
        baseDao.insert(tm01Entity);
    }

    @Override
    public Tm01DetailDTO getTeamInfo(Long teamId) {
        Tm01DTO tm01DTO = get(teamId);

        Tm01DetailDTO tm01DetailDTO = BeanUtil.copyProperties(tm01DTO, Tm01DetailDTO.class);
        tm01DetailDTO.setOt01DTOList(ot01Service.loadBusinessData(teamId, "05"));
        return tm01DetailDTO;
    }

    @Override
    public List<CommonDto> getTeamList() {

        return baseDao.loadTm01InfoAndPassed(SecurityUser.getDeptId());
    }

    @Override
    public void updateTeam(Tm01UpdateVO dto) {
        //获取当前登录用户的项目ID
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        Pj01Entity pj01 = pj01Dao.selectById(pj0101);
        Tm01Entity tm01 = tm01Dao.selectById(dto.getTm0101());
        if (!"0".equals(pj01.getStupstatus()) && !tm01.getCp0201().equals(dto.getCp0201())) {
            throw new RenException("已上报省厅，不允许变更所属单位！");
        }
        //校验当前项目下，班组是否存在
        if (!dto.getTeamname().equals(tm01.getTeamname())) {
            tm01Service.checkTeamName(dto.getTeamname(), pj0101);
        }
        // 如果是专业分包单位需要上传分包合同
        Integer issuppackage = cp02Dao.selectCount(new QueryWrapper<Cp02Entity>()
                .eq("cp0201", dto.getCp0201()).and(
                        query -> query.in("corptype", "1")
                )
        );
        List<Ot01DTO> ot01DTOList = dto.getOt01DTOList();
        if (issuppackage > 0) {
            if (CollectionUtil.isEmpty(ot01DTOList)) {
                throw new RenException("参建单位为分包公司需要上传分包合同");
            }
        }
        ot01Service.doFileRelation(ot01DTOList, tm01.getTm0101());
        BeanUtil.copyProperties(dto, tm01, CopyOptions.create().setIgnoreNullValue(true));
        baseDao.updateById(tm01);
    }

    @Override
    public void entryTeam(Long[] ids) {

        if (ObjectUtil.isEmpty(ids)) {
            throw new RenException("请选择需要操作的班组");
        }
        List<Long> longs = Arrays.asList(ids);

        baseDao.teamInByIds(longs);
        List<PersonDTO> personDTOList = baseDao.selectPersonByTeamIds(longs);

        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.WORKER_TYPE);
    }

    @Override
    public void exitTeam(Long[] ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new RenException("请选择需要操作的班组");
        }
        List<Long> longs = Arrays.asList(ids);

        baseDao.teamOutByIds(longs);

        List<PersonDTO> personDTOList = baseDao.selectPersonByTeamIds(longs);

        supDeviceTaskService.personIntoDevices(personDTOList, CommonConstants.PERSON_DELETE, CommonConstants.WORKER_TYPE);
    }

    @Override
    public Result teamPromiseUpload(Tm01FileUploadVO fileUploadVO) {

        ot01Service.doFileRelation(fileUploadVO.getOt01DTOList(), Long.valueOf(fileUploadVO.getTm0101()));
        return new Result().ok("上传成功");
    }

    @Override
    public Result getTeamPromiseUpload(String tm0101) {

        return new Result().ok(ot01Service.loadBusinessData(Long.valueOf(tm0101), Constant.FileType.TEAMPROMISE.Value()));
    }

    @Override
    public void downloadTeamPromise(HttpServletResponse response) {

        FileDownloadUtil.downloadFile(response, TEAM_PROMISE_EXPORT);
    }


    /**
     * 分包单位绑定：如果是分包单位添加班组，需要上传附件信息。需要分包单位确认
     */
}