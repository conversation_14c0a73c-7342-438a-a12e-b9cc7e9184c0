package io.renren.modules.enterprise.pj04.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj04.dto.Pj04DTO;
import io.renren.modules.enterprise.pj04.entity.Pj04Entity;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目培训详情
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Pj04Dao extends BaseDao<Pj04Entity> {
    /**
     * 查询培训详情
     * @param id pj0301
     * @return
     */
    List<Pj04DTO> selectByPj0301(Long id);

    void batchInsertPj04(List<Pj04Entity> list);

}