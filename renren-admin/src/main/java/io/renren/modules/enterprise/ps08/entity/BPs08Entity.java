package io.renren.modules.enterprise.ps08.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> Chris
 * @Date : 2021-10-11
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS08")
@Accessors(chain = true)
@ApiModel(value = "在线合同信息")
public class BPs08Entity {

    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty("ID")
    private Long ps0801;

    /**
     * 工人ID
     */
    @ApiModelProperty("工人ID")
    private Long ps0201;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Long pj0101;

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    private String contractno;

    /**
     * 统一信用代码
     */
    @ApiModelProperty("统一信用代码")
    private String corpcode;

    /**
     * 甲方（用工企业）
     */
    @ApiModelProperty("甲方（用工企业）")
    private String corpname;

    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人")
    private String legalman;

    /**
     * 单位地址
     */
    @ApiModelProperty("单位地址")
    private String corpaddress;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String linkcellphone;

    /**
     * 乙方姓名
     */
    @ApiModelProperty("乙方姓名")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private String gender;

    /**
     * 出生年月
     */
    @ApiModelProperty("出生年月")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String idcardnumber;

    /**
     * 居住地址
     */
    @ApiModelProperty("居住地址")
    private String address;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String cellphone;

    /**
     * 合同开始时间
     */
    @ApiModelProperty("项目名称")
    private String contractbegin;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectname;

    /**
     * 工种
     */
    @ApiModelProperty("工种")
    private String worktypecode;

    /**
     * 职责
     */
    @ApiModelProperty("职责")
    private String duty;

    /**
     * 工资支付方式
     */
    @ApiModelProperty("工资支付方式")
    private String salarypaymentmethod;

    /**
     * 每月计时工资
     */
    @ApiModelProperty("每月计时工资")
    private String timewage;

    /**
     * 工程名字
     */
    @ApiModelProperty("工程名字")
    private String prjname;

    /**
     * 工作量
     */
    @ApiModelProperty("工作量")
    private String workload;

    /**
     * 工作量工资
     */
    @ApiModelProperty("工作量工资")
    private String workloadwage;

    /**
     * 其他支付形式
     */
    @ApiModelProperty("其他支付形式")
    private String otherwage;

    /**
     * 工资发放日
     */
    @ApiModelProperty("工资发放日")
    private String payday;

    /**
     * 工时制度
     */
    @ApiModelProperty("工时制度")
    private String workhoursystem;

    /**
     * 每天工作小时
     */
    @ApiModelProperty("每天工作小时")
    private String workhour;

    /**
     * 每周工作天数
     */
    @ApiModelProperty("每周工作天数")
    private String workday;

    /**
     * 不定时工时制
     */
    @ApiModelProperty("不定时工时制")
    private String workirregular;

    /**
     * 福利
     */
    @ApiModelProperty("福利")
    private String welfare;

    /**
     * 委托代理人
     */
    @ApiModelProperty("委托代理人")
    private String consignor ;

    /**
     * 工作地点
     */
    @ApiModelProperty("工作地点")
    private String workeraddress;

    /**
     * 其他补充事项
     */
    @ApiModelProperty("其他补充事项")
    private String otherthings;

    /**
     * 签订时间
     */
    @ApiModelProperty("签订时间")
    private String signdate;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;

}
