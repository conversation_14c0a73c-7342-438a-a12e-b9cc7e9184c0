package io.renren.modules.enterprise.pw01.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pw01.dto.Pw01DTO;
import io.renren.modules.enterprise.pw01.entity.Pw01Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工资导入记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Mapper
public interface Pw01Dao extends BaseDao<Pw01Entity> {

    List<Pw01DTO> getList(@Param("page") Page<Pw01DTO> page, @Param("params") Map<String, Object> params);
}