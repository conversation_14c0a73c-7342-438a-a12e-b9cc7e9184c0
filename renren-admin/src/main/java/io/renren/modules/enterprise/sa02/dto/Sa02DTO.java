package io.renren.modules.enterprise.sa02.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
*<AUTHOR>
*@date 2021/10/9
*/

@Data
@ApiModel("网点信息")
public class Sa02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long sa0201;

    @ApiModelProperty(value = "网点名称")
    private String name;

    @ApiModelProperty(value = "网点详细地址")
    private String address;

    @ApiModelProperty(value = "网点所在经度")
    private String lng;

    @ApiModelProperty(value = "网点所在纬度")
    private String lat;

    @ApiModelProperty(value = "网点行政区划")
    private String areacode;

    @ApiModelProperty(value = "网点联系人")
    private String contacts;

    @ApiModelProperty(value = "网点联系电话")
    private String contactnumber;

    @ApiModelProperty(value = "银行代码")
    private String Code;

    @ApiModelProperty(value = "机构号")
    private String institutionnumber;


    @ApiModelProperty(value = "邮箱")
    private String mailbox;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新者")
    private Double updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @ApiModelProperty(value = "创建者")
    private Double creator;

    @ApiModelProperty(value = "上级机构号")
    private String superiorinstitutionnumber;

    @ApiModelProperty(value = "机构id")
    private Double deptId;
}
