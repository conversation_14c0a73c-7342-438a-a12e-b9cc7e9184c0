package io.renren.modules.enterprise.cp02audit.dto;

import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 参建单位审核表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-03
 */
@Data
@ApiModel(value = "参建单位审核表")
public class Cp02AuditDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long cp0201;
    @ApiModelProperty(value = "企业ID")
    private Long cp0101;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "企业名称")
    private String corpname;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "参建类型")
    private String corptype;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核人")
    private String auditor;
    @ApiModelProperty(value = "审核时间")
    private Date auditdate;
    @ApiModelProperty(value = "审核结果")
    private String auditresult;
    @ApiModelProperty(value = "联系人")
    private String linkman;
    @ApiModelProperty(value = "联系电话")
    private String linkcellphone;
    @ApiModelProperty(value = "社会信用代码")
    private String corpcode;

    @ApiModelProperty(value = "合同附件")
    @Valid
    private List<Ot01DTO> ot01DTOList;

}