package io.renren.modules.enterprise.pj03.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.Result;
import io.renren.modules.admin.sys.dao.SysDictDataDao;
import io.renren.modules.admin.sys.entity.DictDTO;
import io.renren.modules.enterprise.pj03.dao.Pj03Dao;
import io.renren.modules.enterprise.pj03.dto.AddExamsDTO;
import io.renren.modules.enterprise.pj03.dto.PagePs02Params;
import io.renren.modules.enterprise.pj03.dto.Pj03DTO;
import io.renren.modules.enterprise.pj03.dto.Pj03DoConfirmDTO;
import io.renren.modules.enterprise.pj03.entity.Pj03Entity;
import io.renren.modules.enterprise.pj03.service.Pj03Service;
import io.renren.modules.enterprise.pj04.dao.Pj04Dao;
import io.renren.modules.enterprise.pj04.entity.Pj04Entity;
import io.renren.modules.enterprise.pj05.dao.Pj05Dao;
import io.renren.modules.enterprise.pj05.dto.Pj05DTO;
import io.renren.modules.enterprise.pj05.entity.Pj05Entity;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps02.dto.Ps02PageDTO;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 项目培训信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Pj03ServiceImpl extends CrudServiceImpl<Pj03Dao, Pj03Entity, Pj03DTO> implements Pj03Service {
    @Autowired
    private Pj04Dao pj04Dao;
    @Autowired
    private Pj05Dao pj05Dao;
    @Autowired
    private SysDictDataDao sysDictDataDao;
    @Autowired
    private Ps02Dao ps02Dao;
    @Autowired
    private Ot01Service ot01Service;

    @Override
    public QueryWrapper<Pj03Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pj03Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePj03Info(Pj03DTO dto) {
        Pj03Entity pj03Entity = BeanUtil.copyProperties(dto, Pj03Entity.class);
        pj03Entity.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        baseDao.insert(pj03Entity);

        List<Pj04Entity> list = ps02Dao.selectList(new QueryWrapper<Ps02Entity>()
                .in("tm0101", dto.getTm0101List())
                .eq("in_or_out", "1")).stream().map(x -> {
            Pj04Entity pj04Entity = new Pj04Entity();
            pj04Entity.setPj0401(IdWorker.getId());
            pj04Entity.setPj0301(pj03Entity.getPj0301());
            pj04Entity.setPs0201(x.getPs0201());
            return pj04Entity;
        }).collect(Collectors.toList());
        if (list.size() > 0) {
            pj04Dao.batchInsertPj04(list);
        }
        // 关联附件
        if (!Objects.isNull(dto.getOt01DTOList()) && dto.getOt01DTOList().size() > 0) {
            ot01Service.updateBusily(pj03Entity.getPj0301(), dto.getOt01DTOList().stream().map(Ot01DTO::getOt0101).collect(Collectors.toList()));
        }

    }

    @Override
    public void updatePj03Info(Pj03DTO dto) {

    }

    @Override
    public PageData<Pj03DTO> pageList(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<Pj03Entity> page = getPage(params, "t.CREATE_DATE", false);
        List<Pj03DTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Pj03DTO.class);
    }

    @Override
    public List<DictDTO> getTeamList() {

        return sysDictDataDao.getTeamCreditList(CommonUtils.userProjectInfo().getPj0101());
    }

    @Override
    public PageData<Ps02PageDTO> pagePs02ByTm0101(PagePs02Params params) {
        Page<Ps02PageDTO> page = new Page<>(params.getPage(), params.getLimit());
        List<Long> tm0101List = params.getTm0101List();
        List<Ps02PageDTO> list = tm0101List.size() > 0 ? baseDao.pagePs02ByTm0101(page, tm0101List) : Collections.emptyList();
        return getPageData(list, page.getTotal(), Ps02PageDTO.class);
    }

    @Override
    public PageData<Ps02PageDTO> pagePs02(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Page<Ps02PageDTO> page = new Page<>(curPage, limit);
        List<Ps02PageDTO> list = baseDao.pagePs02(page, params);
        return getPageData(list, page.getTotal(), Ps02PageDTO.class);
    }

    @Override
    public Result addExams(AddExamsDTO dto) {
        AtomicLong num = new AtomicLong();
        dto.getPj05DTOList().forEach(x -> {
            Pj05Entity pj05Entity = BeanUtil.copyProperties(x, Pj05Entity.class);
            pj05Entity.setPj0301(dto.getPj0301());
            pj05Entity.setSno(String.valueOf(num.incrementAndGet()));
            pj05Dao.insert(pj05Entity);
        });

        return new Result();
    }

    @Override
    public Result doConfirm(Pj03DoConfirmDTO dto) {

        Pj03Entity pj03Entity = new Pj03Entity();
        pj03Entity.setPj0301(Long.valueOf(dto.getPj0301()));
        pj03Entity.setIsConfirm(dto.getConfirmStatus());
        return baseDao.updateById(pj03Entity) > 0? new Result().ok("操作成功"): new Result().error("操作失败");
    }

    @Override
    public Result<List<Pj05DTO>> examInfo(Long pj0301) {
        List<Pj05DTO> pj05List = pj05Dao.getExamInfo(pj0301);

        return new Result<List<Pj05DTO>>().ok(pj05List);
    }
}