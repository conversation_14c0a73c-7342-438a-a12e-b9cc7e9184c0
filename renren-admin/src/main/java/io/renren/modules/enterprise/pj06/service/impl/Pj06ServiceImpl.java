package io.renren.modules.enterprise.pj06.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.redis.RedisKeys;
import io.renren.common.redis.RedisUtils;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.message.service.SysSmsService;
import io.renren.modules.admin.sys.dao.SysDeptDao;
import io.renren.modules.admin.sys.dao.SysRoleDao;
import io.renren.modules.admin.sys.dao.SysUserDao;
import io.renren.modules.admin.sys.dto.SysUserDTO;
import io.renren.modules.admin.sys.entity.SysDeptEntity;
import io.renren.modules.admin.sys.entity.SysUserEntity;
import io.renren.modules.admin.sys.service.SysParamsService;
import io.renren.modules.admin.sys.service.SysUserService;
import io.renren.modules.enterprise.cp01.dao.Cp01Dao;
import io.renren.modules.enterprise.cp02.dao.Cp02Dao;
import io.renren.modules.enterprise.cp03.service.BCp03Service;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.pj01report.dao.Pj01ReportDao;
import io.renren.modules.enterprise.pj01report.entity.Pj01ReportEntity;
import io.renren.modules.enterprise.pj06.dao.Pj06Dao;
import io.renren.modules.enterprise.pj06.dto.Pj06DTO;
import io.renren.modules.enterprise.pj06.entity.Pj06Entity;
import io.renren.modules.enterprise.pj06.service.Pj06AsyncService;
import io.renren.modules.enterprise.pj06.service.Pj06Service;
import io.renren.modules.enterprise.pj06audit.dao.Pj06AuditDao;
import io.renren.modules.ot01.service.Ot01Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class Pj06ServiceImpl extends CrudServiceImpl<Pj06Dao, Pj06Entity, Pj06DTO> implements Pj06Service {
    @Autowired
    private Pj06Service pj06Service;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Pj06Dao pj06Dao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private SysDeptDao sysDeptDao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysSmsService sysSmsService;
    @Autowired
    private SysParamsService sysParamsService;
    @Autowired
    private BCp03Service cp03Service;
    @Autowired
    private Pj01ReportDao pj01ReportDao;
    @Autowired
    private Pj06AuditDao pj06AuditDao;
    @Autowired
    private SysUserDao userDao;
    @Autowired
    private SysRoleDao sysRoleDao;
    @Autowired
    private Pj06AsyncService pj06AsyncService;
    @Autowired
    private RedisUtils redisUtils;
    private static final String REVIEW_SUCCESS = "1";
    private static final String REVIEW_ERROR = "2";

    @Override
    public QueryWrapper<Pj06Entity> getWrapper(Map<String, Object> params) {
        String name = (String) params.get("name");
        QueryWrapper<Pj06Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(name), "name", name);
        wrapper.orderByDesc("CREATE_DATE");
        return wrapper;
    }

    @Override
    public void saveCheckProjectInfo(Pj06DTO dto) {
        /*if (REVIEW_SUCCESS.equals(dto.getStatus())) {
            //数据写入到SYS_DEPT,创建机构
            Long deptId = pj06Service.saveDeptInfo(dto);
            //数据写入到PJ01,新增项目基础信息
            Long pj0101 = pj06Service.savePj01Info(dto, deptId);
//            //保存建设单位信息
//            pj06Service.saveConstruction(dto, pj0101);
//            //保存总包单位信息
//            pj06Service.saveContractUnit(dto, pj0101);
            //数据写入到SYS_USER,创建用户
            SysUserDTO sysUserDTO = pj06Service.saveUserInfo(dto, deptId);
            //调用生成关系表过程
            sysUserDao.callRPj01Dept(pj0101);
            //写入项目上报配置表
            // 上报地
            //String report = dto.getReport();
            // 获取上报银行
            *//*String payBankCode = "";
            if (StringUtils.isNotBlank(dto.getPayBankCode())) {
                payBankCode = dto.getPayBankCode();
            }*//*
            //pj06Service.savePj01Report(pj0101, report,  payBankCode);
            // 写入省级编码表
            *//*Dt04Entity dt04Entity = new Dt04Entity();
            dt04Entity.setPj0101(pj0101);
            dt04Dao.insert(dt04Entity);*//*
            //发送用户名和密码到注册项目的手机号码
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("username", sysUserDTO.getUsername());
            jsonObject.put("password", sysUserDTO.getPassword());
            sysSmsService.send("1001", dto.getLinkphone(), jsonObject.toJSONString());
        } else if (REVIEW_ERROR.equals(dto.getStatus())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("notPassMsg", dto.getMemo());
            sysSmsService.send("1002", dto.getLinkphone(), jsonObject.toJSONString());
        }
        //更新项目审核数据表PJ06
        pj06Service.update(dto);*/
    }

    @Override
    public Result saveProjectInfo(Pj06DTO dto) {
        Result<Object> result = new Result<>();
        // 校验验证码是否正确
        String registerKey = RedisKeys.getProjectRegisterKey(dto.getLinkphone());
        if (!StrUtil.equals(dto.getRegisterCode(), redisUtils.getString(registerKey))) {
            throw new RenException("输入验证码错误");
        }
        //校验参建单位一致性
        boolean flag = checkSameCompany(dto.getConstructcode(), dto.getContractcode(), dto.getSupervisecode());
        if (flag) {
            throw new RenException("三方主体不能为同一单位，请检查后重试！");
        }
        //判断项目是否已存在
        Long pj01Number = pj06Dao.getPj01Number(dto.getName());
        if (pj01Number > 0) {
            throw new RenException("项目已存在,请勿重复注册！");
        }
        //判断项目是否在待审核列表中
        Long projectNumber = pj06Dao.getProjectNumber(dto.getName());
        if (projectNumber > 0) {
            throw new RenException("项目已注册,请您耐心等待审核！");
        }
        //判断手机号是否已注册
        SysUserEntity user = userDao.selectOne(new QueryWrapper<SysUserEntity>().eq("USERNAME", dto.getLinkphone()));
        if (user != null) {
            throw new RenException("该手机号已注册，请更换手机号重试！");
        }
        //校验手机号用户是否已存在待审核列表
        List<Pj06Entity> waitusers = pj06Dao.selectList(new QueryWrapper<Pj06Entity>().eq("LINKPHONE", dto.getLinkphone()).eq("AUDITSTATUS","0"));
        if (waitusers.size() > 0) {
            throw new RenException("该手机号已绑定待审核项目，请更换手机号重试！");
        }
        // 将数据写入审核表
        Pj06Entity pj06 = new Pj06Entity();
        BeanUtil.copyProperties(dto, pj06, CopyOptions.create().setIgnoreNullValue(true));
        pj06.setIndustry("1");
        baseDao.insert(pj06);
        // 操作成功之后 删除验证码
        redisUtils.delete(registerKey);
        //绑定注册附件
        ot01Service.doFileRelation(dto.getRegisterFiles(), pj06.getPj0601());
        return result;
    }

    private boolean checkSameCompany(Long constructcode, Long contractcode, Long supervisecode) {
        if (constructcode.equals(contractcode) || constructcode.equals(supervisecode) || contractcode.equals(supervisecode)) {
            return true;
        }
        return false;
    }

    //数据写入到SYS_DEPT,创建机构
    @Override
    public Long saveDeptInfo(Pj06DTO dto) {
        SysDeptEntity sysDeptEntity = new SysDeptEntity();
        // 主管部门areacode
        sysDeptEntity.setAreacode(dto.getVirareacode());
        sysDeptEntity.setPid(92733L);
        sysDeptEntity.setName(dto.getName());
        sysDeptDao.insert(sysDeptEntity);
        return sysDeptEntity.getId();
    }

    @Override
    public Long savePj01Info(Pj06DTO dto, Long deptId) {
        Pj01Entity pj01Entity = new Pj01Entity();
        pj01Entity.setName(dto.getName());
        pj01Entity.setAreacode(dto.getAreacode());
        pj01Entity.setLinkman(dto.getLinkman());
        pj01Entity.setLinkphone(dto.getLinkphone());
        pj01Entity.setDeptId(deptId);
        //新注册的项目默认设置为在建项目
        pj01Entity.setPrjstatus("3");
        pj01Dao.insert(pj01Entity);
        return pj01Entity.getPj0101();
    }

    @Override
    public void saveConstruction(Pj06DTO dto, Long pj0101) {
//        Cp01DTO cp01DTO = cp01Dao.loadCp01(dto.getConstructionnumber());
//        Cp02Entity cp02Entity = new Cp02Entity();
//        //如果参建不存在就新增信息到CP01
//        if (ObjectUtil.isNull(cp01DTO)) {
//            Cp01Entity construction = new Cp01Entity();
//            construction.setCorpname(dto.getConstructionname());
//            construction.setCorpcode(dto.getConstructionnumber());
//            cp01Dao.insert(construction);
//            cp02Entity.setCp0101(construction.getCp0101());
//            //获取企业工商信息
//            pj06AsyncService.saveEnterpriseBusinessInfo(construction.getCp0101(), dto.getConstructionnumber());
//        } else {
//            cp02Entity.setCp0101(cp01DTO.getCp0101());
//        }
//        cp02Entity.setCorptype("8");
//        cp02Entity.setPj0101(pj0101);
//        cp02Entity.setEntrytime(new Date());
//        cp02Dao.insert(cp02Entity);
//        //保存进退场记录表
//        BCp03DTO cp03DTO = new BCp03DTO();
//        cp03DTO.setCp0201(cp02Entity.getCp0201());
//        cp03DTO.setEntryOrExitTime(cp02Entity.getEntrytime());
//        cp03DTO.setInOrOut("1");
//        cp03Service.save(cp03DTO);
    }

    @Override
    public void saveContractUnit(Pj06DTO dto, Long pj0101) {
//        Cp01DTO cp01DTO = cp01Dao.loadCp01(dto.getContractnumber());
//        Cp02Entity cp02Entity = new Cp02Entity();
//        if (ObjectUtil.isNull(cp01DTO)) {
//            Cp01Entity cp01Entity = new Cp01Entity();
//            cp01Entity.setCorpname(dto.getContractname());
//            cp01Entity.setCorpcode(dto.getContractnumber());
//            cp01Dao.insert(cp01Entity);
//            cp02Entity.setCp0101(cp01Entity.getCp0101());
//            //获取企业工商信息
//            pj06AsyncService.saveEnterpriseBusinessInfo(cp01Entity.getCp0101(), dto.getContractnumber());
//        } else {
//            cp02Entity.setCp0101(cp01DTO.getCp0101());
//        }
//        cp02Entity.setCorptype("9");
//        cp02Entity.setPj0101(pj0101);
//        cp02Entity.setEntrytime(new Date());
//        cp02Dao.insert(cp02Entity);
//        //保存进退场记录表
//        BCp03DTO cp03DTO = new BCp03DTO();
//        cp03DTO.setCp0201(cp02Entity.getCp0201());
//        cp03DTO.setEntryOrExitTime(cp02Entity.getEntrytime());
//        cp03DTO.setInOrOut("1");
//        cp03Service.save(cp03DTO);
    }

    @Override
    public SysUserDTO saveUserInfo(Pj06DTO dto, Long deptId) {
        SysUserDTO sysUserDTO = new SysUserDTO();
        sysUserDTO.setPassword(sysParamsService.getValue(Constant.PASSWORD));
        sysUserDTO.setDeptId(deptId);
        sysUserDTO.setRealName(dto.getLinkman());
        sysUserDTO.setMobile(dto.getLinkphone());
        sysUserDTO.setSuperAdmin(0);
        sysUserDTO.setStatus(1);
        sysUserDTO.setUserType("1");
        //生成用户名
        sysUserDTO.setUsername(pj06Service.generateUserName());
        // 用户角色
        List collect = new ArrayList();
        /*String[] split = dto.getRoleIdList().split(",");
        for (String s : split) {
            collect.add(Long.valueOf(s));
        }*/
        collect.add(1254300243697975297L);
        sysUserDTO.setRoleIdList(collect);
        sysUserService.save(sysUserDTO);
        return sysUserDTO;
    }

    @Override
    public String generateUserName() {
        String userName;
        //先查询当前行政区划的账号使用到哪个数字
        Long lastname = pj06Dao.selectUserName();
        userName = "YB" + lastname;
        return userName;
    }

    @Override
    public void savePj01Report(Long pj0101, String report, String payBankCode) {
        Pj01ReportEntity pj01ReportEntity = new Pj01ReportEntity();
        pj01ReportEntity.setPj0101(pj0101);
        pj01ReportEntity.setReportType(report);
        pj01ReportEntity.setPayBankCode(payBankCode);
        pj01ReportDao.insert(pj01ReportEntity);
    }

    @Override
    public List<CommonDto> getRoleList() {

        List<CommonDto> roleList = sysRoleDao.getRoleList();

        return roleList;
    }

    @Override
    public Result sendCode(String phone) {
        if (!Validator.isMatchRegex(PatternPool.MOBILE, phone)) {
            return new Result().error("请输入正确的手机号码");
        }

        String code = RandomUtil.randomNumbers(4);
        redisUtils.set(RedisKeys.getProjectRegisterKey(phone), code, 60 * 5);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        sysSmsService.send("1003", phone, jsonObject.toJSONString());

        return new Result().ok("发送成功");
    }


    /**
     * 添加参建单位到企业确认表
     *
     * @param pj06DTO
     */
    private void savePj06Audit(Pj06DTO pj06DTO) {
        // 一个企业只能在项目中存在一个参建类型
        /*if (StringUtils.equalsAny(pj06DTO.getConstructionunit(), pj06DTO.getConstructionNo(), pj06DTO.getSupervisionnumber())) {
            throw new RenException("一个企业只能在项目中存在一个参建类型");
        }
        // 总包单位
        Pj06AuditEntity pj06Constructionunit = new Pj06AuditEntity();
        pj06Constructionunit.setPj0601(pj06DTO.getPj0601());
        pj06Constructionunit.setCp0101(Long.valueOf(pj06DTO.getConstructionunit()));
        pj06Constructionunit.setCorptype(Constant.CorpInType.CONSTRUCTIONUNIT.getValue());
        pj06AuditDao.insert(pj06Constructionunit);
        // 建设单位
        Pj06AuditEntity pj06ConstructionNo = new Pj06AuditEntity();
        pj06ConstructionNo.setPj0601(pj06DTO.getPj0601());
        pj06ConstructionNo.setCp0101(Long.valueOf(pj06DTO.getConstructionNo()));
        pj06ConstructionNo.setCorptype(Constant.CorpInType.OWNER.getValue());
        pj06AuditDao.insert(pj06ConstructionNo);
        // 监理单位
        Pj06AuditEntity pj06Supervisionnumber = new Pj06AuditEntity();
        pj06Supervisionnumber.setPj0601(pj06DTO.getPj0601());
        pj06Supervisionnumber.setCp0101(Long.valueOf(pj06DTO.getSupervisionnumber()));
        pj06Supervisionnumber.setCorptype(Constant.CorpInType.SUPERVISION.getValue());
        pj06AuditDao.insert(pj06Supervisionnumber);*/
    }

}