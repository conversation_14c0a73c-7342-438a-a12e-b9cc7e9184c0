package io.renren.modules.enterprise.ps06.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.ps06.dao.BPs06Dao;
import io.renren.modules.enterprise.ps06.dto.BPs06DTO;
import io.renren.modules.enterprise.ps06.entity.BPs06Entity;
import io.renren.modules.enterprise.ps06.service.BPs06Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工人进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-04-27
 */
@Service
public class BPs06ServiceImpl extends CrudServiceImpl<BPs06Dao, BPs06Entity, BPs06DTO> implements BPs06Service {

    @Override
    public QueryWrapper<BPs06Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<BPs06Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    /**
     * 构建Ps06集合
     *
     * @param ps0201s          工人ids
     * @param inOrOut         进退场状态
     * @return
     */
    @Override
    public void batchInsertInOrOut(List<Long> ps0201s,  String inOrOut) {

        Long userId = SecurityUser.getUserId();
        if (CollUtil.isNotEmpty(ps0201s)) {
            List<BPs06DTO> collect = ps0201s.stream().map(item -> {
                BPs06DTO ps06DTO = new BPs06DTO();
                ps06DTO.setPs0601(IdWorker.getId());
                ps06DTO.setPs0201(item);
                ps06DTO.setEntryOrExitTime(new Date());
                ps06DTO.setInOrOut(inOrOut);
                ps06DTO.setCreateDate(new Date());
                ps06DTO.setUpdateDate(new Date());
                return ps06DTO;
            }).collect(Collectors.toList());
            batchInsertInOrOut(collect);
        }
    }

    /**
     * 记录进退场（单）
     *
     * @param bPs06DTO
     */
    @Override
    public void batchInsertInOrOut(BPs06DTO bPs06DTO) {
        if (ObjectUtil.isNull(bPs06DTO)) {
            return;
        }
        List<BPs06DTO> bPs06DTOS = Collections.singletonList(bPs06DTO);
        batchInsertInOrOut(bPs06DTOS);
    }

    /**
     * 记录进退场（批量）
     *
     * @param dtoList
     */
    @Override
    public void batchInsertInOrOut(List<BPs06DTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        baseDao.batchInsertInOrOut(dtoList);
    }
}