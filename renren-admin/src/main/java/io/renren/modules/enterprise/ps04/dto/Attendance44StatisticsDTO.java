package io.renren.modules.enterprise.ps04.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 到岗率-项目列表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "到岗率-项目列表")
public class Attendance44StatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sno;
    @ApiModelProperty(value = "项目名称")
    private String projectname;
    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String entrytime;
    @ApiModelProperty(value = "录入系统时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String createDate;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "证件号码")
    private String idcardnumber;

    @ApiModelProperty(value = "工号")
    private Long userId;

    @ApiModelProperty(value = "参建单位")
    private String corpname;

    @ApiModelProperty(value = "岗位")
    private String jobtype;

    @ApiModelProperty(value = "考勤天数")
    private String kqs;

    @ApiModelProperty(value = "选择天数")
    private String days;

    @ApiModelProperty(value = "到岗率")
    private String arrivepercent;

    @ApiModelProperty(value = "今日考勤")
    private String todaykqs;

    @ApiModelProperty(value = "考勤列表")
    private List<AttendanceMonthDTO> kqlist;

}