package io.renren.modules.enterprise.pj01info.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj01info.dto.Pj01InfoDTO;
import io.renren.modules.enterprise.pj01info.entity.Pj01InfoEntity;
import org.apache.ibatis.annotations.Mapper;
import io.renren.modules.enterprise.pj01info.dto.*;
import java.util.List;
import java.util.Map;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Mapper
public interface Pj01InfoDao extends BaseDao<Pj01InfoEntity> {
    /**
     * 列表分页查询数据
     * @param params
     * @return
     */
    List<Pj01InfoDTO> getListData(Map<String, Object> params);
    /**
     * 项目查询获取参建单位
     * @param params
     * @return
     */
    List<Cp02PageDTO> getCp02PageList(Map<String, Object> params);
    /**
     * 项目查询获取班组
     * @param params
     * @return
     */
    List<Tm01PageDTO> getTeamPageList(Map<String, Object> params);
    /**
     * 项目查询获取工人
     * @param params
     * @return
     */
    List<Ps02PageDTO> getWorkerPageList(Map<String, Object> params);
    /**
     * 项目查询获取考勤
     * @param params
     * @return
     */
    List<Kq02PageDTO> getAttendancePageList(Map<String, Object> params);
    /**
     * 项目查询获取管理人员
     * @param params
     * @return
     */
    List<Ps04PageDTO> getManagerPageList(Map<String, Object> params);
}