package io.renren.modules.enterprise.cp04audit.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.cp04.dto.Cp04DetailDTO;
import io.renren.modules.enterprise.cp04.dto.Cp04PageDTO;
import io.renren.modules.enterprise.cp04.entity.Cp04Entity;
import io.renren.modules.enterprise.cp04audit.dao.Cp04AuditDao;
import io.renren.modules.enterprise.cp04audit.dto.Cp04AuditDTO;
import io.renren.modules.enterprise.cp04audit.dto.Cp04AuditDetailDTO;
import io.renren.modules.enterprise.cp04audit.dto.Cp04AuditPageDTO;
import io.renren.modules.enterprise.cp04audit.entity.Cp04AuditEntity;
import io.renren.modules.enterprise.cp04audit.service.Cp04AuditService;
import io.renren.modules.ot01.service.Ot01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 分包信息审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@Service
public class Cp04AuditServiceImpl extends CrudServiceImpl<Cp04AuditDao, Cp04AuditEntity, Cp04AuditDTO> implements Cp04AuditService {

    @Autowired
    private Ot01Service ot01Service;

    @Override
    public QueryWrapper<Cp04AuditEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Cp04AuditEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Cp04AuditPageDTO> getPageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId()) ;
        IPage<Cp04AuditEntity> page = getPage(params, "", false);
        List<Cp04AuditPageDTO> pageList = baseDao.pageList(params);
        return getPageData(pageList, page.getTotal(), Cp04AuditPageDTO.class);
    }

    @Override
    public Cp04AuditDetailDTO getInfo(Long cp0401) {
        Cp04AuditDetailDTO cp04DetailDTO = new Cp04AuditDetailDTO();
        Cp04AuditEntity cp04Entity = baseDao.selectOne(new QueryWrapper<Cp04AuditEntity>()
                .eq("cp0401",cp0401));
        if (ObjectUtil.isNotNull(cp04Entity)) {
            BeanUtils.copyProperties(cp04Entity, cp04DetailDTO);
            cp04DetailDTO.setOt01DTOList(ot01Service.loadBusinessData(cp0401, "06"));
        }
        return cp04DetailDTO;
    }
}