package io.renren.modules.enterprise.ps09.dto;

import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工人工资附件表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-22
 */
@Data
@ApiModel(value = "工人工资附件表")
public class Ps09DTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键")
	private BigDecimal ps0901;
		@ApiModelProperty(value = "项目ID")
	private BigDecimal pj0101;
		@ApiModelProperty(value = "发放月份")
	private Date yearmonth;
		@ApiModelProperty(value = "是否上传")
	private Long isupload;
		@ApiModelProperty(value = "文件")
		private List<Ot01DTO> ot01DTO;
				
}