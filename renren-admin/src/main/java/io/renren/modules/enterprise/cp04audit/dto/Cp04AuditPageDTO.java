package io.renren.modules.enterprise.cp04audit.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分页
 * <AUTHOR> chris
 * @Date : 2022-11-22
 **/
@Data
public class Cp04AuditPageDTO {

    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long cp0401;

    @ApiModelProperty(value = "项目ID")
    private String pjname;

    @ApiModelProperty(value = "分包单位ID")
    private String cp0201;

    @ApiModelProperty(value = "分包单位ID")
    private String cpname;

    @ApiModelProperty(value = "分包工程名称")
    private String subcontractname;

    @ApiModelProperty(value = "分包工程内容")
    private String subcontractcontent;

    @ApiModelProperty(value = "开工时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "计划完工时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completedate;

    @ApiModelProperty(value = "分包价格（万元）")
    private BigDecimal subcontractprice;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;


}
