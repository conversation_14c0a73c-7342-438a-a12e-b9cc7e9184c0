package io.renren.modules.enterprise.pj06audit.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.pj06audit.dto.Pj06AuditDTO;
import io.renren.modules.enterprise.pj06audit.excel.Pj06AuditExcel;
import io.renren.modules.enterprise.pj06audit.service.Pj06AuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * ${comments}
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-27
 */
@RestController
@RequestMapping("enterprise/pj06audit")
@Api(tags = "${comments}")
public class Pj06AuditController {
    @Autowired
    private Pj06AuditService pj06AuditService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:pj06audit:page")
    public Result<PageData<Pj06AuditDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Pj06AuditDTO> page = pj06AuditService.page(params);

        return new Result<PageData<Pj06AuditDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pj06audit:info")
    public Result<Pj06AuditDTO> get(@PathVariable("id") Long id) {
        Pj06AuditDTO data = pj06AuditService.get(id);

        return new Result<Pj06AuditDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:pj06audit:save")
    public Result save(@RequestBody Pj06AuditDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj06AuditService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:pj06audit:update")
    public Result update(@RequestBody Pj06AuditDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj06AuditService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pj06audit:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj06AuditService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:pj06audit:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Pj06AuditDTO> list = pj06AuditService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Pj06AuditExcel.class);
    }

}