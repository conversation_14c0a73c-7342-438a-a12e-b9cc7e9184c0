package io.renren.modules.enterprise.ps03.dto;

import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;
import java.util.List;

/**
 * 管理人员在职信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@Data
@ApiModel(value = "管理人员在职信息表")
public class Ps03DTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键ID")
	private Long ps0301;
		@ApiModelProperty(value = "当前项目ID")
	private Long pj0101;
		@ApiModelProperty(value = "在职企业ID")
	private Long cp0101;
		@ApiModelProperty(value = "人员ID")
	private Long ps0101;
		@ApiModelProperty(value = "在职状态")
	private String managestatus;
		@ApiModelProperty(value = "头像采集照片")
	private String photo;
		@ApiModelProperty(value = "是否购买工伤或意外伤害保险")
	private String hasbuyinsurance;
		@ApiModelProperty(value = "入职时间")
	private Date inductiontime;
		@ApiModelProperty(value = "离职时间")
	private Date departuretime;
		@ApiModelProperty(value = "证书")
	private List<Ot01DTO> certificateFiles;
		@ApiModelProperty(value = "社保")
	private List<Ot01DTO> insuranceFiles;
		@ApiModelProperty(value = "附件id")
	private List<Ot01DTO> ot0101s;
		@ApiModelProperty(value = "人员基础信息")
		@Valid
	private Ps01DTO ps01DTO;

				
}