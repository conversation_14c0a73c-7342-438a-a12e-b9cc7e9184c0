package io.renren.modules.enterprise.excelexp.dao;

import io.renren.modules.enterprise.excelexp.dto.AttendanceRecordDTO;
import io.renren.modules.enterprise.excelexp.dto.PayrollRegisterDTO;
import io.renren.modules.enterprise.excelexp.dto.WorkerRecordDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 三表导出
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-18
 */
@Mapper
public interface ExcelExpDao {

    /**
     *工资发放花名册
     */
    List<PayrollRegisterDTO> getPayrollRegisterDTOList(Map<String, Object> params);

    /**
     *用工备案表
     */
    List<WorkerRecordDTO> getWorkerRecordDTOList(Map<String, Object> params);

    /**
     *考勤统计表
     */
    List<AttendanceRecordDTO> getAttendanceRecordDTOList(Map<String, Object> params);

}