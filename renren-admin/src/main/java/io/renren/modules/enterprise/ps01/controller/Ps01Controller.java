package io.renren.modules.enterprise.ps01.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps01.dto.PersonInfoDTO;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-06-30
 */
@RestController
@RequestMapping("enterprise/ps01")
@Api(tags = "人员实名基础信息")
public class Ps01Controller {
    @Autowired
    private Ps01Service ps01Service;


    @GetMapping("getManageList")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    public Result<PageData<Ps01DTO>> getManageList(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps01DTO> page = ps01Service.getManageList(params);

        return new Result<PageData<Ps01DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps01:info")
    public Result<Ps01DTO> get(@PathVariable("id") Long id) {
        Ps01DTO data = ps01Service.getPs01Info(id);

        return new Result<Ps01DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:ps01:save")
    public Result save(@RequestBody Ps01DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps01Service.savePs01Info(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:ps01:update")
    public Result update(@RequestBody Ps01DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps01Service.updatePs01Info(dto);

        return new Result();
    }


    @GetMapping("/getPersonInfo")
    @ApiOperation("获取人员信息")
    @LogOperation("获取人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idcardnumber", value = "身份证号码", paramType = "query", required = true, dataType = "string")
    })
//    @RequiresPermissions("enterprise:ps01:getPersonInfo")
    public Result<PersonInfoDTO> getPersonInfo(@ApiIgnore @RequestParam Map<String, Object> params) {

        return ps01Service.getPersonInfo(params);
    }
}