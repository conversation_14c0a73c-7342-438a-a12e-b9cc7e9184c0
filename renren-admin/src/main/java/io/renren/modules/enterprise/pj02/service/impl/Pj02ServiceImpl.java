package io.renren.modules.enterprise.pj02.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.renren.modules.enterprise.pj02.dao.Pj02Dao;
import io.renren.modules.enterprise.pj02.dto.Pj02DTO;
import io.renren.modules.enterprise.pj02.entity.Pj02Entity;
import io.renren.modules.enterprise.pj02.service.Pj02Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目施工许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Pj02ServiceImpl extends ServiceImpl<Pj02Dao, Pj02Entity> implements Pj02Service {
    @Autowired
    private Pj02Dao pj02Dao;

    @Override
    public Pj02DTO getByPj0101(Long id) {
        return pj02Dao.getPj02List(id);
    }
}