package io.renren.modules.enterprise.tm02.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 班组进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-26
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_TM02")
public class BTm02Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long tm0201;
    /**
     * 班组ID
     */
	private Long tm0101;
    /**
     * 进退场时间
     */
	private Date entryOrExitTime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 备注
     */
	private String memo;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}