package io.renren.modules.enterprise.cg10.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.enterprise.cg09.dao.Cg09Dao;
import io.renren.modules.enterprise.cg09.dto.Cg09DTO;
import io.renren.modules.enterprise.cg10.dao.Cg10Dao;
import io.renren.modules.enterprise.cg10.dto.Cg10DTO;
import io.renren.modules.enterprise.cg10.dto.Cg10Page;
import io.renren.modules.enterprise.cg10.entity.Cg10Entity;
import io.renren.modules.enterprise.cg10.service.Cg10Service;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 项目档案上传
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-14
 */
@Service
public class Cg10ServiceImpl extends CrudServiceImpl<Cg10Dao, Cg10Entity, Cg10DTO> implements Cg10Service {
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Cg09Dao cg09Dao;
    private static final String FILE_TYPE = "703";


    @Override
    public QueryWrapper<Cg10Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Cg10Entity> wrapper = new QueryWrapper<>();

        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInfo(Cg10DTO dto) {
        // 保存配置信息
        Cg10Entity entity = BeanUtil.copyProperties(dto, Cg10Entity.class);
        entity.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        entity.setState("0");
        baseDao.insert(entity);
        // 保存附件信息
        if (CollectionUtils.isNotEmpty(dto.getFileList())) {
            ot01Service.doFileRelation(dto.getFileList(), entity.getCg1001());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfo(Cg10DTO dto) {
        // 保存配置信息
        Cg10Entity entity = BeanUtil.copyProperties(dto, Cg10Entity.class);
        entity.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        baseDao.updateById(entity);
        // 保存附件信息
        if (CollectionUtils.isNotEmpty(dto.getFileList())) {
            ot01Service.doFileRelation(dto.getFileList(), entity.getCg1001());
        }
    }

    @Override
    public Cg10DTO getInfo(Long id) {
        Cg10Entity entity = baseDao.selectById(id);
        Cg10DTO dto = BeanUtil.copyProperties(entity, Cg10DTO.class);
        List<Ot01DTO> ot01List = ot01Service.loadBusinessData(entity.getCg0901(), FILE_TYPE);
        dto.setFileList(ot01List);
        return dto;
    }

    @Override
    public PageData<Cg10Page> pageList(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        params.put("fileType", FILE_TYPE);
        IPage<Cg10Entity> page = getPage(params, "", false);
        List<Cg10Page> list = baseDao.selectPageList(params);
        return getPageData(list, page.getTotal(), Cg10Page.class);
    }

    @Override
    public Cg10DTO getArchInfo(Map<String, Object> params) {
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        params.put("fileType", "702");
        Cg10DTO dto = baseDao.selectArchInfo(params);
        Cg09DTO cg09DTO = cg09Dao.selectConfigInfo(params);
        if (dto != null && ObjectUtil.isNotEmpty(dto)) {
            List<Ot01DTO> ot01List = ot01Service.loadBusinessData(dto.getCg1001(), FILE_TYPE);
            dto.setFileList(ot01List);
        } else {
            dto = new Cg10DTO();
        }
        if (cg09DTO != null) {
            dto.setTemplateUrl(cg09DTO.getTemplateUrl());
        }
        return dto;
    }

}