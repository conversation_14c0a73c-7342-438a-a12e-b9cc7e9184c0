package io.renren.modules.enterprise.cp02audit.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDTO;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDetailDTO;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditPageDTO;
import io.renren.modules.enterprise.cp02audit.entity.Cp02AuditEntity;

import java.util.Map;

/**
 * 参建单位审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
public interface Cp02AuditService extends CrudService<Cp02AuditEntity, Cp02AuditDTO> {


    /**
     * 分页
     * @param params
     * @return
     */
    PageData<Cp02AuditPageDTO> getPageList(Map<String, Object> params);


    /**
     * 详情
     * @param cp0201
     * @return
     */
    Cp02AuditDetailDTO getInfo(Long cp0201) ;
}