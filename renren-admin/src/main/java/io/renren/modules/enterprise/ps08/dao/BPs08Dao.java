package io.renren.modules.enterprise.ps08.dao;

import io.renren.common.dao.BaseDao;
import io.renren.common.entity.BaseEntity;
import io.renren.modules.enterprise.ps08.dto.BPs08DTO;
import io.renren.modules.enterprise.ps08.entity.BPs08Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> Chris
 * @Date : 2021-10-11
 **/
@Mapper
public interface BPs08Dao extends BaseDao<BPs08Entity> {
    BPs08Entity selectContractA(@Param("tm0101") Long tm0101);

    BPs08Entity selectContractB(@Param("ps0201") Long ps0201);

    BPs08DTO getWorkerContract(@Param("ps0201") Long ps0201);


    /**
     * 批量查询人员是否签订在线合同
     * @param list
     * @return
     */
    Integer getPersonSign(List<String> list) ;
}
