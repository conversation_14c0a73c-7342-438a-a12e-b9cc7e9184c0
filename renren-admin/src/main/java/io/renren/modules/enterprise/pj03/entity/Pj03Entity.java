package io.renren.modules.enterprise.pj03.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目培训信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-21
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ03")
public class Pj03Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId(type = IdType.ASSIGN_ID)
	private Long pj0301;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 课程名称
     */
	private String trainingname;
    /**
     * 培训类型
     */
	private String trainingtypecode;
    /**
     * 培训方式
     */
	private String trainingmode;
    /**
     * 培训讲师
     */
	private String trainer;
    /**
     * 讲师证件号码
     */
	private String traineridcardnumber;
    /**
     * 培训机构
     */
	private String trainingorg;
    /**
     * 培训地址
     */
	private String trainingaddress;
    /**
     * 培训开始时间
     */
	private Date trainingstartdate;
    /**
     * 培训结束时间
     */
	private Date trainingenddate;
    /**
     * 培训时长
     */
	private BigDecimal trainingduration;
    /**
     * 培训简述
     */
	private String description;
    /**
     * 培训教材
     */
	private String textbook;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;

	/**
	 * 是否确认：0未确认 1已确认
	 */
	private String isConfirm;

	/**
	 * 合格分数
	 */
	private Integer passScore;
}