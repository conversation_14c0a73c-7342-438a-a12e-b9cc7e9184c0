package io.renren.modules.enterprise.ps02.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps08.dto.BPs08DTO;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 建筑工人合同信息上传
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "建筑工人合同信息上传")
public class Ps02FileUploadVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "ps0201")
    @NotBlank(message = "请选择一个工人进行合同信息上传")
    private String ps0201;

    @ApiModelProperty(value = "工人合同", required = true)
    @Valid
    private List<Ot01DTO> ot01DTOList;

}