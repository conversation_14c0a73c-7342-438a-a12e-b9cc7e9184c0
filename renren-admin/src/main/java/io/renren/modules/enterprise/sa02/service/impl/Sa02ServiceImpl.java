package io.renren.modules.enterprise.sa02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.GetLngAndLatUtil;
import io.renren.modules.enterprise.sa02.dao.Sa02Dao;
import io.renren.modules.enterprise.sa02.dto.Sa02DTO;
import io.renren.modules.enterprise.sa02.entity.Sa02Entity;
import io.renren.modules.enterprise.sa02.service.Sa02Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
*<AUTHOR>
*@date 2021/10/9
*/
@Service
public class Sa02ServiceImpl extends CrudServiceImpl<Sa02Dao, Sa02Entity, Sa02DTO> implements Sa02Service {

    @Autowired
    private Sa02Dao sa02Dao;

    @Override
    public PageData<Sa02DTO> pageSa02(Map<String, Object> params) {
        IPage<Sa02Entity> page = getPage(params,"",true);
        List<Sa02DTO> list = sa02Dao.page(params);
        PageData<Sa02DTO> sa02Page = getPageData(list,page.getTotal(),Sa02DTO.class);
        return sa02Page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(Sa02DTO sa02DTO) {
        Map<String, String> map = GetLngAndLatUtil.getLngAndLatByAddress(sa02DTO.getAddress());
        sa02DTO.setLat(map.get("lat"));
        sa02DTO.setLng(map.get("lng"));
        super.save(sa02DTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDto(Sa02DTO sa02DTO) {
        Sa02Entity bSa02Entity = baseDao.selectById(sa02DTO.getSa0201());
        if(!bSa02Entity.getAddress().equals(sa02DTO.getAddress())){
            Map<String, String> map = GetLngAndLatUtil.getLngAndLatByAddress(sa02DTO.getAddress());
            sa02DTO.setLat(map.get("lat"));
            sa02DTO.setLng(map.get("lng"));
        }
        super.update(sa02DTO);
    }

    @Override
    public QueryWrapper<Sa02Entity> getWrapper(Map<String, Object> params) {
        return null;
    }


}
