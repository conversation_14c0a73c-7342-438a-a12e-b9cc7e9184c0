package io.renren.modules.enterprise.pa05log.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工资单操作日志
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PA05_LOG")
public class Pa05LogEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	private Long id;
    /**
     * 工资单ID
     */
	private Long pa0501;
    /**
     * 操作行为
     */
	private String operation;
    /**
     * 操作人
     */
	private String operator;
    /**
     * 操作时间
     */
	private Date operatdate;
    /**
     * 操作意见
     */
	private String options;
}