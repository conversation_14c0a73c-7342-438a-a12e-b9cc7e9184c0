package io.renren.modules.enterprise.tm01audit.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 分包班组审核表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-03
 */
@Data
@ApiModel(value = "分包班组审核表")
public class Tm01AuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long tm0101;
    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "班组编号")
    private String teamsysno;
    @ApiModelProperty(value = "班组名称")
    private String teamname;
    @ApiModelProperty(value = "责任人姓名")
    private String responsiblepersonname;
    @ApiModelProperty(value = "责任人联系电话")
    private String responsiblepersonphone;
    @ApiModelProperty(value = "责任人证件类型")
    private String idcardtype;
    @ApiModelProperty(value = "责任人证件号码")
    private String responsiblepersonidnumber;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核人")
    private String auditor;
    @ApiModelProperty(value = "审核时间")
    private Date auditdate;
    @ApiModelProperty(value = "审核结果")
    private String auditresult;

}