package io.renren.modules.enterprise.pj01info.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-7-8 08:37:20
 * 工人列表
 */
@Data
@ApiModel(value = "建筑工人分页列表信息")
public class Ps02PageDTO {

    @ApiModelProperty(value = "主键ID")
    private Long ps0201;

    @ApiModelProperty(value = "所属项目")
    private String projectName;

    @ApiModelProperty(value = "所属企业")
    private String corpName;

    @ApiModelProperty(value = "所属班组")
    private String tm0101;

    @ApiModelProperty(value = "姓名")
    private String personName;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idCardNumber;

    @ApiModelProperty(value = "是否班组长")
    private String isTeamLeader;

    @ApiModelProperty(value = "手机号码")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String cellPhone;

    @ApiModelProperty(value = "工种")
    private String workTypeCode;

    @ApiModelProperty(value = "进退场状态")
    private String inOrout;

    @ApiModelProperty(value = "是否上传特殊工人文件")
    private String isUploadWorkerFile;
}
