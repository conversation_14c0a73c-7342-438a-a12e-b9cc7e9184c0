package io.renren.modules.enterprise.bankjoint.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.constant.Constant;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.bankjoint.dao.BankJointDao;
import io.renren.modules.enterprise.bankjoint.dto.BankJointDTO;
import io.renren.modules.enterprise.bankjoint.entity.BankJointEntity;
import io.renren.modules.enterprise.bankjoint.service.BankJointService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 银行对接情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
@Service
public class BankJointServiceImpl extends CrudServiceImpl<BankJointDao, BankJointEntity, BankJointDTO> implements BankJointService {

    @Override
    public QueryWrapper<BankJointEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<BankJointEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public List<BankJointDTO> pageList(Map<String, Object> params) {
        String areacode = SecurityUser.getDeptAreaCode();
        String lastcode = areacode.substring(4);
        if (Constant.CITY_LASECODE.equals(lastcode)) {
            params.put("areacode", areacode.substring(0, 4));
        } else {
            params.put("areacode", areacode);
        }
        List<BankJointDTO> list = baseDao.getList(params);
        return list;
    }

}