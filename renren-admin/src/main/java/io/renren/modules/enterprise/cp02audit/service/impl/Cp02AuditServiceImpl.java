package io.renren.modules.enterprise.cp02audit.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.cp02.dto.Cp02PageDTO;
import io.renren.modules.enterprise.cp02.entity.Cp02Entity;
import io.renren.modules.enterprise.cp02audit.dao.Cp02AuditDao;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDTO;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDetailDTO;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditPageDTO;
import io.renren.modules.enterprise.cp02audit.entity.Cp02AuditEntity;
import io.renren.modules.enterprise.cp02audit.service.Cp02AuditService;
import io.renren.modules.ot01.service.Ot01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 参建单位审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@Service
public class Cp02AuditServiceImpl extends CrudServiceImpl<Cp02AuditDao, Cp02AuditEntity, Cp02AuditDTO> implements Cp02AuditService {

    @Autowired
    private Ot01Service ot01Service;


    @Override
    public QueryWrapper<Cp02AuditEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Cp02AuditEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Cp02AuditPageDTO> getPageList(Map<String, Object> params) {
        paramsToLike(params, "corpname");
        // 分页
        IPage<Cp02AuditEntity> page = getPage(params, "", false);
        // 查询
        params.put("deptId", SecurityUser.getDeptId());
        List<Cp02AuditPageDTO> list = baseDao.getPageList(params);
        return getPageData(list, page.getTotal(), Cp02AuditPageDTO.class);
    }


    @Override
    public Cp02AuditDetailDTO getInfo(Long cp0201) {

        Cp02AuditDetailDTO info = baseDao.getInfo(cp0201);
        if (ObjectUtil.isNotNull(info)) {
            info.setOt01DTOList(ot01Service.loadBusinessData(cp0201,"06"));
        }
        return info;
    }
}