package io.renren.modules.enterprise.ps01.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps01.dto.PersonInfoDTO;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps01Dao extends BaseDao<Ps01Entity> {
    /**
     * 查询人员基础信息
     *
     * @param idCardNumber 身份证号码
     * @return Ps01DTO
     */
    default Ps01Entity loadPs01(String idCardNumber){
        return selectOne(new QueryWrapper<Ps01Entity>()
                .eq("idcardnumber", idCardNumber));
    }

    /**
     * 查询列表数据
     *
     * @param page   page
     * @param params params
     * @return
     */
    List<Ps01DTO> getList(Map<String, Object> params);

    /**
     * 获取人员信息
     * @param params idcardnumber
     * @return
     */
    PersonInfoDTO getPersonInfo(Map<String, Object> params) ;
}