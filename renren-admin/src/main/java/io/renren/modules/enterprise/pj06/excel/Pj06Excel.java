package io.renren.modules.enterprise.pj06.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Data
public class Pj06Excel {
    @Excel(name = "主键id")
    private BigDecimal pj0601;
    @Excel(name = "项目名称")
    private String name;
    @Excel(name = "所属行业")
    private String industry;
    @Excel(name = "项目所在地")
    private String areacode;
    @Excel(name = "联系人姓名")
    private String linkman;
    @Excel(name = "联系人电话")
    private String linkphone;
    @Excel(name = "建设单位名称")
    private String constructionname;
    @Excel(name = "建设单位统一社会信用代码")
    private String constructionnumber;
    @Excel(name = "施工总承包单位名称")
    private String contractname;
    @Excel(name = "施工总承包单位统一社会信用代码")
    private String contractnumber;
    @Excel(name = "备注")
    private String memo;
    @Excel(name = "审核状态(0待审核，1通过，2不通过)")
    private String status;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "审核时间")
    private Date updateDate;

}