package io.renren.modules.enterprise.pj06.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj06.entity.Pj06Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Mapper
public interface Pj06Dao extends BaseDao<Pj06Entity> {
    /**
     * 查询未审核的项目
     *
     * @param name     项目名称
     * @param areaCode 行政区划
     * @return Long
     */
    Long getProjectNumber(@Param("name") String name);

    /**
     * 查询PJ01是否存在项目
     *
     * @param name     项目名称
     * @return Long
     */
    Long getPj01Number(@Param("name") String name);

    /**
     * 查询当前区域下边账号的最大值
     * @param areaCode 行政区划
     * @return
     */
    Long selectUserName();
}