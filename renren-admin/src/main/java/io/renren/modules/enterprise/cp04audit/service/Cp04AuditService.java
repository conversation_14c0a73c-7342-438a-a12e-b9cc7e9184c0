package io.renren.modules.enterprise.cp04audit.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.cp04.dto.Cp04DetailDTO;
import io.renren.modules.enterprise.cp04audit.dto.Cp04AuditDTO;
import io.renren.modules.enterprise.cp04audit.dto.Cp04AuditDetailDTO;
import io.renren.modules.enterprise.cp04audit.dto.Cp04AuditPageDTO;
import io.renren.modules.enterprise.cp04audit.entity.Cp04AuditEntity;

import java.util.Map;

/**
 * 分包信息审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
public interface Cp04AuditService extends CrudService<Cp04AuditEntity, Cp04AuditDTO> {


    /**
     * 分页
     * @param params
     * @return
     */
    PageData<Cp04AuditPageDTO> getPageList(Map<String, Object> params);


    /**
     * 详情
     * @return
     */
    Cp04AuditDetailDTO getInfo(Long cp0401) ;
}