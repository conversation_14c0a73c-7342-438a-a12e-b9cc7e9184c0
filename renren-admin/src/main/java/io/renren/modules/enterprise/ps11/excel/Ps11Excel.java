package io.renren.modules.enterprise.ps11.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员/工人预约进场
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-25
 */
@Data
public class Ps11Excel {
    @Excel(name = "id")
    private BigDecimal ps1101;
    @Excel(name = "项目Id")
    private BigDecimal pj0101;
    @Excel(name = "基础人员信息")
    private BigDecimal ps0101;
    @Excel(name = "班组（人员类型为工人才写入）")
    private BigDecimal tm0101;
    @Excel(name = "参见单位（人员类型为管理人员才写入）")
    private BigDecimal cp0201;
    @Excel(name = "")
    private String worktypecode;
    @Excel(name = "1工人 2管理人员")
    private Short persontype;
    @Excel(name = "进场时间")
    private Date entrydate;
    @Excel(name = "")
    private Date createDate;
    @Excel(name = "0未处理1已处理")
    private Short status;
    @Excel(name = "岗位类型（类型为管理人员才写入）")
    private String jobtype;

}