package io.renren.modules.enterprise.ps04.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps04.dto.DispatchRecordDTO;
import io.renren.modules.enterprise.ps04.dto.Ps04DTO;
import io.renren.modules.enterprise.ps04.dto.Ps04PageDTO;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.enterprise.ps04.vo.Ps04SaveVO;

import java.util.Map;

/**
 * 管理人员参建信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
public interface Ps04Service extends CrudService<Ps04Entity, Ps04DTO> {


    /**
     * 分页查询数据
     *
     * @param params
     * @return
     */
    PageData<Ps04PageDTO> ps04Page(Map<String, Object> params);


    /**
     * 管理人员派遣记录
     * @param params
     * @return
     */
    PageData<DispatchRecordDTO> dispatchRecords(Map<String, Object> params);


    /**
     * 添加管理人员
     * @param ps04SaveVO
     * @return
     */
    Result savePs04(Ps04SaveVO ps04SaveVO) ;

    Ps04DTO getInfo(Long id);

    /**
     * 退场
     * @param ids
     */
    void exit(Long[] ids);

    Result updateInfo(Ps04DTO dto);

    /**
     * 管理人员下发
     * @param ids
     */
    void deviceAddPerson(Long[] ids);

    /**
     * 人员进场
     * @param ids
     */
    void entry(Long[] ids);

    /**
     * 管理人员基础信息
     * @param params
     * @return
     */
    Result info(Map<String, Object> params);

    /**
     * 管理人员到岗详情
     * @param params
     * @return
     */
    Result attendanceTJ(Map<String, Object> params);
}