package io.renren.modules.enterprise.cp02audit.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditDetailDTO;
import io.renren.modules.enterprise.cp02audit.dto.Cp02AuditPageDTO;
import io.renren.modules.enterprise.cp02audit.entity.Cp02AuditEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 参建单位审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@Mapper
public interface Cp02AuditDao extends BaseDao<Cp02AuditEntity> {


    /**
     * 分页
     * @param params
     * @return
     */
    List<Cp02AuditPageDTO> getPageList(Map<String, Object> params);


    /**
     * 详情
     * @param cp0201
     * @return
     */
    Cp02AuditDetailDTO getInfo(Long cp0201) ;
}