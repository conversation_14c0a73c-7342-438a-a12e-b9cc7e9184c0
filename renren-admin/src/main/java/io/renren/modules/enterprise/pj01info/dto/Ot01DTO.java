package io.renren.modules.enterprise.pj01info.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 附件数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-05-21
 */
@Data
@ApiModel(value = "附件数据表")
public class Ot01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "附件ID不能为空")
    private Long ot0101;

    @ApiModelProperty(value = "业务类型", required = true)
    private String busitype;

    @ApiModelProperty(value = "业务编号")
    private String busisysno;

    @ApiModelProperty(value = "附件名称")
    private String name;

    @ApiModelProperty(value = "附件路径")
    private String url;

    @ApiModelProperty(value = "附件类型")
    private String viewType;

    @ApiModelProperty(value = "附件原始名称")
    private String originalName;


}