package io.renren.modules.enterprise.bankjoint.controller;

import io.renren.common.utils.Result;
import io.renren.modules.enterprise.bankjoint.dto.BankJointDTO;
import io.renren.modules.enterprise.bankjoint.service.BankJointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;


/**
 * 银行对接情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-14
 */
@RestController
@RequestMapping("regul/bankjoint")
@Api(tags = "银行对接情况")
public class BankJointController {
    @Autowired
    private BankJointService bankJointService;

    @GetMapping("page")
    @ApiOperation("银行对接统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "payBankCode", value = "银行名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "bankjointstatus", value = "对接状态", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("regul:bankjoint:page")
    public Result<List<BankJointDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        List<BankJointDTO> list = bankJointService.pageList(params);
        return new Result<List<BankJointDTO>>().ok(list);
    }

}