package io.renren.modules.enterprise.ps01.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps01.dao.Ps01Dao;
import io.renren.modules.enterprise.ps01.dto.PersonInfoDTO;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Ps01ServiceImpl extends CrudServiceImpl<Ps01Dao, Ps01Entity, Ps01DTO> implements Ps01Service {

    @Autowired
    private Ps01Dao ps01Dao;

    /*@Override
    public PageData<Ps01DTO> pageList(Map<String, Object> params) {
        String name = (String) params.get("name");
        String idcardnumber = (String) params.get("idcardnumber");
        IPage<Ps01Entity> page = getPage(params, "", false);
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        params.put("pj0101", pj0101);
        List<Ps01DTO> list = new ArrayList<>();
        if (StringUtils.isNotBlank(name) || StringUtils.isNotBlank(idcardnumber)) {
            list = ps01Dao.getList(params);
        }
        return getPageData(list, page.getTotal(), Ps01DTO.class);
    }*/

    @Override
    public Ps01DTO getPs01Info(Long id) {
        Ps01Entity ps01Entity = ps01Dao.selectById(id);
        return ConvertUtils.sourceToTarget(ps01Entity, Ps01DTO.class);
    }

    @Override
    public void savePs01Info(Ps01DTO dto) {
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto, Ps01Entity.class);
        ps01Dao.insert(ps01Entity);
    }

    @Override
    public void updatePs01Info(Ps01DTO dto) {
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto, Ps01Entity.class);
        ps01Dao.updateById(ps01Entity);
    }

    @Override
    public PageData<Ps01DTO> getManageList(Map<String, Object> params) {
        return null;
    }

    @Override
    public Result<PersonInfoDTO> getPersonInfo(Map<String, Object> params) {

        PersonInfoDTO personInfo = baseDao.getPersonInfo(params);

        return ObjectUtil.isNotNull(personInfo) ?
                new Result<PersonInfoDTO>().ok(personInfo):new Result<PersonInfoDTO>().error("人员库中未找到此人员信息,请手动添加");
    }

    @Override
    public QueryWrapper<Ps01Entity> getWrapper(Map<String, Object> params) {
        return null;
    }
}