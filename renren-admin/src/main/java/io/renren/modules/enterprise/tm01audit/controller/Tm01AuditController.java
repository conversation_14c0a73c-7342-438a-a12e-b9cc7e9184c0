package io.renren.modules.enterprise.tm01audit.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.tm01audit.dto.Tm01AuditDTO;
import io.renren.modules.enterprise.tm01audit.dto.Tm01DetailAuditDTO;
import io.renren.modules.enterprise.tm01audit.dto.Tm01PageAuditDTO;
import io.renren.modules.enterprise.tm01audit.service.Tm01AuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 分包班组审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@RestController
@RequestMapping("enterprise/tm01audit")
@Api(tags="分包班组审核表")
public class Tm01AuditController {
    @Autowired
    private Tm01AuditService tm01AuditService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String"),
        @ApiImplicitParam(name = "teamname", value = "班组名称", paramType = "query", dataType="String"),
        @ApiImplicitParam(name = "auditstatus", value = "审核状态(0待审核，1通过，2不通过)", paramType = "query", dataType="String"),
    })
    @RequiresPermissions("enterprise:tm01audit:page")
    public Result<PageData<Tm01PageAuditDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Tm01PageAuditDTO> page = tm01AuditService.getPageList(params);

        return new Result<PageData<Tm01PageAuditDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:tm01audit:info")
    public Result<Tm01DetailAuditDTO> get(@PathVariable("id") Long id){
        Tm01DetailAuditDTO data = tm01AuditService.getInfo(id);

        return new Result<Tm01DetailAuditDTO>().ok(data);
    }

}