package io.renren.modules.enterprise.ps03.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员在职信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS03")
public class Ps03Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId(type = IdType.ASSIGN_ID)
	private Long ps0301;
    /**
     * 当前项目ID
     */
	private Long pj0101;
    /**
     * 在职企业ID
     */
	private Long cp0101;
    /**
     * 人员ID
     */
	private Long ps0101;
    /**
     * 在职状态
     */
	private String managestatus;
    /**
     * 头像采集照片
     */
	private String photo;
    /**
     * 是否购买工伤或意外伤害保险
     */
	private String hasbuyinsurance;
    /**
     * 入职时间
     */
	private Date inductiontime;
    /**
     * 离职时间
     */
	private Date departuretime;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}