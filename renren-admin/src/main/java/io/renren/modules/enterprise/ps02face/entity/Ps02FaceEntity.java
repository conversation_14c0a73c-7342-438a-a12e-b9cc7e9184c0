package io.renren.modules.enterprise.ps02face.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 工人人脸库信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS02_FACE")
public class Ps02FaceEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 人员id
     */
    private Long ps0201;
    /**
     * 项目id
     */
    private Long pj0101;
    /**
     * 人脸特征
     */
    private byte[] faceFeature;
    /**
     * 项目所在地
     */
    private String areacode;
    /**
     * 进退场
     */
    private String inOrOut;
    /**
     * 工人头像（暂适用于采集数据）
     */
    private String issuecardpicurl;
    /**
     * 是否采集（0，未采集。1，已采集）
     */
    private String iscollect;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}