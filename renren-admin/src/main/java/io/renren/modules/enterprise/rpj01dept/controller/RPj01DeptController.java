package io.renren.modules.enterprise.rpj01dept.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.rpj01dept.dto.InsertRpdDTO;
import io.renren.modules.enterprise.rpj01dept.dto.Pj0101NameDTO;
import io.renren.modules.enterprise.rpj01dept.service.RPj01DeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;


/**
 * 项目和机构的关系表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-11
 */
@RestController
@RequestMapping("enterprise/rpj01dept")
@Api(tags="项目和机构的关系表")
public class RPj01DeptController {
    @Autowired
    private RPj01DeptService rPj01DeptService;


    @GetMapping("pj0101List")
    @ApiOperation("获取所有项目")
    @RequiresPermissions("enterprise:rpj01dept:page")
    public Result<List<Pj0101NameDTO>> getPj0101List(){
        List<Pj0101NameDTO> data = rPj01DeptService.getPj0101List();

        return new Result<List<Pj0101NameDTO>>().ok(data);
    }


  @GetMapping("getOwnPj0101List/{deptId}")
    @ApiOperation("获取当前机构下的所有项目")
    @RequiresPermissions("enterprise:rpj01dept:page")
    public Result<List<String>> getOwnPj0101List(@PathVariable("deptId")Long deptId){
        List<String> data = rPj01DeptService.getOwnPj0101List(deptId);
        return new Result<List<String>>().ok(data);
    }


    @PostMapping("insert")
    @ApiOperation("添加项目机构关系")
    @LogOperation("添加项目机构关系")
    @RequiresPermissions("enterprise:rpj01dept:page")
    public Result save(@RequestBody InsertRpdDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);
        return rPj01DeptService.insertByInsertRpdDTO(dto);
    }



}