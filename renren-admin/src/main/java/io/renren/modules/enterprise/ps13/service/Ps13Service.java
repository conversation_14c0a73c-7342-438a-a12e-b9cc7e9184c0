package io.renren.modules.enterprise.ps13.service;

import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps13.dto.Ps13DTO;
import io.renren.modules.enterprise.ps13.entity.Ps13Entity;

import java.util.Map;

/**
 * 关键岗位配置表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-24
 */
public interface Ps13Service extends CrudService<Ps13Entity, Ps13DTO> {
    // 判断岗位是否为关键岗位
    Result isKeyJob(Map<String, Object> params);
}