package io.renren.modules.enterprise.tjattendanceinfo.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.common.controller.Pj01ExportInfoHandler;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.enterprise.tjattendanceinfo.annotation.VirtualProcessor;
import io.renren.modules.enterprise.tjattendanceinfo.dao.TjAttendanceInfoDao;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceGenerateDataDTO;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoDTO;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoPageExportDTO;
import io.renren.modules.enterprise.tjattendanceinfo.entity.TjAttendanceInfoEntity;
import io.renren.modules.enterprise.tjattendanceinfo.service.TjAttendanceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;

/**
 * 考勤统计表详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-05-14
 */
@Slf4j
@Service
public class TjAttendanceInfoServiceImpl extends CrudServiceImpl<TjAttendanceInfoDao, TjAttendanceInfoEntity, TjAttendanceInfoDTO> implements TjAttendanceInfoService {

    @Autowired
    private Pj01ExportInfoHandler pj01ExportInfoHandler;

    @Override
    public QueryWrapper<TjAttendanceInfoEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<TjAttendanceInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    /**
     * 导出考勤表明细数据
     *
     * @param params
     * @param response
     */
    @Override
    public void exportTjAttendanceInfo(Map<String, Object> params, HttpServletResponse response) {
        String tjMonth = checkMonthGetMonth(params);
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        params.put("pj0101", pj0101);
        List<TjAttendanceInfoPageExportDTO> tjAttendanceInfoPageExportDTOS = baseDao.getTjAttendanceInfoList(params);
        if (CollUtil.isEmpty(tjAttendanceInfoPageExportDTOS)) {
            throw new RenException("未查询到相关数据");
        }
        VirtualProcessor.dealBeanList(tjAttendanceInfoPageExportDTOS);
        ExportParams exportParams = new ExportParams("考勤表明细-统计情况（" + tjMonth + "）", tjMonth + "考勤表明细");
        //此处格式对应下文文件名后缀xlsx
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TjAttendanceInfoPageExportDTO.class, tjAttendanceInfoPageExportDTOS);
        pj01ExportInfoHandler.downLoadExcel(tjMonth + "考勤表明细-统计情况" + ".xlsx", response, workbook);
    }

    /**
     * 生成考勤表统计数据
     *
     * @param dto
     */
    @Override
    public void generateTjAttendanceData(TjAttendanceGenerateDataDTO dto) {

        Map<Object, Object> mapTjAttendance = MapUtil.newHashMap();
        mapTjAttendance.put("Cpj0101", CommonUtils.userProjectInfo().getPj0101());
        mapTjAttendance.put("Ckqmonth", dto.getTjMonth());
        baseDao.generateTjAttendanceData(mapTjAttendance);
    }

    /**
     * 分页
     *
     * @param params
     * @return
     */
    @Override
    public PageData<TjAttendanceInfoPageExportDTO> pageList(Map<String, Object> params) {
        checkMonthGetMonth(params);
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        IPage<TjAttendanceInfoEntity> page = getPage(params, "", false);
        List<TjAttendanceInfoPageExportDTO> tjAttendanceInfoPageExportDTOS = baseDao.getTjAttendanceInfoList(params);
        PageData<TjAttendanceInfoPageExportDTO> pageData = getPageData(tjAttendanceInfoPageExportDTOS, page.getTotal(), TjAttendanceInfoPageExportDTO.class);
        return pageData;
    }

    @Override
    public boolean existTjAttendanceData(Map<String, Object> params) {
        List<TjAttendanceInfoPageExportDTO> list = baseDao.getTjAttendanceInfoList(params);
        return CollUtil.isEmpty(list);
    }

    @Override
    public Integer getKqdaysByIdcardnumber(Map<String, Object> params) {
        Integer kqdays = baseDao.getKqdaysByIdcardnumber(params);
        return kqdays == null ? 0 : kqdays;
    }

    @Override
    public void deleteTjAttendanceData(Map<String, Object> params) {
        baseDao.deleteTjAttendanceData(params);
    }

    /**
     * 检查当前月份是否符合要求。不能查询当前月份以后的数据
     *
     * @return
     */
    public static String checkMonthGetMonth(Map<String, Object> params) {
        String tjMonth = getTjAttendanceInfoMonth(params);
        // 正则表达式检查格式为 yyyy-MM
        String regex = "^\\d{4}-\\d{2}$";
        if (!tjMonth.matches(regex)) {
            throw new RenException("传入查询数据时间格式异常");
        }
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        try {
            // 将 thMonth 解析为 YearMonth 对象
            YearMonth inputMonth = YearMonth.parse(tjMonth, formatter);
            // 获取当前的 YearMonth 对象
            YearMonth currentMonth = YearMonth.now();
            // 比较两个 YearMonth 对象
            if (inputMonth.isAfter(currentMonth)) {
                throw new RenException("只能查询当前月份以前的数据");
            }
        } catch (DateTimeParseException e) {
            // 如果解析失败，抛出异常
            log.error("checkMonthGetMonth DateTimeParseException e", e);
            throw new RenException("查询数据失败，请联系管理员");
        }
        return tjMonth;
    }

    /**
     * 获取考勤统计时间
     *
     * @param params
     * @return
     */
//    @NotNull
    private static String getTjAttendanceInfoMonth(Map<String, Object> params) {
        String tjMonth = MapUtil.getStr(params, "tjMonth");
        if (StrUtil.isEmpty(tjMonth)) {
            throw new RenException("导出考勤月份不能为空");
        }
        return tjMonth;
    }
}