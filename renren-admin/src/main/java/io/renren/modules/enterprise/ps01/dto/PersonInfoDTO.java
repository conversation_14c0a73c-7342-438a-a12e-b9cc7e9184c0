package io.renren.modules.enterprise.ps01.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "人员实名基础信息")
public class PersonInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0101;

    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    @ApiModelProperty(value = "证件类型_Select选择器", required = true)
    private String idcardtype;

    @ApiModelProperty(value = "证件号码", required = true)
    private String idcardnumber;

    @ApiModelProperty(value = "性别_Select选择器", required = true)
    private String gender;

    @ApiModelProperty(value = "民族_Select选择器", required = true)
    private String nation;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "住址", required = true)
    private String address;

    @ApiModelProperty(value = "学历_Select选择器")
    private String edulevel;

    @ApiModelProperty(value = "学位_Select选择器")
    private String degree;

    @ApiModelProperty(value = "身份证头像")
    private String headimageurl;

    @ApiModelProperty(value = "采集头像")
    private String photo;

    @ApiModelProperty(value = "政治面貌", required = true)
    private String politicstype;

    @ApiModelProperty(value = "人员类别", required = true)
    private String workertype;

    @ApiModelProperty(value = "是否加入工会_Select选择器")
    private String isjoined;

    @ApiModelProperty(value = "加入工会时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date joinedtime;

    @ApiModelProperty(value = "手机号码", required = true)
    private String cellphone;

    @ApiModelProperty(value = "文化程度_Select选择器")
    private String cultureleveltype;

    @ApiModelProperty(value = "特长")
    private String specialty;

    @ApiModelProperty(value = "是否有重大病史_Select选择器")
    private String hasbadmedicalhistory;

    @ApiModelProperty(value = "紧急联系人姓名")
    private String urgentlinkman;

    @ApiModelProperty(value = "紧急联系电话")
    private String urgentlinkmanphone;

    @ApiModelProperty(value = "开始工作日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workdate;

    @ApiModelProperty(value = "婚姻状况")
    private String maritalstatus;

    @ApiModelProperty(value = "发证机关")
    private String grantorg;

    @ApiModelProperty(value = "正面照 URL")
    private String positiveidcardimageurl;

    @ApiModelProperty(value = "反面照 URL")
    private String negativeidcardimageurl;

    @ApiModelProperty(value = "有效期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "有效期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expirydate;
}