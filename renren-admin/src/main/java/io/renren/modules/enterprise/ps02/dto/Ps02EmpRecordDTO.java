package io.renren.modules.enterprise.ps02.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-09-06 14:11
 */
@Data
@ApiModel(value = "工人用工记录")
public class Ps02EmpRecordDTO implements Serializable {
    private static final long serialVersionUID = 3276055274768956139L;

    @ApiModelProperty(value = "人员ID")
    private Long ps0101;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "证件号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idCardNumber;

    @ApiModelProperty(value = "所属项目")
    private String projectName;

    @ApiModelProperty(value = "所属班组")
    private String teamName;

    @ApiModelProperty(value = "工种")
    private String workTypeCode;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryTime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exitTime;

    @ApiModelProperty(value = "退场状态")
    private String inOrOut;
}
