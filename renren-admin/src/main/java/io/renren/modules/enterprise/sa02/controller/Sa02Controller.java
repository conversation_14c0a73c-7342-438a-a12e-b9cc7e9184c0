package io.renren.modules.enterprise.sa02.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.sa02.dto.Sa02DTO;
import io.renren.modules.enterprise.sa02.service.Sa02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 网点信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-09
 */
@RestController
@RequestMapping("enterprise/sa02")
@Api(tags = "网点信息")
public class Sa02Controller {

    @Autowired
    private Sa02Service sa02Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    //@RequiresPermissions("enterprise:sa02:page")
    public Result<PageData<Sa02DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Sa02DTO> page = sa02Service.pageSa02(params);
        return new Result<PageData<Sa02DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("详细信息")
    //@RequiresPermissions("enterprise:sa02:info")
    public Result<Sa02DTO> get(@PathVariable("id") Long id){
        Sa02DTO data = sa02Service.get(id);

        return new Result<Sa02DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("新增网点")
    @LogOperation("新增网点")
    //@RequiresPermissions("enterprise:sa02:save")
    public Result save(@RequestBody Sa02DTO sa02DTO) {
        ValidatorUtils.validateEntity(sa02DTO);
        sa02Service.add(sa02DTO);
        return new Result();
    }

    @PutMapping
    @ApiOperation("修改网点信息")
    @LogOperation("修改网点信息")
    //@RequiresPermissions("enterprise:sa02:update")
    public Result update(@RequestBody Sa02DTO sa02DTO) {
        ValidatorUtils.validateEntity(sa02DTO);
        sa02Service.updateDto(sa02DTO);
        return new Result();
    }

}
