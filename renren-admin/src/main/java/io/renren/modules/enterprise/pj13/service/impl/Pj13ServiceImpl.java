package io.renren.modules.enterprise.pj13.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.enterprise.pj13.dao.Pj13Dao;
import io.renren.modules.enterprise.pj13.dto.Pj13DTO;
import io.renren.modules.enterprise.pj13.dto.Ps02ExitPageDTO;
import io.renren.modules.enterprise.pj13.dto.Ps04ExitPageDTO;
import io.renren.modules.enterprise.pj13.dto.Tm01ExitPageDTO;
import io.renren.modules.enterprise.pj13.entity.Pj13Entity;
import io.renren.modules.enterprise.pj13.service.Pj13Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@Service
public class Pj13ServiceImpl extends CrudServiceImpl<Pj13Dao, Pj13Entity, Pj13DTO> implements Pj13Service {

    @Override
    public QueryWrapper<Pj13Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pj13Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ps02ExitPageDTO> ps02ExitPage(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Page<Ps02ExitPageDTO> page = new Page<>(curPage, limit);
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        List<Ps02ExitPageDTO> list = baseDao.ps02ExitPage(page, params);
        return getPageData(list, page.getTotal(), Ps02ExitPageDTO.class);
    }

    @Override
    public PageData<Ps04ExitPageDTO> ps04ExitPage(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Page<Ps04ExitPageDTO> page = new Page<>(curPage, limit);
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        List<Ps04ExitPageDTO> list = baseDao.ps04ExitPage(page, params);
        return getPageData(list, page.getTotal(), Ps04ExitPageDTO.class);
    }

    @Override
    public PageData<Tm01ExitPageDTO> tm01ExitPage(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Page<Tm01ExitPageDTO> page = new Page<>(curPage, limit);
        params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
        List<Tm01ExitPageDTO> list = baseDao.tm01ExitPage(page, params);
        return getPageData(list, page.getTotal(), Tm01ExitPageDTO.class);
    }
}