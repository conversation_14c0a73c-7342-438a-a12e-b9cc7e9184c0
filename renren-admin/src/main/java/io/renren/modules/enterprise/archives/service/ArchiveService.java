package io.renren.modules.enterprise.archives.service;

import io.renren.common.page.PageData;
import io.renren.common.service.BaseService;
import io.renren.modules.enterprise.archives.dto.ArchiveInfo;
import io.renren.modules.enterprise.archives.dto.PartUnitPage;
import io.renren.modules.enterprise.archives.dto.PersonPage;
import io.renren.modules.enterprise.archives.dto.TeamPage;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ArchiveService extends BaseService<ArchiveInfo> {
    /**
     * 人员档案分页
     *
     * @param params Map<String, Object>
     * @return PageData<PersonPage>
     */
    PageData<PersonPage> personPageList(Map<String, Object> params);

    /**
     * 参建单位档案分页
     *
     * @param params Map<String, Object>
     * @return PageData<PartUnitPage>
     */
    PageData<PartUnitPage> partUnitPageList(Map<String, Object> params);

    /**
     * 班组档案分页
     *
     * @param params Map<String, Object>
     * @return PageData<TeamPage>
     */
    PageData<TeamPage> teamPageList(Map<String, Object> params);
}
