package io.renren.modules.enterprise.pj05.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训考题表
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-02-21
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ05")
public class Pj05Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId(type = IdType.ASSIGN_ID)
	private Long pj0501;
    /**
     * 培训ID
     */
	private Long pj0301;
    /**
     * 序号
     */
	private String sno;
    /**
     * 试题标题
     */
	private String title;
    /**
     * 选项A
     */
	private String optiona;
    /**
     * 选项B
     */
	private String optionb;
    /**
     * 选项C
     */
	private String optionc;
    /**
     * 选项D
     */
	private String optiond;
    /**
     * 正确答案
     */
	private String answer;
    /**
     * 分值
     */
	private Short score;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}