package io.renren.modules.enterprise.pa05log.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 工资单操作日志
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "工资单操作日志")
public class Pa05LogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "工资单ID")
    private Long pa0501;
    @ApiModelProperty(value = "操作行为")
    private String operation;
    @ApiModelProperty(value = "操作人")
    private String operator;
    @ApiModelProperty(value = "操作时间")
    private Date operatdate;
    @ApiModelProperty(value = "操作意见")
    private String options;

}