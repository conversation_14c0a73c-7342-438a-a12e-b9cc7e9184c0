package io.renren.modules.enterprise.cg09.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.TimePeriodNode;
import io.renren.common.utils.TimePeriodTreeUtils;
import io.renren.common.utils.TreeUtils;
import io.renren.modules.enterprise.cg09.dao.Cg09Dao;
import io.renren.modules.enterprise.cg09.dto.Cg09DTO;
import io.renren.modules.enterprise.cg09.entity.Cg09Entity;
import io.renren.modules.enterprise.cg09.service.Cg09Service;
import io.renren.modules.enterprise.cg10.dto.FileCountInfo;
import io.renren.modules.enterprise.pj01.dto.Pj01DTO;
import io.renren.modules.enterprise.pj01.service.Pj01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 检查清单档案配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-09
 */
@Service
public class Cg09ServiceImpl extends CrudServiceImpl<Cg09Dao, Cg09Entity, Cg09DTO> implements Cg09Service {

    @Resource
    private Pj01Service pj01Service;

    private static final String MONTH_TYPE = "1";

    private static final String FILE_TYPE = "702";


    @Override
    public QueryWrapper<Cg09Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Cg09Entity> wrapper = new QueryWrapper<>();

        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public List<Cg09DTO> getFileTree() {
        Pj01DTO projectInfo = pj01Service.getProjectInfo(CommonUtils.userProjectInfo().getPj0101());
        Date startTime = ObjectUtil.isNull(projectInfo.getStartdate()) ? new Date() : projectInfo.getStartdate();
//        Date endTime = DateTime.now().isBefore(projectInfo.getCompleteDate()) ? new Date() : projectInfo.getCompleteDate();
        Date endTime = (projectInfo.getCompleteDate() != null && DateTime.now().isBefore(new DateTime(projectInfo.getCompleteDate())))
                ? new Date()
                : (projectInfo.getCompleteDate() != null ? projectInfo.getCompleteDate() : new Date());
        List<Cg09DTO> list = baseDao.getFileTreeList(projectInfo.getPj0101());
        // 为 type 值为 1 的数据添加时间段树
        addTimePeriodTreeToTypeOne(list, startTime, endTime);

        // 构建树结构
        List<Cg09DTO> treeList = TreeUtils.build(list);

        // 计算并设置每个节点的附件数量
        calculateFileCountForTree(treeList, projectInfo.getPj0101());

        return treeList;
    }

    /**
     * 为 type 值为 1 的数据添加时间段树
     *
     * @param list 原始数据列表
     */
    private void addTimePeriodTreeToTypeOne(List<Cg09DTO> list, Date startTime, Date endTime) {
        if (list == null || list.isEmpty()) {
            return;
        }
        // 在扁平列表中找到所有 type 为 1 的节点
        List<Cg09DTO> typeOneNodes = new ArrayList<>();
        for (Cg09DTO dto : list) {
            if (MONTH_TYPE.equals(dto.getType())) {
                typeOneNodes.add(dto);
            }
        }
        // 为每个 type为1的节点添加时间段树
        for (Cg09DTO typeOneNode : typeOneNodes) {
            // 生成时间段树（使用传入的开始和结束时间）
            List<TimePeriodNode> timePeriodTree = TimePeriodTreeUtils.buildTimePeriodTree(
                    startTime, endTime);
            // 将 TimePeriodNode 转换为Cg09DTO并添加到原始列表中
            List<Cg09DTO> timePeriod = convertTimePeriodNodesToCg09Dto(timePeriodTree, typeOneNode.getId(), typeOneNode.getTreeLevel(), typeOneNode.getCg0901());
            list.addAll(timePeriod);
        }
    }

    /**
     * 将 TimePeriodNode 转换为 Cg09DTO
     *
     * @param timePeriodNodes 时间段节点列表
     * @param parentId        父节点ID
     * @param parentTreeLevel 父节点的treeLevel
     * @param archiveId       档案ID
     * @return 转换后的 Cg09DTO 列表
     */
    private List<Cg09DTO> convertTimePeriodNodesToCg09Dto(List<TimePeriodNode> timePeriodNodes, Long parentId, Integer parentTreeLevel, Long archiveId) {
        List<Cg09DTO> result = new ArrayList<>();
        // 递归处理所有节点
        for (TimePeriodNode node : timePeriodNodes) {
            Cg09DTO dto = new Cg09DTO();

            // 生成稳定的确定性ID，确保相同输入总是产生相同ID
            Long nodeId = generateStableNodeId(parentId, node);

            // 设置基本属性
            dto.setId(nodeId);
            dto.setCg0901(nodeId);
            dto.setFileName(node.getDisplayName());

            // 根据节点类型设置不同的属性
            if (node.getNodeType() == TimePeriodNode.NodeType.YEAR) {
                dto.setPid(parentId);
                dto.setType("year");
                dto.setTreeLevel(parentTreeLevel != null ? parentTreeLevel + 1 : 4);
                // 年份倒序排列：使用负数，年份越大，sort值越小
                dto.setSort(-node.getYear());
                // 保存档案ID和年份信息，用于后续计算附件数量
                dto.setFileNumber(archiveId + "_" + node.getYear()); // 临时存储档案ID和年份
            } else if (node.getNodeType() == TimePeriodNode.NodeType.MONTH) {
                dto.setPid(parentId);
                dto.setType("month");
                // 月份节点的TreeLevel应该比其父节点（年份节点）的TreeLevel多1
                dto.setTreeLevel(parentTreeLevel != null ? parentTreeLevel + 1 : 5);
                // 月份倒序排列：使用负数，月份越大，sort值越小
                dto.setSort(-node.getMonth());
                // 保存档案ID、年份和月份信息，用于后续计算附件数量
                dto.setFileNumber(archiveId + "_" + node.getYear() + "_" + node.getMonth()); // 临时存储档案ID、年份和月份
            }

            result.add(dto);

            // 递归处理子节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                List<Cg09DTO> childDto = convertTimePeriodNodesToCg09Dto(node.getChildren(), nodeId, dto.getTreeLevel(), archiveId);
                result.addAll(childDto);
            }
        }

        return result;
    }

    /**
     * 生成稳定的确定性节点ID
     * 使用哈希算法确保相同的输入总是产生相同的ID，避免Long溢出问题
     *
     * @param parentId 父节点ID
     * @param node     时间段节点
     * @return 稳定的节点ID
     */
    private Long generateStableNodeId(Long parentId, TimePeriodNode node) {
        // 构建唯一标识字符串
        String uniqueKey;
        if (node.getNodeType() == TimePeriodNode.NodeType.YEAR) {
            // 年份节点：parentId + "_YEAR_" + year
            uniqueKey = parentId + "_YEAR_" + node.getYear();
        } else if (node.getNodeType() == TimePeriodNode.NodeType.MONTH) {
            // 月份节点：parentId + "_MONTH_" + year + "_" + month
            uniqueKey = parentId + "_MONTH_" + node.getYear() + "_" + node.getMonth();
        } else {
            // 如果节点类型未知，使用随机ID生成方式
            return IdUtil.getSnowflakeNextId();
        }

        // 使用字符串的hashCode生成稳定的ID
        // 为了避免负数和冲突，使用绝对值并确保在Long范围内
        long hash = Math.abs((long) uniqueKey.hashCode());

        // 为了进一步减少冲突概率，可以在hash基础上加上时间戳的低位
        // 但这里我们追求完全确定性，所以只使用hash

        // 确保ID为正数且在合理范围内（避免与现有业务ID冲突）
        // 使用一个较大的基数来区分时间段节点和普通业务节点
        return 9000000000000000L + (hash % 1000000000000000L);
    }

    @Override
    public List<Cg09DTO> getAllArchiveList(Map<String, Object> params) {
        params.put("fileType", FILE_TYPE);
        return baseDao.getAllArchiveList(params);
    }

    @Override
    public Cg09DTO getArchConfigInfo(Map<String, Object> params) {
        params.put("fileType", FILE_TYPE);
        return baseDao.selectConfigInfo(params);
    }

    /**
     * 计算并设置树中每个节点的附件数量
     * 实现父节点数量等于子节点数量之和的逻辑
     *
     * @param treeList 树形结构列表
     * @param pj0101   项目ID
     */
    private void calculateFileCountForTree(List<Cg09DTO> treeList, Long pj0101) {
        if (treeList == null || treeList.isEmpty()) {
            return;
        }

        for (Cg09DTO node : treeList) {
            calculateNodeFileCount(node, pj0101);
        }
    }

    /**
     * 递归计算节点的附件数量
     *
     * @param node   当前节点
     * @param pj0101 项目ID
     * @return 当前节点及其子节点的总附件数量
     */
    private int calculateNodeFileCount(Cg09DTO node, Long pj0101) {
        int totalCount = 0;

        // 先计算当前节点自身的附件数量
        int selfCount = calculateActualFileCount(node, pj0101);
        totalCount += selfCount;

        // 如果有子节点，递归计算子节点的数量并累加
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (Cg09DTO child : node.getChildren()) {
                totalCount += calculateNodeFileCount(child, pj0101);
            }
        }

        // 设置当前节点的总附件数量（自身 + 所有子节点）
        node.setFileCount(totalCount);

        return totalCount;
    }

    /**
     * 计算节点的实际附件数量（不包括子节点）
     *
     * @param node   节点
     * @param pj0101 项目ID
     * @return 实际附件数量
     */
    private int calculateActualFileCount(Cg09DTO node, Long pj0101) {
        String nodeType = node.getType();

        // 对于动态生成的年份和月份节点，需要特殊处理
        if ("year".equals(nodeType)) {
            // 年份节点：不计算自身附件数量，因为附件都在月份节点中
            return 0;
        } else if ("month".equals(nodeType)) {
            // 月份节点：查询该档案在该年月的附件数量
            return calculateMonthFileCount(node, pj0101);
        } else {
            // 普通档案节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                // 如果有子节点（年份、月份节点），则自身不计算附件数量
                return 0;
            } else {
                // 如果没有子节点，返回从数据库查询得到的附件数量
                return node.getFileCount() != null ? node.getFileCount() : 0;
            }
        }
    }

    /**
     * 计算年份节点的附件数量
     *
     * @param yearNode 年份节点
     * @param pj0101   项目ID
     * @return 年份附件数量
     */
    private int calculateYearFileCount(Cg09DTO yearNode, Long pj0101) {
        // 从fileNumber中获取档案ID和年份信息
        String fileNumber = yearNode.getFileNumber();
        if (fileNumber != null && fileNumber.contains("_")) {
            String[] parts = fileNumber.split("_");
            if (parts.length >= 2) {
                try {
                    Long archiveId = Long.parseLong(parts[0]);
                    String year = parts[1];

                    Map<String, Object> params = new HashMap<>();
                    params.put("cg0901", archiveId);
                    params.put("year", year);
                    params.put("pj0101", pj0101);

                    Integer count = baseDao.getFileCountByArchiveAndYearOnly(params);
                    return count != null ? count : 0;
                } catch (NumberFormatException e) {
                    // 解析失败，返回0
                    return 0;
                }
            }
        }

        return 0;
    }

    /**
     * 计算月份节点的附件数量
     *
     * @param monthNode 月份节点
     * @param pj0101    项目ID
     * @return 月份附件数量
     */
    private int calculateMonthFileCount(Cg09DTO monthNode, Long pj0101) {
        // 从fileNumber中获取档案ID、年份和月份信息
        String fileNumber = monthNode.getFileNumber();
        if (fileNumber != null && fileNumber.contains("_")) {
            String[] parts = fileNumber.split("_");
            if (parts.length >= 3) {
                try {
                    Long archiveId = Long.parseLong(parts[0]);
                    String year = parts[1];
                    String month = parts[2];
                    String fileYear = year + String.format("%02d", Integer.parseInt(month));

                    Map<String, Object> params = new HashMap<>();
                    params.put("cg0901", archiveId);
                    params.put("fileYear", fileYear);
                    params.put("pj0101", pj0101);

                    Integer count = baseDao.getFileCountByArchiveAndYear(params);
                    return count != null ? count : 0;
                } catch (NumberFormatException e) {
                    // 解析失败，返回0
                    return 0;
                }
            }
        }

        return 0;
    }


}