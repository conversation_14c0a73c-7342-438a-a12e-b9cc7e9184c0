package io.renren.modules.enterprise.ps09.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import io.renren.common.exception.RenException;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.DateUtils;
import io.renren.common.utils.Result;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.enterprise.ps09.dao.Ps09Dao;
import io.renren.modules.enterprise.ps09.dto.Ps09DTO;
import io.renren.modules.enterprise.ps09.entity.Ps09Entity;
import io.renren.modules.enterprise.ps09.service.Ps09Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 工人工资附件表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-22
 */
@Service
public class Ps09ServiceImpl extends CrudServiceImpl<Ps09Dao, Ps09Entity, Ps09DTO> implements Ps09Service {

    @Autowired
    private Ot01Service ot01Service;

    @Override
    public QueryWrapper<Ps09Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps09Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Transactional
    @Override
    public Result saveFile(Ps09DTO dto) {
        if (Objects.isNull(dto)) {
            throw new RenException("传入信息为空");
        }
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        String yearMonth = DateUtils.format(dto.getYearmonth(), "yyyyMM");
        Ps09Entity uploadFile = baseDao.isUploadFile(pj0101, yearMonth);
        Long busies = null;
        if (Objects.nonNull(uploadFile)) {
            // 修改状态
            uploadFile.setIsupload(1);
            baseDao.updateById(uploadFile);
        }else {
            busies = IdWorker.getId();
            Ps09Entity build = Ps09Entity.builder().ps0901(busies).yearmonth(yearMonth).isupload(1).pj0101(pj0101).build();
            baseDao.insert(build);
        }
        //如果有合同
        List<Ot01DTO> ot01DTOList = dto.getOt01DTO();
        List<Long> ot0101List = ot01DTOList.stream()
                .filter(ot01DTO -> StrUtil.isBlank(ot01DTO.getBusisysno()))
                .map(Ot01DTO::getOt0101)
                .collect(Collectors.toList());
        if (ot0101List.size() > 0) {
            ot01Service.updateBusily(busies, ot0101List);
        }
        return new Result().ok("上传成功");
    }
}