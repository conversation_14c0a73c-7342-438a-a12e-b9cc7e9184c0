package io.renren.modules.enterprise.pw02.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工资导入明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Data
public class Pw02Excel {
    @Excel(name = "")
    private BigDecimal pw0201;
    @Excel(name = "")
    private BigDecimal pw0101;
    @Excel(name = "人员ID")
    private BigDecimal ps0201;
    @Excel(name = "考勤天数")
    private Short kqts;
    @Excel(name = "应发工资")
    private BigDecimal yfgz;
    @Excel(name = "银行卡号")
    private String kh;
    @Excel(name = "扣发工资")
    private BigDecimal kfgz;
    @Excel(name = "实发工资")
    private BigDecimal sfgz;
    @Excel(name = "系统考勤天数")
    private Short xtkqts;
    @Excel(name = "工人身份证号")
    private String idcardnumber;
    @Excel(name = "工人姓名")
    private String name;
    @Excel(name = "项目id")
    private BigDecimal pj0101;
    @Excel(name = "工资所属年月")
    private Integer month;
    @Excel(name = "工人手机号")
    private String mobile;
    @Excel(name = "补录说明")
    private String reason;
    @Excel(name = "审核状态（0：不通过，1：通过）")
    private Short auditstatus;
    @Excel(name = "导入状态（0：完全匹配，1：补录，2：异常）")
    private Short importstatus;
    @Excel(name = "发放状态（1：成功，2：失败）")
    private Short status;

}