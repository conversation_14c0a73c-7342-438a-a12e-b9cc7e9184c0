package io.renren.modules.enterprise.tm01.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_TM01")
public class Tm01Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long tm0101;
    /**
     * 参建单位ID
     */
    private Long cp0201;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 班组编号
     */
    private String teamsysno;
    /**
     * 班组名称
     */
    private String teamname;
    /**
     * 责任人姓名
     */
    private String responsiblepersonname;
    /**
     * 责任人联系电话
     */
    private String responsiblepersonphone;
    /**
     * 证件类型
     */
    private String idcardtype;
    /**
     * 证件号码
     */
    private String responsiblepersonidnumber;
    /**
     * 备注
     */
    private String memo;
    /**
     * 进场时间
     */
    private Date entrytime;
    /**
     * 退场时间
     */
    private Date exittime;
    /**
     * 进退场状态
     */
    private String inOrOut;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}