package io.renren.modules.enterprise.kq01log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.kq01log.dao.Kq01LogDao;
import io.renren.modules.enterprise.kq01log.dto.Kq01LogDTO;
import io.renren.modules.enterprise.kq01log.dto.Kq01LogPageDTO;
import io.renren.modules.enterprise.kq01log.entity.Kq01LogEntity;
import io.renren.modules.enterprise.kq01log.service.Kq01LogService;
import io.renren.modules.enterprise.kq04.dao.Kq04Dao;
import io.renren.modules.enterprise.kq04.dto.Kq04DTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 考勤设备信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Kq01LogServiceImpl extends CrudServiceImpl<Kq01LogDao, Kq01LogEntity, Kq01LogDTO> implements Kq01LogService {

    @Autowired
    private Kq04Dao kq04Dao;

    @Override
    public QueryWrapper<Kq01LogEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Kq01LogEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }


    @Override
    public PageData<Kq01LogPageDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Kq01LogEntity> page = getPage(params, "t.CREATE_DATE", false);
        List<Kq01LogPageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Kq01LogPageDTO.class);
    }

    @Override
    public PageData<Kq04DTO> getLog(Map<String, Object> params) {
        IPage<Kq01LogEntity> page = getPage(params, "", false);
        List<Kq04DTO> list  = baseDao.getListInfo(params);
        return getPageData(list, page.getTotal(), Kq04DTO.class);
    }


}