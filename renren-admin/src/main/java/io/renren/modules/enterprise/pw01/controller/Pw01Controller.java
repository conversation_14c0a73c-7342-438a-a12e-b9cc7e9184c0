package io.renren.modules.enterprise.pw01.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.enterprise.pw01.dto.Pw01DTO;
import io.renren.modules.enterprise.pw01.excel.Pw01Excel;
import io.renren.modules.enterprise.pw01.service.Pw01Service;
import io.renren.modules.enterprise.pw02.dto.Pw02DTO;
import io.renren.modules.enterprise.pw02.service.Pw02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 工资导入记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@RestController
@RequestMapping("enterprise/pw01")
@Api(tags="工资导入记录表")
public class Pw01Controller {
    @Autowired
    private Pw01Service pw01Service;
    @Autowired
    private Pw02Service pw02Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pw01:page")
    public Result<PageData<Pw01DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Pw01DTO> page = pw01Service.pageList(params);

        return new Result<PageData<Pw01DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pw01:info")
    public Result<Pw01DTO> get(@PathVariable("id") Long id){
        Pw01DTO data = pw01Service.get(id);

        return new Result<Pw01DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:pw01:save")
    public Result save(@RequestBody Pw01DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pw01Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:pw01:update")
    public Result update(@RequestBody Pw01DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pw01Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pw01:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pw01Service.delete(ids);

        return new Result();
    }

    @PostMapping("submit")
    @ApiOperation("提交")
    @LogOperation("提交")
    @RequiresPermissions("enterprise:pw01:submit")
    public Result submit(@RequestBody Pw01DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);
        Pw01DTO pw01DTO = pw01Service.get(dto.getPw0101());
        if (StrUtil.equals(pw01DTO.getAuditstatus(), "1")) {
            return new Result().error("该记录已审核通过，无法重复提交");
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put("pw0101", dto.getPw0101().toString());
        List<Pw02DTO> list = pw02Service.getList(params);
        List<Pw02DTO> collect = list.stream()
                .filter(x -> !"0".equals(x.getImportstatus()) && StrUtil.isEmpty(x.getReason()))
                .collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(collect)) {
            String names = collect.stream().map(Pw02DTO::getName).collect(Collectors.joining(","));
            return new Result().error(names + "说明未填写，无法提交审核");
        }

        pw01DTO.setIssubmit("1");
        pw01DTO.setAuditstatus("0");
        pw01DTO.setSubmittime(new Date());
        pw01DTO.setAuditreason(null);
        pw01Service.update(pw01DTO);
        pw02Service.updateAuditStatusByPw0101(dto.getPw0101());

        return new Result();
    }

    @PostMapping("importSalary")
    @ApiOperation("上传工资表")
    @LogOperation("上传工资表")
    @RequiresPermissions("enterprise:pw01:importSalary")
    public Result importSalary(@RequestParam("file") MultipartFile file, @RequestParam("month") String month) throws Exception {
        Result result = new Result();
        Map<String, Object> params = new HashMap<>();
        params.put("month", month);
        try{
            result = pw02Service.importSalary(file,params);
        }catch (RenException e){
            result.error("导入失败："+e.getMsg());
        }catch (Exception e){
            result.error("导入失败：文档数据填写有误，请确认填写是否正确！");
        }
        return result;
    }

    @GetMapping("/download")
    @ApiOperation("导入模板下载")
    @LogOperation("导入模板下载")
    @RequiresPermissions("enterprise:pw01:download")
    public void download(HttpServletResponse response, @RequestParam(name = "month", required = false) String month) {
        pw02Service.download(response, month);
    }

    @GetMapping("/export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:pw01:export")
    public void export(HttpServletResponse response, @RequestParam(name = "pw0101") Long pw0101) {
        pw02Service.export(response, pw0101);
    }

}