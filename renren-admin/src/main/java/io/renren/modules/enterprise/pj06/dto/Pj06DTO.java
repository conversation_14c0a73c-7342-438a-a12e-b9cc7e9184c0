package io.renren.modules.enterprise.pj06.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Data
@ApiModel(value = "项目注册信息")
public class Pj06DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long pj0601;
    @ApiModelProperty(value = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String name;
    @ApiModelProperty(value = "所属行业")
    private String industry;
    @ApiModelProperty(value = "项目所在地")
//    @NotBlank(message = "项目所在地不能为空")
    private String areacode;
    @ApiModelProperty(value = "建设地址")
    @NotBlank(message = "建设地址不能为空")
    private String address;
    @ApiModelProperty(value = "经度")
    private BigDecimal lng;
    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;
    @ApiModelProperty(value = "建设单位ID")
    @NotBlank(message = "建设单位不能为空")
    private Long constructcode;
    @ApiModelProperty(value = "总包单位ID")
    @NotBlank(message = "总包单位不能为空")
    private Long contractcode;
    @ApiModelProperty(value = "监理单位ID")
    @NotBlank(message = "监理单位不能为空")
    private Long supervisecode;
    @ApiModelProperty(value = "联系人姓名")
    @NotBlank(message = "联系人姓名不能为空")
    private String linkman;
    @ApiModelProperty(value = "联系人电话")
    @NotBlank(message = "联系人电话不能为空")
    private String linkphone;
    @ApiModelProperty(value = "主管部门")
    @NotBlank(message = "主管部门不能为空")
    private String virareacode;
    @ApiModelProperty(value = "上报地")
    private String report;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "审核状态")
    private String auditstatus;
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditdate;
    @ApiModelProperty(value = "审核人")
    private String auditor;
    @ApiModelProperty(value = "审核结果")
    private String auditresult;
    @ApiModelProperty(value = "注册资料")
    private List<Ot01DTO> registerFiles;
    @ApiModelProperty(value = "注册手机Code")
    @NotBlank(message = "验证码不能为空")
    private String registerCode;
    @ApiModelProperty(value = "中心负责人id")
    private Long ps1201;
}