package io.renren.modules.enterprise.ps03.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.admin.sys.dto.SysDictDataDTO;
import io.renren.modules.enterprise.ps03.dto.EnterPersonDTO;
import io.renren.modules.enterprise.ps03.dto.ProjectInfoDTO;
import io.renren.modules.enterprise.ps03.dto.Ps03DTO;
import io.renren.modules.enterprise.ps03.dto.Ps03PageDTO;
import io.renren.modules.enterprise.ps03.excel.Ps03Excel;
import io.renren.modules.enterprise.ps03.service.Ps03Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 管理人员在职信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@RestController
@RequestMapping("enterprise/ps03")
@Api(tags="管理人员在职信息表")
public class Ps03Controller {
    @Autowired
    private Ps03Service ps03Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
//    @RequiresPermissions("enterprise:ps03:page")
    public Result<PageData<Ps03PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps03PageDTO> page = ps03Service.Ps03page(params);

        return new Result<PageData<Ps03PageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
//    @RequiresPermissions("enterprise:ps03:info")
    public Result<Ps03DTO> get(@PathVariable("id") Long id){
        Ps03DTO data = ps03Service.getPs03(id);

        return new Result<Ps03DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
 //   @RequiresPermissions("enterprise:ps03:save")
    public Result save(@RequestBody Ps03DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps03Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
 //   @RequiresPermissions("enterprise:ps03:update")
    public Result update(@RequestBody Ps03DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps03Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
 //   @RequiresPermissions("enterprise:ps03:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps03Service.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
//    @RequiresPermissions("enterprise:ps03:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Ps03DTO> list = ps03Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Ps03Excel.class);
    }

    @PostMapping("enterPerson")
    @ApiOperation("管理人员入场")
    @LogOperation("管理人员入场")
    //   @RequiresPermissions("enterprise:ps03:save")
    public Result enterPerson(@RequestBody EnterPersonDTO dto){

        return ps03Service.enterPerson(dto);
    }

    @GetMapping("getProjectInfo")
    @ApiOperation("获取企业所属项目信息")
    @LogOperation("获取企业所属项目信息")
    public Result<List<ProjectInfoDTO>> getProjectInfo() {

        return ps03Service.getProjectInfo();
    }

    @GetMapping("getJobType")
    @ApiOperation("获取岗位类型")
    @LogOperation("获取岗位类型")
    public Result<List<SysDictDataDTO>> getJobType() {

        return ps03Service.getJobType();
    }
}