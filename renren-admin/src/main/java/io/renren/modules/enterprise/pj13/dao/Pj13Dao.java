package io.renren.modules.enterprise.pj13.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj13.dto.Ps02ExitPageDTO;
import io.renren.modules.enterprise.pj13.dto.Ps04ExitPageDTO;
import io.renren.modules.enterprise.pj13.dto.Tm01ExitPageDTO;
import io.renren.modules.enterprise.pj13.entity.Pj13Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@Mapper
public interface Pj13Dao extends BaseDao<Pj13Entity> {

    List<Ps02ExitPageDTO> ps02ExitPage(@Param("page") Page<Ps02ExitPageDTO> page, @Param("params") Map<String, Object> params);

    List<Ps04ExitPageDTO> ps04ExitPage(@Param("page") Page<Ps04ExitPageDTO> page, @Param("params") Map<String, Object> params);

    List<Tm01ExitPageDTO> tm01ExitPage(@Param("page") Page<Tm01ExitPageDTO> page, @Param("params") Map<String, Object> params);
}