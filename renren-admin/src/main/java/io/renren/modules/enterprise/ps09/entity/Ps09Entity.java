package io.renren.modules.enterprise.ps09.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工人工资附件表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-22
 */
@Data
@Builder
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS09")
public class Ps09Entity {
	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	private Long ps0901;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 发放月份
     */
	private String yearmonth;
    /**
     * 是否上传
     */
	private Integer isupload;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}