package io.renren.modules.enterprise.pw02.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工资导入明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PW02")
public class Pw02Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
	@TableId
	private Long pw0201;
    /**
     * 工资导入记录表id
     */
	private Long pw0101;
    /**
     * 人员ID
     */
	private Long ps0201;
    /**
     * 考勤天数
     */
	private Integer kqts;
    /**
     * 应发工资
     */
	private double yfgz;
    /**
     * 银行卡号
     */
	private String kh;
	/**
	 * 开户行
	 */
	private String khh;
    /**
     * 实发工资
     */
	private double sfgz;
    /**
     * 系统考勤天数
     */
	@TableField(insertStrategy = FieldStrategy.DEFAULT)
	private Integer xtkqts;
    /**
     * 工人身份证号
     */
	private String idcardnumber;
    /**
     * 工人姓名
     */
	private String name;
    /**
     * 项目id
     */
	private Long pj0101;
    /**
     * 工资所属年月
     */
	private Long month;
    /**
     * 工人手机号
     */
	private String mobile;
    /**
     * 补录说明
     */
	private String reason;
    /**
     * 审核状态（0：待审核，1：通过，2：不通过）
     */
	private String auditstatus;
    /**
     * 导入状态（0：完全匹配，1：补录，2：异常）
     */
	private String importstatus;
    /**
     * 发放状态（1：成功，2：失败）
     */
	private String status;
	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private Long creator;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}