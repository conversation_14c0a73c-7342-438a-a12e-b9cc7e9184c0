package io.renren.modules.enterprise.share.dao;

import io.renren.common.common.dto.CommonDto;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.share.dto.ShareDTO;
import io.renren.modules.enterprise.share.entity.ShareEntity;
import io.renren.modules.enterprise.tm01.dto.Tm01PageDTO;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Mapper
public interface ShareDao extends BaseDao<ShareEntity> {
    /**
     * 查询分页列表
     *
     * @param params
     * @return
     */
    List<ShareDTO> getList(Map<String, Object> params);

    /**
     * 通过班组名称和项目id查询班组是否存在
     *
     * @param teamName 班组名称
     * @param pj0101 项目ID
     * @return Integer
     */
    Integer queryByName(@Param("teamName") String teamName, @Param("pj0101") Long pj0101);

    /**
     * 查询当前登录用户下边的班组信息
     *
     * @param deptId 当前登录用户deptId
     * @return list
     */
    List<CommonDto> loadTm01Info(Long deptId);


    /**
     * 查询当前登录用户下边的班组信息 已通过审核且当前企业需要在场
     *
     * @param deptId 当前登录用户deptId
     * @return list
     */
    List<CommonDto> loadTm01InfoAndPassed(Long deptId);


    /**
     * 查询人员信息
     *
     * @param longs 人员id
     * @return
     */
    List<PersonDTO> selectPersonByTeamIds(List<Long> longs);


    /**
     * 工人进退场
     *
     * @param ids  ps0201
     * @param type 进场1
     */
    void teamInByIds(@Param("list") List<Long> ids);

    /**
     * 工人进退场
     *
     * @param ids  ps0201
     * @param type 退场2
     */
    void teamOutByIds(@Param("list") List<Long> ids);

    String createTeamsysno();

    void unbind(Long userId, Long pj0101);
}