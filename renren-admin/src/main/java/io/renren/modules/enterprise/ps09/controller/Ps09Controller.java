package io.renren.modules.enterprise.ps09.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps09.dto.Ps09DTO;
import io.renren.modules.enterprise.ps09.excel.Ps09Excel;
import io.renren.modules.enterprise.ps09.service.Ps09Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 工人工资附件表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-10-22
 */
@RestController
@RequestMapping("enterprise/bps09")
@Api(tags="工人工资附件表")
public class Ps09Controller {
    @Autowired
    private Ps09Service bPs09Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:bps09:page")
    public Result<PageData<Ps09DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps09DTO> page = bPs09Service.page(params);

        return new Result<PageData<Ps09DTO>>().ok(page);
    }



    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:bps09:info")
    public Result<Ps09DTO> get(@PathVariable("id") Long id){
        Ps09DTO data = bPs09Service.get(id);

        return new Result<Ps09DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:bps09:save")
    public Result save(@RequestBody Ps09DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        bPs09Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:bps09:update")
    public Result update(@RequestBody Ps09DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        bPs09Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:bps09:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        bPs09Service.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:bps09:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Ps09DTO> list = bPs09Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Ps09Excel.class);
    }

}