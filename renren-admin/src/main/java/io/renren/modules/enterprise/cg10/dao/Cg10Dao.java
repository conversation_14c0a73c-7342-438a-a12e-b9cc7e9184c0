package io.renren.modules.enterprise.cg10.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.cg10.dto.Cg10DTO;
import io.renren.modules.enterprise.cg10.dto.Cg10Page;
import io.renren.modules.enterprise.cg10.entity.Cg10Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 项目档案上传
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-14
 */
@Mapper
public interface Cg10Dao extends BaseDao<Cg10Entity> {

    /**
     * 查询列表数据
     *
     * @param params Map<String, Object>
     * @return List<Cg10DTO>
     */
    List<Cg10Page> selectPageList(Map<String, Object> params);

    /**
     * 查询档案信息
     * @param params Map<String, Object>
     * @return Cg10DTO
     */
    Cg10DTO selectArchInfo(Map<String, Object> params);

}