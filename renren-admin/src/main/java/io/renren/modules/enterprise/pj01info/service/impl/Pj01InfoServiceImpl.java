package io.renren.modules.enterprise.pj01info.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.enterprise.pa01.dao.Pa01Dao;
import io.renren.modules.enterprise.pa01.entity.Pa01Entity;
import io.renren.modules.enterprise.pj01info.dao.Pj01InfoDao;
import io.renren.modules.enterprise.pj01info.dto.*;
import io.renren.modules.enterprise.pj01info.entity.Pj01InfoEntity;
import io.renren.modules.enterprise.pj01info.service.Pj01InfoService;
import io.renren.modules.enterprise.pj02.dto.Pj02DTO;
import io.renren.modules.enterprise.pj02.service.Pj02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Pj01InfoServiceImpl extends CrudServiceImpl<Pj01InfoDao, Pj01InfoEntity, Pj01InfoDTO> implements Pj01InfoService {
    @Autowired
    private Pj01InfoService Pj01InfoService;
    @Autowired
    private Pj02Service pj02Service;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Pa01Dao pa01Dao;

    @Override
    public QueryWrapper<Pj01InfoEntity> getWrapper(Map<String, Object> params) {
        String name = (String) params.get("name");
        QueryWrapper<Pj01InfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(name), "name", name);
        return wrapper;
    }

    @Override
    public PageData<Pj01InfoDTO> selectListData(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        Object areacode = params.get("areacode");
        if (areacode != null) {
            String areacodes = areacode.toString();
            if (areacodes.endsWith("0000")) {
                areacodes = areacodes.substring(0, 2);
            } else if (areacodes.endsWith("00")) {
                areacodes = areacodes.substring(0, 4);
            }
            params.put("areacode", areacodes);
        }
        IPage<Pj01InfoEntity> page = getPage(params, "", false);
        List<Pj01InfoDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Pj01InfoDTO.class);
    }

    @Override
    public Pj01InfoDTO getProjectInfo(Long id) {
        Pj01InfoDTO Pj01InfoDTO = Pj01InfoService.get(id);
        //查询施工许可证信息
        Pj02DTO byPj01Info01 = pj02Service.getByPj0101(id);
        Pj01InfoDTO.setPj02DTO(byPj01Info01);
        //查询项目合同
        List<Ot01DTO> ot01DTOList = ot01Service.loadBusinessData(id, "01");
        Pj01InfoDTO.setOt01DTOList(ot01DTOList);
        return Pj01InfoDTO;
    }

    @Override
    public Pa01Entity getSpecialAccount(Long pj0101) {
        return pa01Dao.selectOne(new QueryWrapper<Pa01Entity>().eq("pj0101", pj0101));
    }

    @Override
    public PageData<Cp02PageDTO> cp02PageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        // 分页
        IPage<Pj01InfoEntity> page = getPage(params, "", false);
        List<Cp02PageDTO> list = baseDao.getCp02PageList(params);
        return getPageData(list, page.getTotal(), Cp02PageDTO.class);
    }

    @Override
    public PageData<Tm01PageDTO> teamPageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        // 分页
        IPage<Pj01InfoEntity> page = getPage(params, "", false);
        List<Tm01PageDTO> list = baseDao.getTeamPageList(params);
        return getPageData(list, page.getTotal(), Tm01PageDTO.class);
    }

    @Override
    public PageData<Ps02PageDTO> workerPageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        // 分页
        IPage<Pj01InfoEntity> page = getPage(params, "", false);
        List<Ps02PageDTO> list = baseDao.getWorkerPageList(params);
        return getPageData(list, page.getTotal(), Ps02PageDTO.class);
    }

    @Override
    public PageData<Kq02PageDTO> attendancePageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        // 分页
        IPage<Pj01InfoEntity> page = getPage(params, "CHECKDATE", false);
        List<Kq02PageDTO> list = baseDao.getAttendancePageList(params);
        return getPageData(list, page.getTotal(), Kq02PageDTO.class);
    }

    @Override
    public PageData<Ps04PageDTO> managerPageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        // 分页
        IPage<Pj01InfoEntity> page = getPage(params, "", false);
        List<Ps04PageDTO> list = baseDao.getManagerPageList(params);
        return getPageData(list, page.getTotal(), Ps04PageDTO.class);
    }
}