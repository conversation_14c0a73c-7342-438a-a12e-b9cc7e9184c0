package io.renren.modules.enterprise.pj04.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.renren.modules.enterprise.pj04.dao.Pj04Dao;
import io.renren.modules.enterprise.pj04.dto.Pj04DTO;
import io.renren.modules.enterprise.pj04.entity.Pj04Entity;
import io.renren.modules.enterprise.pj04.service.Pj04Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目培训详情
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Pj04ServiceImpl extends ServiceImpl<Pj04Dao, Pj04Entity> implements Pj04Service {
    @Autowired
    private Pj04Dao pj04Dao;

    @Override
    public List<Pj04DTO> getByPj0301(Long id) {
        return pj04Dao.selectByPj0301(id);
    }
}