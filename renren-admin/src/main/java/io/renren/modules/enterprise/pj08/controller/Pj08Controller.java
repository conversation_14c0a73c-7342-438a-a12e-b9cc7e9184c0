package io.renren.modules.enterprise.pj08.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.pj08.dto.Pj08DTO;
import io.renren.modules.enterprise.pj08.service.Pj08Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 项目竣工验收信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-03-21
 */
@RestController
@RequestMapping("enterprise/pj08")
@Api(tags = "项目竣工验收信息表")
public class Pj08Controller {
    @Autowired
    private Pj08Service pj08Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:pj08:page")
    public Result<PageData<Pj08DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Pj08DTO> page = pj08Service.pageList(params);

        return new Result<PageData<Pj08DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("regul:pj08:info")
    public Result<Pj08DTO> get(@PathVariable("id") Long id) {
        Pj08DTO data = pj08Service.get(id);

        return new Result<Pj08DTO>().ok(data);
    }

    @PostMapping("saveInfo")
    @ApiOperation("竣工申请")
    @LogOperation("竣工申请")
    //@RequiresPermissions("enterprise:pj08:saveInfo")
    public Result saveInfo(@RequestBody Pj08DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        Result result = pj08Service.saveInfo(dto);

        return result;
    }

    @PostMapping("callback")
    @ApiOperation("撤销申请")
    @LogOperation("撤销申请")
    //@RequiresPermissions("enterprise:pj08:callback")
    public Result callback(@RequestBody Pj08DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        Result result = pj08Service.callback(dto);

        return result;
    }


}