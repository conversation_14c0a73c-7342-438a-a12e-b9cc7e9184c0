package io.renren.modules.enterprise.excelexp.controller;

import com.google.gson.JsonObject;
import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.excelexp.service.ExcelExpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * 三表导出
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-18
 */
@RestController
@RequestMapping("enterprise/excelExp")
@Api(tags="三表导出")
public class ExcelExpController {
    @Autowired
    private ExcelExpService excelExpService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:excelExp:page")
    public Result<PageData<JsonObject>> page(@ApiIgnore @RequestParam Map<String, Object> params){

        PageData<JsonObject> page = excelExpService.getPageList(params);

        return new Result<PageData<JsonObject>>().ok(page);
    }

    @GetMapping("export")
    @ApiOperation("导出工资发放花名册")
    @LogOperation("导出工资发放花名册")
    @RequiresPermissions("enterprise:excelExp:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {

      excelExpService.excelExp(params,response);

    }

}