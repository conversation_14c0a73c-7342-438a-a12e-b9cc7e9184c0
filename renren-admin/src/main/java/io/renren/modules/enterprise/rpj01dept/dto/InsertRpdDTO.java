package io.renren.modules.enterprise.rpj01dept.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 添加关系类
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-11
 */
@Data
@ApiModel(value = "添加关系类")
public class InsertRpdDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机构id")
    private Long deptId;

    @ApiModelProperty(value = "项目id集合")
    private List<Long> pj0101s;

}