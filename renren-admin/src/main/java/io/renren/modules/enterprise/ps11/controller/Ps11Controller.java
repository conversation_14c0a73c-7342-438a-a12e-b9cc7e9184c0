package io.renren.modules.enterprise.ps11.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps11.dto.Ps11DTO;
import io.renren.modules.enterprise.ps11.service.Ps11Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 管理人员/工人预约进场
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-25
 */
@RestController
@RequestMapping("enterprise/ps11")
@Api(tags="管理人员/工人预约进场")
public class Ps11Controller {
    @Autowired
    private Ps11Service ps11Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:ps11:page")
    public Result<PageData<Ps11DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps11DTO> page = ps11Service.page(params);

        return new Result<PageData<Ps11DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps11:info")
    public Result<Ps11DTO> get(@PathVariable("id") Long id){
        Ps11DTO data = ps11Service.get(id);

        return new Result<Ps11DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:ps11:save")
    public Result save(@RequestBody Ps11DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps11Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:ps11:update")
    public Result update(@RequestBody Ps11DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps11Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:ps11:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps11Service.delete(ids);

        return new Result();
    }
}