package io.renren.modules.enterprise.ps02face.service;

import com.arcsoft.face.FaceInfo;
import com.arcsoft.face.toolkit.ImageInfo;
import io.renren.modules.enterprise.ps02face.dto.FaceRecoResDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20
 */
public interface IFaceEngineService {

    /**
     * 人脸检测
     * @param imageInfo
     * @return
     */
    List<FaceInfo> detectFaces(ImageInfo imageInfo);

    /**
     * 人脸比较
     * @param imageInfo1
     * @param imageInfo2
     * @return
     */
    Float compareFace(ImageInfo imageInfo1,ImageInfo imageInfo2) ;

    /**
     * 提取人脸特征值
     * @param imageInfo
     * @param faceInfo
     * @return
     */
    byte[] extractFaceFeature(ImageInfo imageInfo,FaceInfo faceInfo);

    /**
     * 人脸识别
     * @param faceFeature
     * @param userInfoList
     * @param passRate
     * @return
     */
    List<FaceRecoResDTO> faceRecoSearch(byte[] faceFeature, List userInfoList, float passRate) ;

}
