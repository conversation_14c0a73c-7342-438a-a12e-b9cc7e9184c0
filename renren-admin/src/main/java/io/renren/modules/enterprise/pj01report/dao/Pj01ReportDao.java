package io.renren.modules.enterprise.pj01report.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj01report.dto.Pj01ReportDTO;
import io.renren.modules.enterprise.pj01report.entity.Pj01ReportEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 项目上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-12
 */
@Mapper
public interface Pj01ReportDao extends BaseDao<Pj01ReportEntity> {


    /**
     * 列表分页查询数据
     *
     * @param params
     * @return
     */
    List<Pj01ReportDTO> getListData(Map<String, Object> params);

    /**
     * 根据pj0101查询配置
     *
     * @param pj0101
     * @return
     */
    Pj01ReportDTO getData(Long pj0101);

}