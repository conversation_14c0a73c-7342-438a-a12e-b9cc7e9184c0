package io.renren.modules.enterprise.ps06.service;


import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.ps06.dto.BPs06DTO;
import io.renren.modules.enterprise.ps06.entity.BPs06Entity;

import java.util.Date;
import java.util.List;

/**
 * 工人进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-27
 */
public interface BPs06Service extends CrudService<BPs06Entity, BPs06DTO> {

    /**
     * 构建Ps06
     *
     * @param ps0201s          工人ids
     * @param inOrOut         进退场状态
     * @return
     */
    void batchInsertInOrOut(List<Long> ps0201s, String inOrOut);

    /**
     * 记录进退场（单）
     */
    void batchInsertInOrOut(BPs06DTO bPs06DTO);

    /**
     * 记录进退场（批量）
     *
     * @param dtoList
     */
    void batchInsertInOrOut(List<BPs06DTO> dtoList);
}