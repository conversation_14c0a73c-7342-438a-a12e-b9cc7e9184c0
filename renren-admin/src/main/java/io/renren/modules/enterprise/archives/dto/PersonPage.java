package io.renren.modules.enterprise.archives.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "人员档案分页")
public class PersonPage implements Serializable {
    private static final long serialVersionUID = -2144694517913788461L;

    @ApiModelProperty(value = "人员ID")
    private Long personId;

    @ApiModelProperty(value = "所属班组")
    private String teamName;

    @ApiModelProperty(value = "人员姓名")
    private String personName;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idCardNumber;

    @ApiModelProperty(value = "工种")
    private String workTypeCode;

    @ApiModelProperty(value = "是否班组长")
    private String isTeamLeader;

    @ApiModelProperty(value = "状态")
    private String inOrOut;

    @ApiModelProperty(value = "是否上传")
    private String whether;
}
