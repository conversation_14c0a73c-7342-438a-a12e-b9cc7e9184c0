package io.renren.modules.enterprise.tjattendanceinfo.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceGenerateDataDTO;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoDTO;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceInfoPageExportDTO;
import io.renren.modules.enterprise.tjattendanceinfo.entity.TjAttendanceInfoEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 考勤统计表详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-05-14
 */
public interface TjAttendanceInfoService extends CrudService<TjAttendanceInfoEntity, TjAttendanceInfoDTO> {

    /**
     * 导出考勤表明细数据
     *
     * @param params
     * @param response
     */
    void exportTjAttendanceInfo(Map<String, Object> params, HttpServletResponse response);

    /**
     * 生成数据月份
     *
     * @param dto
     */
    void generateTjAttendanceData(TjAttendanceGenerateDataDTO dto);

    /**
     * 分页
     *
     * @param params
     * @return
     */
    PageData<TjAttendanceInfoPageExportDTO> pageList(Map<String, Object> params);

    /**
     * 判断项目某月是否生成考勤数据
     *
     * @param params
     */
    boolean existTjAttendanceData(Map<String, Object> params);

    /**
     * 获取人员月份考勤天数
     *
     * @param params
     */
    Integer getKqdaysByIdcardnumber(Map<String, Object> params);

    /**
     * 删除数据月份
     *
     * @param params
     */
    void deleteTjAttendanceData(Map<String, Object> params);
}