package io.renren.modules.enterprise.archives.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "班组档案分页")
public class TeamPage implements Serializable {
    private static final long serialVersionUID = -2144694517913788461L;

    @ApiModelProperty(value = "人员ID")
    private Long teamId;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "责任人姓名")
    private String  responsibleName;

    @ApiModelProperty(value = "责任人联系电话")
    private String phone;

    @ApiModelProperty(value = "状态")
    private String inOrOut;

    @ApiModelProperty(value = "是否上传")
    private String whether;
}
