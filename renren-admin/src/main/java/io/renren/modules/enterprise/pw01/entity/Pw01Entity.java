package io.renren.modules.enterprise.pw01.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工资导入记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PW01")
public class Pw01Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@TableId
	private Long pw0101;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 应发档案工资总额
     */
	private double yfgz;
    /**
     * 发放总人数
     */
	private Long ffzrs;
    /**
     * 工资所属年月
     */
	private Long month;
    /**
     * 是否确认
     */
	private String issubmit;
    /**
     * 实发工资总额
     */
	private double sfgz;
    /**
     * 提交时间
     */
	private Date submittime;
    /**
     * 审核状态（0：待审核，1：通过，2：驳回）
     */
	private String auditstatus;
    /**
     * 审核说明
     */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String auditreason;
	/**
	 * 创建者
	 */
	@TableField(fill = FieldFill.INSERT)
	private Long creator;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}