package io.renren.modules.enterprise.ps02face.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj01facetemp.entity.Pj01FaceTempEntity;
import io.renren.modules.enterprise.ps02face.entity.Ps02FaceEntity;
import io.renren.modules.enterprise.ps02facedata.entity.Ps02FaceDataEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工人人脸库信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-20
 */
@Mapper
public interface Ps02FaceDao extends BaseDao<Ps02FaceEntity> {

    /**
     * 获取未采集完成项目
     *
     * @return
     */
    List<Pj01FaceTempEntity> getUnCollectedPj01();

    /**
     * 当前项目数据采集完成
     * @param pj0101
     */
    void updatePj01FaceTempComplate(Long pj0101);

    /**
     * 获取未采集人员
     * @return
     */
    List<Ps02FaceDataEntity> getUnCollectedPs02(Long pj0101);

    /**
     * 当前项目数据采集完成
     * @param ps0201
     */
    void updatePs02FaceTempComplate(Long ps0201);
}