package io.renren.modules.enterprise.pj04.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.renren.modules.enterprise.pj04.dto.Pj04DTO;
import io.renren.modules.enterprise.pj04.entity.Pj04Entity;

import java.util.List;

/**
 * 项目培训详情
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Pj04Service extends IService<Pj04Entity> {
    /**
     * 根据Pj0301查询对应的培训详情信息
     * @param id pj0301
     * @return
     */
    List<Pj04DTO> getByPj0301(Long id);
}