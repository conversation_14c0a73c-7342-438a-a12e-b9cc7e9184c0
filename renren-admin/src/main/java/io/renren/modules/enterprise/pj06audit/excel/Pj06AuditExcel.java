package io.renren.modules.enterprise.pj06audit.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-27
 */
@Data
public class Pj06AuditExcel {
    @Excel(name = "主键id")
    private BigDecimal id;
    @Excel(name = "")
    private BigDecimal pj0601;
    @Excel(name = "审核状态（0：待审核，1：审核通过，2：审核不通过）")
    private String auditstatus;
    @Excel(name = "审核结果")
    private String auditresult;
    @Excel(name = "审核时间")
    private Date auditdate;
    @Excel(name = "企业id")
    private BigDecimal cp0101;
    @Excel(name = "参建类型")
    private String corptype;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}