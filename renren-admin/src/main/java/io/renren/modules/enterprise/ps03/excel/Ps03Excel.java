package io.renren.modules.enterprise.ps03.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理人员在职信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@Data
public class Ps03Excel {
    @Excel(name = "主键ID")
    private BigDecimal ps0301;
    @Excel(name = "当前项目ID")
    private BigDecimal pj0101;
    @Excel(name = "在职企业ID")
    private BigDecimal cp0101;
    @Excel(name = "人员ID")
    private BigDecimal ps0101;
    @Excel(name = "在职状态")
    private String managestatus;
    @Excel(name = "头像采集照片")
    private String photo;
    @Excel(name = "是否购买工伤或意外伤害保险")
    private String hasbuyinsurance;
    @Excel(name = "入职时间")
    private Date inductiontime;
    @Excel(name = "离职时间")
    private Date departuretime;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}