package io.renren.modules.enterprise.cg10.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "档案数量信息")
public class FileCountInfo implements Serializable {
    private static final long serialVersionUID = -8874329922452261326L;

    @ApiModelProperty(value = "档案ID")
    private Long cg0901;

    @ApiModelProperty(value = "档案所属年月")
    private String fileYear;

    @ApiModelProperty(value = "档案数量")
    private Integer fileCount;
}
