package io.renren.modules.enterprise.pj06audit.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PJ06_AUDIT")
public class Pj06AuditEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * $column.comments
     */
    private Long pj0601;
    /**
     * 审核状态（0：待审核，1：审核通过，2：审核不通过）
     */
    private String auditstatus;
    /**
     * 审核结果
     */
    private String auditresult;
    /**
     * 审核时间
     */
    private Date auditdate;
    /**
     * 企业id
     */
    private Long cp0101;
    /**
     * 参建类型
     */
    private String corptype;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}