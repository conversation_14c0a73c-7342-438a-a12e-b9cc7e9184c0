package io.renren.modules.enterprise.pj13.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "工人退场分页")
public class Ps02ExitPageDTO {
    @ApiModelProperty(value = "工人姓名")
    private String name;
    @ApiModelProperty(value = "工人id")
    private Long ps0201;
    @ApiModelProperty(value = "身份证号")
    private String idcardnumber;
    @ApiModelProperty(value = "性别")
    private String gender;
    @ApiModelProperty(value = "手机号码")
    private String cellphone;
    @ApiModelProperty(value = "班组名称")
    private String teamName;
    @ApiModelProperty(value = "入场时间")
    private Date entrytime;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核失败原因")
    private String auditreason;
}
