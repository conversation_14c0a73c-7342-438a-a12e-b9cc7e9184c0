package io.renren.modules.enterprise.tjattendanceinfo.annotation;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;

import java.io.IOException;

/**
 * 考勤数据明细表导出，数据格式化
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2024/5/15 14:55
 **/
public class AttendanceFormatSerialize extends JsonSerializer<String> implements ContextualSerializer {

    private char oldSymbol;
    private char nowSymbol;

    public AttendanceFormatSerialize(final char nowSymbol, final char oldSymbol) {
        this.nowSymbol = nowSymbol;
        this.oldSymbol = oldSymbol;
    }

    public AttendanceFormatSerialize() {

    }

    @Override
    public void serialize(String str, JsonGenerator jsonGenerator, SerializerProvider beanProperty) throws IOException {
        String replace = str.replace(this.oldSymbol, this.nowSymbol);
        jsonGenerator.writeString(replace);
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider provider, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty != null) {
            // 获取字段是否有脱敏注解，有则创建一个序列化对象，并调用serialize方法
            AttendanceFormat attendanceFormat = beanProperty.getAnnotation(AttendanceFormat.class);
            if (attendanceFormat == null) {
                attendanceFormat = beanProperty.getContextAnnotation(AttendanceFormat.class);
            }
            if (attendanceFormat != null) {
                return new AttendanceFormatSerialize(attendanceFormat.nowSymbol(), attendanceFormat.oldSymbol());
            }
            return provider.findValueSerializer(beanProperty.getType(), beanProperty);
        }
        return provider.findNullValueSerializer(null);
    }
}
