package io.renren.modules.enterprise.tjattendanceinfo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 考勤统计表详情
 *
 * <AUTHOR>
 * @since 1.0.0 2024-05-14
 */
@Data
@ApiModel(value = "考勤统计表详情")
public class TjAttendanceInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "班组ID")
    private Long tm0101;
    @ApiModelProperty(value = "人员ID")
    private Long userId;
    @ApiModelProperty(value = "人员类型")
    private String personType;
    @ApiModelProperty(value = "人员姓名")
    private String workername;
    @ApiModelProperty(value = "岗位")
    private String jobtype;
    @ApiModelProperty(value = "当前进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "考勤月份")
    private String kqmonth;
    @ApiModelProperty(value = "考勤天数")
    private Long kqdays;
    @ApiModelProperty(value = "1号")
    private String day1;
    @ApiModelProperty(value = "2号")
    private String day2;
    @ApiModelProperty(value = "3号")
    private String day3;
    @ApiModelProperty(value = "4号")
    private String day4;
    @ApiModelProperty(value = "5号")
    private String day5;
    @ApiModelProperty(value = "6号")
    private String day6;
    @ApiModelProperty(value = "7号")
    private String day7;
    @ApiModelProperty(value = "8号")
    private String day8;
    @ApiModelProperty(value = "9号")
    private String day9;
    @ApiModelProperty(value = "10号")
    private String day10;
    @ApiModelProperty(value = "11号")
    private String day11;
    @ApiModelProperty(value = "12号")
    private String day12;
    @ApiModelProperty(value = "13号")
    private String day13;
    @ApiModelProperty(value = "14号")
    private String day14;
    @ApiModelProperty(value = "15号")
    private String day15;
    @ApiModelProperty(value = "16号")
    private String day16;
    @ApiModelProperty(value = "17号")
    private String day17;
    @ApiModelProperty(value = "18号")
    private String day18;
    @ApiModelProperty(value = "19号")
    private String day19;
    @ApiModelProperty(value = "20号")
    private String day20;
    @ApiModelProperty(value = "21号")
    private String day21;
    @ApiModelProperty(value = "22号")
    private String day22;
    @ApiModelProperty(value = "23号")
    private String day23;
    @ApiModelProperty(value = "24号")
    private String day24;
    @ApiModelProperty(value = "25号")
    private String day25;
    @ApiModelProperty(value = "26号")
    private String day26;
    @ApiModelProperty(value = "27号")
    private String day27;
    @ApiModelProperty(value = "28号")
    private String day28;
    @ApiModelProperty(value = "29号")
    private String day29;
    @ApiModelProperty(value = "30号")
    private String day30;
    @ApiModelProperty(value = "31号")
    private String day31;
    @ApiModelProperty(value = "统计时间")
    private Date tjdate;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "参建企业")
    private Long cp0101;
    @ApiModelProperty(value = "证件号码")
    private String idcardnumber;
    @ApiModelProperty(value = "合计工时")
    private String kqhours;
    @ApiModelProperty(value = "身份证住址")
    private String address;
    @ApiModelProperty(value = "联系电话")
    private String cellphone;
    @ApiModelProperty(value = "紧急联系电话")
    private String urgentlinkmanphone;
    @ApiModelProperty(value = "工资卡开户行名称")
    private String payrollbankname;
    @ApiModelProperty(value = "工资卡帐号")
    private String payrollbankcardnumber;

}