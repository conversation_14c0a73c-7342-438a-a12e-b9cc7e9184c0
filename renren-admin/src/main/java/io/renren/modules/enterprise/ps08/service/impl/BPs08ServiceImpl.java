package io.renren.modules.enterprise.ps08.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.ps08.dao.BPs08Dao;
import io.renren.modules.enterprise.ps08.dto.BPs08DTO;
import io.renren.modules.enterprise.ps08.entity.BPs08Entity;
import io.renren.modules.enterprise.ps08.service.BPs08Service;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> Chris
 * @Date : 2021-10-11
 **/
@Service
public class BPs08ServiceImpl extends ServiceImpl<BPs08Dao, BPs08Entity> implements BPs08Service {

}
