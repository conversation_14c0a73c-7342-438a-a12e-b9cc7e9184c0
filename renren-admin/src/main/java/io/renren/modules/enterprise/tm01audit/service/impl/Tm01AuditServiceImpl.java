package io.renren.modules.enterprise.tm01audit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.tm01audit.dao.Tm01AuditDao;
import io.renren.modules.enterprise.tm01audit.dto.Tm01AuditDTO;
import io.renren.modules.enterprise.tm01audit.dto.Tm01DetailAuditDTO;
import io.renren.modules.enterprise.tm01audit.dto.Tm01PageAuditDTO;
import io.renren.modules.enterprise.tm01audit.entity.Tm01AuditEntity;
import io.renren.modules.enterprise.tm01audit.service.Tm01AuditService;
import io.renren.modules.ot01.service.Ot01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 分包班组审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@Service
public class Tm01AuditServiceImpl extends CrudServiceImpl<Tm01AuditDao, Tm01AuditEntity, Tm01AuditDTO> implements Tm01AuditService {

    @Autowired
    private Ot01Service ot01Service;

    @Override
    public QueryWrapper<Tm01AuditEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Tm01AuditEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Tm01PageAuditDTO> getPageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Tm01AuditEntity> page = getPage(params, "", false);
        List<Tm01PageAuditDTO> list = baseDao.getPageList(params);
        return getPageData(list, page.getTotal(), Tm01PageAuditDTO.class);
    }

    @Override
    public Tm01DetailAuditDTO getInfo(Long id) {
        Tm01DetailAuditDTO tm01DetailDTO = baseDao.getInfo(id);
        tm01DetailDTO.setOt01DTOList(ot01Service.loadBusinessData(id, "05"));
        return tm01DetailDTO;
    }
}