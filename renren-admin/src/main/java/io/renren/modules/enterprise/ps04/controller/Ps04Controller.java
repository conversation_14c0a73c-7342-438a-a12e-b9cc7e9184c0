package io.renren.modules.enterprise.ps04.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps04.dto.DispatchRecordDTO;
import io.renren.modules.enterprise.ps04.dto.Ps04DTO;
import io.renren.modules.enterprise.ps04.dto.Ps04PageDTO;
import io.renren.modules.enterprise.ps04.service.Ps04Service;
import io.renren.modules.enterprise.ps04.vo.Ps04SaveVO;
import io.renren.modules.enterprise.ps13.service.Ps13Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 管理人员参建信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-11-19
 */
@RestController
@RequestMapping("enterprise/ps04")
@Api(tags = "管理人员参建信息表")
public class Ps04Controller {
    @Autowired
    private Ps04Service ps04Service;
    @Autowired
    private Ps13Service ps13Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "name", value = "人员名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "jobtype", value = "岗位类型", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "inOrOut", value = "进退场", paramType = "query", dataType = "String"),
    })
    @RequiresPermissions("enterprise:ps04:page")
    public Result<PageData<Ps04PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps04PageDTO> page = ps04Service.ps04Page(params);

        return new Result<PageData<Ps04PageDTO>>().ok(page);
    }

    @GetMapping("dispatchRecords")
    @ApiOperation("管理人员派遣记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    public Result<PageData<DispatchRecordDTO>> dispatchRecords(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<DispatchRecordDTO> page = ps04Service.dispatchRecords(params);

        return new Result<PageData<DispatchRecordDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps04:info")
    public Result<Ps04DTO> get(@PathVariable("id") Long id) {
        Ps04DTO data = ps04Service.getInfo(id);

        return new Result<Ps04DTO>().ok(data);
    }

    @PostMapping("save")
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:ps04:save")
    public Result save(@RequestBody Ps04SaveVO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        Result result = ps04Service.savePs04(dto);

        return result;
    }

    @PostMapping("update")
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:ps04:update")
    public Result update(@RequestBody Ps04DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        Result result = ps04Service.updateInfo(dto);

        return result;
    }

    @PutMapping("personAuthor")
    @ApiOperation("人员下发")
    @RequiresPermissions("enterprise:ps04:personAuthor")
    public Result personAuthor(@RequestBody Long[] ids) {
        ps04Service.deviceAddPerson(ids);
        return new Result();
    }

    @PostMapping("exit")
    @ApiOperation("退场")
    @LogOperation("退场")
    @RequiresPermissions("enterprise:ps04:exit")
    public Result exit(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps04Service.exit(ids);

        return new Result();
    }

    @PostMapping("entry")
    @ApiOperation("进场")
    @LogOperation("进场")
    @RequiresPermissions("enterprise:ps04:entry")
    public Result entry(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps04Service.entry(ids);

        return new Result();
    }

    @GetMapping("info")
    @ApiOperation("人员基础信息")
    @RequiresPermissions("enterprise:ps04:info")
    public Result<Ps04PageDTO> info(@ApiIgnore @RequestParam Map<String, Object> params) {

        Result result = ps04Service.info(params);
        return result;
    }

    @GetMapping("attendanceTJ")
    @ApiOperation("到岗详情")
    @RequiresPermissions("enterprise:ps04:attendanceTJ")
    public Result attendanceTJ(@ApiIgnore @RequestParam Map<String, Object> params) {

        Result result = ps04Service.attendanceTJ(params);
        return result;
    }

    @GetMapping("isKeyJob")
    @ApiOperation("判断岗位是否为关键岗位")
    @RequiresPermissions("enterprise:ps04:isKeyJob")
    public Result isKeyJob(@ApiIgnore @RequestParam Map<String, Object> params) {

        Result result = ps13Service.isKeyJob(params);
        return result;
    }
}