package io.renren.modules.enterprise.ps02face.service;

import io.renren.modules.enterprise.ps02face.entity.Ps02FaceEntity;

import java.util.List;

/**
 * redis操作
 * <AUTHOR> chris
 * @Date : 2023-02-20
 **/
public interface ISaveOrUpdateRedisService {

    /**
     * 保存人脸数据
     * @param projectId 项目id
     * @param ps02Face 人脸数据
     */
    void insertFaceFeature(String projectId, Ps02FaceEntity ps02Face) ;


    /**
     * 从redis中获取人脸库数据
     * @param projectId 项目id
     * @return
     */
    List<Ps02FaceEntity> batchGetFaceFeature(String projectId) ;
}
