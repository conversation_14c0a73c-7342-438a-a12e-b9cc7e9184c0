package io.renren.modules.enterprise.ps06.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps06.dto.BPs06DTO;
import io.renren.modules.enterprise.ps06.excel.BPs06Excel;
import io.renren.modules.enterprise.ps06.service.BPs06Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 工人进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-04-27
 */
@RestController
@RequestMapping("enterprise/bps06")
@Api(tags="工人进退场信息")
public class BPs06Controller {
    @Autowired
    private BPs06Service bPs06Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:bps06:page")
    public Result<PageData<BPs06DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<BPs06DTO> page = bPs06Service.page(params);

        return new Result<PageData<BPs06DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:bps06:info")
    public Result<BPs06DTO> get(@PathVariable("id") Long id){
        BPs06DTO data = bPs06Service.get(id);

        return new Result<BPs06DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:bps06:save")
    public Result save(@RequestBody BPs06DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        bPs06Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:bps06:update")
    public Result update(@RequestBody BPs06DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        bPs06Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:bps06:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        bPs06Service.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:bps06:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<BPs06DTO> list = bPs06Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, BPs06Excel.class);
    }

}