package io.renren.modules.enterprise.pj03.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.admin.sys.entity.DictDTO;
import io.renren.modules.enterprise.pj03.dto.AddExamsDTO;
import io.renren.modules.enterprise.pj03.dto.PagePs02Params;
import io.renren.modules.enterprise.pj03.dto.Pj03DTO;
import io.renren.modules.enterprise.pj03.dto.Pj03DoConfirmDTO;
import io.renren.modules.enterprise.pj03.entity.Pj03Entity;
import io.renren.modules.enterprise.pj05.dto.Pj05DTO;
import io.renren.modules.enterprise.ps02.dto.Ps02PageDTO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 项目培训信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Pj03Service extends CrudService<Pj03Entity, Pj03DTO> {

    /**
     * 新增保存
     * @param dto
     */
    void savePj03Info(Pj03DTO dto);

    /**
     * 修改保存
     * @param dto
     */
    void updatePj03Info(Pj03DTO dto);

    /**
     * 分页查询列表
     * @param params
     * @return
     */
    PageData<Pj03DTO> pageList(Map<String, Object> params);

    List<DictDTO> getTeamList();

    PageData<Ps02PageDTO> pagePs02ByTm0101(PagePs02Params params);

    PageData<Ps02PageDTO> pagePs02(Map<String, Object> params);

    Result addExams(AddExamsDTO dto);

    Result doConfirm(Pj03DoConfirmDTO dto) ;

    Result<List<Pj05DTO>> examInfo(Long pj0301);
}