/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.modules.enterprise.projectledger.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.projectledger.dto.ProjectLedgerDTO;
import io.renren.modules.enterprise.projectledger.entity.ProjectLedgerEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 项目台账
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
public interface ProjectLedgerService extends CrudService<ProjectLedgerEntity,ProjectLedgerDTO> {
    /**
     *分页数据
     **/
    PageData<ProjectLedgerDTO> selectProjectLedgerEntityList(Map<String, Object> params);
    /**
     *导出
     **/

    void excelExp(Map<String, Object> params, HttpServletResponse response) throws Exception ;
}