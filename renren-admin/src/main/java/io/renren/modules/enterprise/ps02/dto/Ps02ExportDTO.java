package io.renren.modules.enterprise.ps02.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 建筑工人花名册
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "建筑工人花名册")
public class Ps02ExportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0201;

    @ApiModelProperty(value = "人员ID")
    private Long ps0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "班组ID")
    private Long tm0101;

    @ApiModelProperty(value = "序号")
    private String sno;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "年龄")
    private String age;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "身份证号")
    private String idcardnumber;

    @ApiModelProperty(value = "工资卡号")
    private String payrollbankcardnumber;

    @ApiModelProperty(value = "开户银行")
    private String payrolltopbankcode;

    @ApiModelProperty(value = "支行名称")
    private String payrollbankname;

    @ApiModelProperty(value = "工资卡支付行号")
    private String payrollno;

    @ApiModelProperty(value = "参建单位")
    private String corpname;

    @ApiModelProperty(value = "班组")
    private String teamname;

    @ApiModelProperty(value = "是否班组长")
    private String isteamleader;

    @ApiModelProperty(value = "工种")
    private String worktypecode;

    @ApiModelProperty(value = "户籍地址")
    private String address;

    @ApiModelProperty(value = "联系电话")
    private String cellphone;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

}