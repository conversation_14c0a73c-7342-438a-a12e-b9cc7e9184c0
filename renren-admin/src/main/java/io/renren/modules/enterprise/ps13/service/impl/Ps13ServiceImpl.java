package io.renren.modules.enterprise.ps13.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps13.dao.Ps13Dao;
import io.renren.modules.enterprise.ps13.dto.Ps13DTO;
import io.renren.modules.enterprise.ps13.entity.Ps13Entity;
import io.renren.modules.enterprise.ps13.service.Ps13Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 关键岗位配置表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-24
 */
@Service
public class Ps13ServiceImpl extends CrudServiceImpl<Ps13Dao, Ps13Entity, Ps13DTO> implements Ps13Service {

    @Override
    public QueryWrapper<Ps13Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps13Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public Result isKeyJob(Map<String, Object> params) {
        Long count = baseDao.countByCp0201AndJobType(params);
        return new Result().ok(count > 0);
    }
}