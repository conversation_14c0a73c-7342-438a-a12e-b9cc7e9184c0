package io.renren.modules.enterprise.pj04.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目培训详情
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ04")
public class Pj04Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long pj0401;
	/**
	 * 培训ID
	 */
	private Long pj0301;
    /**
     * 建筑工人ID
     */
	private Long ps0201;
    /**
     * 是否合格
     */
	private String ispass;
    /**
     * 培训得分(分值0~100，可以保留1位小数)
     */
	private BigDecimal score;
    /**
     * 备注
     */
	private String memo;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}