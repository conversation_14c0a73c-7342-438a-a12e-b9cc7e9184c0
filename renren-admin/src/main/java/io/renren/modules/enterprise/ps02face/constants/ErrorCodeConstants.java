package io.renren.modules.enterprise.ps02face.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> chris
 * @Date : 2023-02-20
 **/
@AllArgsConstructor
@Getter
public enum ErrorCodeConstants {

    OK(200, "操作成功"),
    INIT_FACE_ENGINE_FAILED(1101,"初始化引擎失败"),
    GET_FACETURE_FAILED(1102, "提取人脸特征值失败"),
    CHECK_NO_FACE(1103, "未检测到人脸信息")
    ;
    private Integer code;

    private String desc;
}
