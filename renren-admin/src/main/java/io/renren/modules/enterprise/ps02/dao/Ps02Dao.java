package io.renren.modules.enterprise.ps02.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps02.dto.*;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.supdevicetask.dto.DevicePersonDTO;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;
import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps02Dao extends BaseDao<Ps02Entity> {

    /**
     * 查询分页数据
     *
     * @param params params
     * @return List<Ps02DTO>
     */
    List<Ps02PageDTO> getListData(Map<String, Object> params);

    /**
     * 工人退场
     *
     * @param ids  ps0201
     * @param type 进场或退场
     */
    void updateInOrOutByIds(@Param("list") List<Long> ids, @Param("type") String type);

    /**
     * 查询人员信息
     *
     * @param longs 人员id
     * @return
     */
    List<PersonDTO> selectPersonByIds(List<Long> longs);

    /**
     * 查询人员信息
     *
     * @param ids 人员id
     * @return
     */
    List<PersonDTO> selectDevicePersonByIds(@Param("list") List<Long> ids, @Param("pj0101") Long pj0101);

    /**
     * 查询项目项目的人员是否存在
     *
     * @param pj0101 项目ID
     * @param ps0101 人员ID
     * @return
     */
    Integer selectCountPs0101(@Param("pj0101") Long pj0101, @Param("ps0101") Long ps0101);

    /**
     * 查询工人用工记录信息
     * @param params
     * @return
     */
    List<Ps02EmpRecordDTO> getEmpRecordListData(Map<String, Object> params);

    /**
     *
     * @param ps0201
     * @return
     */
    Ps01Entity getUserInfoByPs0201(String ps0201);

    List<Ps02ExportDTO> exportRosterPage(Map<String, Object> params);

    String selectMemo(Map<String, Object> params);

    void updateTeamleader(Long tm0101);

    Long selectSeqAttInfo();

    void insertAttInfo(Map<String, Object> headImageParams);

    void insertProjectWorkerInfo(Map<String, Object> workerParams);

    void insertProjectWorkerInoutInfo(Map<String, Object> inoutParams);

    /**
     * 工人退场
     *
     * @param ids  ps0201
     */
    void batchExit(@Param("list") List<String> ids);

    /**
     * 获取导入
     *
     * @param idcardnumber
     * @param pj0101
     * @return
     */
    Ps02BatchImportContractDTO getBatchImportContract(@Param("idcardnumber") String idcardnumber, @Param("pj0101") Long pj0101);

    /**
     * 获取工人进退场列表
     *
     * @param params
     * @return
     */
    IPage<Ps02InOrOutDTO> getWorkerInOrOutData(@Param("page")IPage page, @Param("params") Map<String, Object> params);

    /**
     * 获取当前项目工人超龄数量（男大于60，女大于55）
     *
     * @return
     */
    String getWorkerOverageNum(Long pj0101);

    /**
     * 获取超龄工人列表
     *
     * @param page
     * @param params
     * @return
     */
    IPage<Ps02WorkerOverageDTO> getWorkerOveragePage(@Param("page")IPage<Ps02Entity> page, @Param("params") Map<String, Object> params);

    /**
     * 获取工人信息
     *
     * @param idcardnumber
     * @return
     */
    Ps02Entity getByIdcardnumberAndName(@Param("idcardnumber") String idcardnumber, @Param("name") String name);

}