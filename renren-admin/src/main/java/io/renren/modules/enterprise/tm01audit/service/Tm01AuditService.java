package io.renren.modules.enterprise.tm01audit.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.tm01audit.dto.Tm01AuditDTO;
import io.renren.modules.enterprise.tm01audit.dto.Tm01DetailAuditDTO;
import io.renren.modules.enterprise.tm01audit.dto.Tm01PageAuditDTO;
import io.renren.modules.enterprise.tm01audit.entity.Tm01AuditEntity;

import java.util.Map;

/**
 * 分包班组审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
public interface Tm01AuditService extends CrudService<Tm01AuditEntity, Tm01AuditDTO> {


    /**
     * 分页
     * @param params
     * @return
     */
    PageData<Tm01PageAuditDTO> getPageList(Map<String, Object> params);


    /**
     * 详情
     * @param id
     * @return
     */
    Tm01DetailAuditDTO getInfo(Long id) ;
}