package io.renren.modules.enterprise.cp04audit.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.cp04audit.dto.Cp04AuditPageDTO;
import io.renren.modules.enterprise.cp04audit.entity.Cp04AuditEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 分包信息审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-03
 */
@Mapper
public interface Cp04AuditDao extends BaseDao<Cp04AuditEntity> {


    /**
     * 分页
     * @param params
     * @return
     */
    List<Cp04AuditPageDTO> pageList(Map<String, Object> params);
}