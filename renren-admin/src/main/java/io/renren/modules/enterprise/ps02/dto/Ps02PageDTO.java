package io.renren.modules.enterprise.ps02.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-7-8 08:37:20
 * 工人列表
 */
@Data
@ApiModel(value = "建筑工人分页列表信息")
public class Ps02PageDTO {

    @ApiModelProperty(value = "主键ID")
    private Long ps0201;

    @ApiModelProperty(value = "合同ID")
    private Long ps0801;

    @ApiModelProperty(value = "所属项目")
    private String projectName;

    @ApiModelProperty(value = "所属项目id")
    private Long pj0101;

    @ApiModelProperty(value = "所属企业")
    private String corpName;

    @ApiModelProperty(value = "所属班组")
    private String teamname;

    @ApiModelProperty(value = "姓名")
    private String personName;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idCardNumber;

    @ApiModelProperty(value = "是否班组长")
    private String isTeamLeader;

    @ApiModelProperty(value = "手机号码")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String cellPhone;

    @ApiModelProperty(value = "工种")
    private String workTypeCode;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "是否签订合同")
    private String issigned;

    @ApiModelProperty(value = "是否上传合同")
    private String isupload;

    @ApiModelProperty(value = "承诺书是否上传（0否，1是）")
    private String promiseIsUpload;

    @ApiModelProperty(value = "最近考勤时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkdate;

    @ApiModelProperty(value = "年龄")
    private String personage;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "是否上传特殊工种证件")
    private String workerFileIsUpload;
}
