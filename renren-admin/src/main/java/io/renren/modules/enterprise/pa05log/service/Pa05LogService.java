package io.renren.modules.enterprise.pa05log.service;

import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.pa05log.dto.Pa05LogDTO;
import io.renren.modules.enterprise.pa05log.dto.Pa05TimeLineDTO;
import io.renren.modules.enterprise.pa05log.entity.Pa05LogEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * 工资单操作日志
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-03
 */
public interface Pa05LogService extends CrudService<Pa05LogEntity, Pa05LogDTO> {


    /**
     * 保存日志
     * @param dto
     */
    @Async
    void saveLog(Pa05LogDTO dto) ;

    /**
     * 保存日志
     * @param pa0501
     * @param operation
     */
    @Async
    void saveLog(Long pa0501, String operation) ;


    /**
     * 记录分页
     * @param params
     * @return
     */
    Result<List<Pa05TimeLineDTO>> record(Map<String, Object> params) ;
}