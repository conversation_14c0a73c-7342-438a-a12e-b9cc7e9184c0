package io.renren.modules.enterprise.pa05log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.pa05log.dao.Pa05LogDao;
import io.renren.modules.enterprise.pa05log.dto.Pa05LogDTO;
import io.renren.modules.enterprise.pa05log.dto.Pa05TimeLineDTO;
import io.renren.modules.enterprise.pa05log.entity.Pa05LogEntity;
import io.renren.modules.enterprise.pa05log.service.Pa05LogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工资单操作日志
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-01-03
 */
@Service
public class Pa05LogServiceImpl extends CrudServiceImpl<Pa05LogDao, Pa05LogEntity, Pa05LogDTO> implements Pa05LogService {

    @Override
    public QueryWrapper<Pa05LogEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pa05LogEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public void saveLog(Pa05LogDTO dto) {
        save(dto);
    }

    /**
     * 保存日志
     * @param pa0501
     * @param operation
     */
    public void saveLog(Long pa0501, String operation) {
        Pa05LogDTO pa05Log = new Pa05LogDTO();
        pa05Log.setPa0501(pa0501);
        pa05Log.setOperation(operation);
        pa05Log.setOperatdate(new Date());
        pa05Log.setOperator(SecurityUser.getUser().getUsername());
        save(pa05Log);
    }


    @Override
    public Result<List<Pa05TimeLineDTO>> record(Map<String, Object> params) {

        return new Result<List<Pa05TimeLineDTO>>().ok(baseDao.record(params));
    }
}