package io.renren.modules.enterprise.cp02audit.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 参建单位审核表
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-03
 */
@Data
@ApiModel(value = "参建单位审核表")
public class Cp02AuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long cp0201;
    @ApiModelProperty(value = "企业ID")
    private Long cp0101;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "参建类型")
    private String corptype;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核人")
    private String auditor;
    @ApiModelProperty(value = "审核时间")
    private Date auditdate;
    @ApiModelProperty(value = "审核结果")
    private String auditresult;

}