package io.renren.modules.enterprise.pw02.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.Result;
import io.renren.modules.admin.sys.dto.SysDictDataDTO;
import io.renren.modules.admin.sys.service.SysDictDataService;
import io.renren.modules.enterprise.ps01.dao.Ps01Dao;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.pw01.dao.Pw01Dao;
import io.renren.modules.enterprise.pw01.dto.Pw01DTO;
import io.renren.modules.enterprise.pw01.entity.Pw01Entity;
import io.renren.modules.enterprise.pw02.dao.Pw02Dao;
import io.renren.modules.enterprise.pw02.dto.Pw02DTO;
import io.renren.modules.enterprise.pw02.entity.Pw02Entity;
import io.renren.modules.enterprise.pw02.excel.Pw02Excel;
import io.renren.modules.enterprise.pw02.excel.Pw02ImportExcel;
import io.renren.modules.enterprise.pw02.service.Pw02Service;
import io.renren.modules.enterprise.tjattendanceinfo.dto.TjAttendanceGenerateDataDTO;
import io.renren.modules.enterprise.tjattendanceinfo.service.TjAttendanceInfoService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 工资导入明细表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-04
 */
@Service
public class Pw02ServiceImpl extends CrudServiceImpl<Pw02Dao, Pw02Entity, Pw02DTO> implements Pw02Service {
    @Autowired
    private Pw01Dao pw01Dao;
    @Autowired
    private Ps02Dao ps02Dao;
    @Autowired
    private TjAttendanceInfoService tjAttendanceInfoService;
    @Autowired
    private SysDictDataService sysDictDataService;

    @Value("${expFile.pw02ImpUrl}")
    private String pw02ImpUrl;

    @Override
    public QueryWrapper<Pw02Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pw02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public List<Pw02DTO> getList(Map<String, Object> params) {
        params.put("pw0101", Long.parseLong((String) params.get("pw0101")));
        return baseDao.getList(params);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Result importSalary(MultipartFile file, Map<String, Object> params) {
        long month = Long.parseLong(((String) params.get("month")).replace("-", ""));
        Result result = new Result();
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        /*使用easypoi获取文件数据*/
        ImportParams param = new ImportParams();
        param.setTitleRows(0);
        param.setHeadRows(1);
        param.setSheetNum(1);

        // 开启Excel校验
        param.setNeedVerify(true);
        /*获取excel表格中数据，并封装为一个结果对象*/
        ExcelImportResult<Pw02ImportExcel> results;
        //获取excel表格中数据
        List<Pw02ImportExcel> salary = new ArrayList<>();
        List<Pw02ImportExcel> failList = new ArrayList<>();
        try {
            results = ExcelImportUtil.importExcelMore(file.getInputStream(), Pw02ImportExcel.class, param);
            //获取excel表格中数据
            salary = results.getList();
            failList = results.getFailList();
            failList.removeIf(Pw02ImportExcel -> Pw02ImportExcel.getRowNum() == null || Pw02ImportExcel.getName() == null);
            if (results.getList().size() <= 0 && results.getFailList().size() <= 0) {
                throw new RenException("读取到的应发条数为0，请确文档表头是否正确！");
            }
            if (results.getList().size() <= 0 && results.getFailList().size() > 0) {
                throw new RenException("读取成功应发条数为0，读取失败条数为"+results.getFailList().size());
            }
        } catch (Exception e) {
            throw new RenException("文档数据填写有误，请确认填写是否正确！");
        }
        // 校验是否存在重复的人员导入
        Map<String, List<Pw02ImportExcel>> collect = salary.stream()
                .collect(Collectors.groupingBy(Pw02ImportExcel::getIdcardnumber));

        Map<String, List<Pw02ImportExcel>> duplicates = collect.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (!CollectionUtil.isEmpty(duplicates)) {
            StringJoiner errorMsg = new StringJoiner(",");
            duplicates.forEach((idcardnumber, pw02) -> {
                errorMsg.add(idcardnumber);
            });
            throw new RenException("上传的工资表里存在重复身份证，请检查并更正后重新上传！重复身份证：" + errorMsg);
        }
        //不能导入系统的工人
        if (failList.size() > 0) {
            StringBuilder errPerson = new StringBuilder();
            for (int i = 0; i < failList.size(); i++) {
                errPerson.append(failList.get(i).getName()).append(failList.get(i).getErrorMsg());
            }
            result.setCode(500);
            result.setMsg(failList.size() + "个工人存在错误填写：" + errPerson + "请核对后重新提交");
            return result;
        }

        Pw01Entity pw01Entity = pw01Dao.selectOne(new QueryWrapper<Pw01Entity>()
                .eq("pj0101", pj0101)
                .eq("month", month));

        // 生成项目月份考勤统计数据
        TjAttendanceGenerateDataDTO tjAttendanceGenerateDataDTO = new TjAttendanceGenerateDataDTO();
        tjAttendanceGenerateDataDTO.setTjMonth((String) params.get("month"));
        tjAttendanceInfoService.generateTjAttendanceData(tjAttendanceGenerateDataDTO);

        List<Pw02Entity> pw02List = new ArrayList<>();
        double sfgzze = 0.00;
        double yfgzze = 0.00;
        for (Pw02ImportExcel pw02ImportExcel : salary) {
            pw02ImportExcel.setIdcardnumber(StringUtils.removePattern(pw02ImportExcel.getIdcardnumber() ,"[^a-zA-Z0-9]"));
            // 验证工人身份证信息然后插入数据
            boolean validCard = IdcardUtil.isValidCard(pw02ImportExcel.getIdcardnumber());
            if(!validCard){
                throw new RenException("工人["+ pw02ImportExcel.getName() + "]身份证号码信息不正确！");
            }
            Pw02Entity pw02Entity = BeanUtil.copyProperties(pw02ImportExcel, Pw02Entity.class);
            pw02Entity.setPj0101(pj0101);
            pw02Entity.setMonth(month);
            // 判断卡号是否填写正确
            pw02ImportExcel.setKh(pw02ImportExcel.getKh().replace(" ",""));
            Pattern pattern = Pattern.compile("^\\d{8,22}$");
            boolean matches = pattern.matcher(pw02ImportExcel.getKh()).matches();
            if (!matches){
                throw new RenException("上传的工资表里存在银行卡号填写错误，请检查并更正后重新上传！工人："
                        + pw02ImportExcel.getName());
            }
            // 判断开户行代码是否填写正确
            SysDictDataDTO dict = sysDictDataService.getByDictTypeAndValue("PAY_BANK_CODE", pw02ImportExcel.getKhh());
            if (Objects.isNull(dict)) {
                throw new RenException("上传的工资表里存在开户行代码填写错误，请检查并更正后重新上传！工人："
                        + pw02ImportExcel.getName());
            }
            if (!Objects.isNull(pw01Entity)) {
                if ("1".equals(pw01Entity.getAuditstatus()) || "0".equals(pw01Entity.getAuditstatus())) {
                    throw new RenException("该工资年月已上传工资单且提交，请勿重复上传工资单");
                }
                //删除当前工资单数据
                baseDao.delete(new QueryWrapper<Pw02Entity>()
                        .eq("pw0101", pw01Entity.getPw0101()));
            } else {
                Pw01Entity pw01 = new Pw01Entity();
                pw01.setPj0101(pj0101);
                pw01.setMonth(month);
                pw01Dao.insert(pw01);
                pw01Entity = pw01;
            }
            pw02Entity.setPw0101(pw01Entity.getPw0101());
            // 判断人员是否为补录人员
            Ps02Entity ps02 = ps02Dao.getByIdcardnumberAndName(pw02ImportExcel.getIdcardnumber(), pw02ImportExcel.getName());
            if (Objects.isNull(ps02)) {
                pw02Entity.setImportstatus("1");
                pw02Entity.setXtkqts(0);
            } else {
                params.put("idcardnumber", pw02Entity.getIdcardnumber());
                params.put("tjMonth", params.get("month"));
                params.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
                pw02Entity.setXtkqts(tjAttendanceInfoService.getKqdaysByIdcardnumber(params));
                pw02Entity.setImportstatus(getImportStatus(pw02Entity.getKqts(), pw02Entity.getXtkqts()));
            }
            pw02List.add(pw02Entity);
            sfgzze += pw02Entity.getSfgz();
            yfgzze += pw02Entity.getYfgz();
        }

        baseDao.batchInsert(pw02List);
        pw01Entity.setFfzrs((long) pw02List.size());
        pw01Entity.setYfgz(yfgzze);
        pw01Entity.setSfgz(sfgzze);
        pw01Dao.updateById(pw01Entity);
        return result;
    }

    @Override
    public void download(HttpServletResponse response, String month) {
        try {
            // 1. 加载模板配置
            TemplateExportParams params = new TemplateExportParams(
                    pw02ImpUrl,  // 模板路径（无需写 resources/）
                    true   // 是否开启横向循环（如需要多行数据循环）
            );
            List<Pw02ImportExcel> list = new ArrayList<>();
            String fileName = null;
            if (!Objects.isNull(month)) {
                long tjMonth = Long.parseLong(month.replace("-", ""));
                list = baseDao.listByPj0101AndMonth(CommonUtils.userProjectInfo().getPj0101(), tjMonth);

                // 生成项目月份考勤统计数据
                Map<String, Object> map = new HashMap<>();
                map.put("pj0101", CommonUtils.userProjectInfo().getPj0101());
                map.put("tjMonth", month);
//                tjAttendanceInfoService.deleteTjAttendanceData(map);
                TjAttendanceGenerateDataDTO tjAttendanceGenerateDataDTO = new TjAttendanceGenerateDataDTO();
                tjAttendanceGenerateDataDTO.setTjMonth(month);
                tjAttendanceInfoService.generateTjAttendanceData(tjAttendanceGenerateDataDTO);
                for (Pw02ImportExcel pw02ImportExcel : list) {
                    map.put("idcardnumber", pw02ImportExcel.getIdcardnumber());
                    pw02ImportExcel.setXtkqts(tjAttendanceInfoService.getKqdaysByIdcardnumber(map));
                }
                fileName = tjMonth + "工资单.xlsx";
            } else {
                fileName = "工资单.xlsx";
            }
            // 2. 准备数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("list", list);  // 对应模板中的 {{#fe:list}}

            // 3. 生成 Excel 文件
            Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);


            // 4. 设置响应头，触发下载
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public void export(HttpServletResponse response, Long pw0101) {
        try {
            // 1. 加载模板配置
            TemplateExportParams params = new TemplateExportParams(
                    pw02ImpUrl,  // 模板路径（无需写 resources/）
                    true   // 是否开启横向循环（如需要多行数据循环）
            );
            List<Pw02ImportExcel> list = baseDao.getExportList(pw0101);
            // 2. 准备数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("list", list);  // 对应模板中的 {{#fe:list}}

            // 3. 生成 Excel 文件
            Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);
            Pw01Entity pw01Entity = pw01Dao.selectById(pw0101);
            String fileName = pw01Entity.getMonth() + "工资单.xlsx";

            // 4. 设置响应头，触发下载
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public double getSfgzByPw0101(Long pw0101) {
        return baseDao.getSfgzByPw0101(pw0101);
    }

    @Override
    public void updateAuditStatusByPw0101(Long pw0101) {
        baseDao.updateAuditStatusByPw0101(pw0101);
    }

    private String getImportStatus(Integer kqts, Integer xtkqts) {
        if (Objects.equals(kqts, xtkqts)) {
            return "0";
        } else {
            return "2";
        }
    }
}