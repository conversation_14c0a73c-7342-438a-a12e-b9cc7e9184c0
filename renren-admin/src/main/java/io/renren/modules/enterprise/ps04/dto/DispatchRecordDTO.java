package io.renren.modules.enterprise.ps04.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DispatchRecordDTO {

    @ApiModelProperty(value = "主键ID")
    private Long ps0401;
    @ApiModelProperty(value = "人员ID")
    private Long ps0301;
    @ApiModelProperty(value = "所属项目")
    private String projectName;
    @ApiModelProperty(value = "审核状态")
    private String auditstatus;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "进场时间")
    private Date entrytime;
    @ApiModelProperty(value = "岗位类型")
    private String jobtype;
}
