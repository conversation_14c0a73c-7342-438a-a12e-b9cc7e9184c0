/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.modules.enterprise.projectledger.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.projectledger.entity.ProjectLedgerEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目台账
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
@Mapper
public interface ProjectLedgerDao extends BaseDao<ProjectLedgerEntity> {

   List<ProjectLedgerEntity> selectProjectLedgerEntityList(Map<String, Object> params);

   String selectLevelByAreacode(String areacode);
}
