package io.renren.modules.enterprise.ps03audit.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @since 1.0.0 2022-12-28
 */
@Data
@ApiModel(value = "${comments}")
public class Ps03AuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long ps0301;
    @ApiModelProperty(value = "当前项目id")
    private Long pj0101;
    @ApiModelProperty(value = "在职企业id")
    private Long cp0101;
    @ApiModelProperty(value = "人员id")
    private Long ps0101;
    @ApiModelProperty(value = "在职状态")
    private String managestatus;
    @ApiModelProperty(value = "头像采集照片")
    private String photo;
    @ApiModelProperty(value = "是否购买工伤或意外伤害保险")
    private String hasbuyinsurance;
    @ApiModelProperty(value = "入职时间")
    private Date inductiontime;
    @ApiModelProperty(value = "离职时间")
    private Date departuretime;
    @ApiModelProperty(value = "审核状态")
    private String auditstatus;
    @ApiModelProperty(value = "审核人")
    private Long auditor;
    @ApiModelProperty(value = "审核时间")
    private Date auditdate;
    @ApiModelProperty(value = "审核结果")
    private String auditresult;

}