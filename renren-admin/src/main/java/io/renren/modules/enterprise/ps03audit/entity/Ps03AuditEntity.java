package io.renren.modules.enterprise.ps03audit.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-12-28
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS03_AUDIT")
public class Ps03AuditEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
	private Long ps0301;
    /**
     * 当前项目id
     */
	private Long pj0101;
    /**
     * 在职企业id
     */
	private Long cp0101;
    /**
     * 人员id
     */
	private Long ps0101;
    /**
     * 在职状态
     */
	private String managestatus;
    /**
     * 头像采集照片
     */
	private String photo;
    /**
     * 是否购买工伤或意外伤害保险
     */
	private String hasbuyinsurance;
    /**
     * 入职时间
     */
	private Date inductiontime;
    /**
     * 离职时间
     */
	private Date departuretime;
    /**
     * 审核状态
     */
	private String auditstatus;
    /**
     * 审核人
     */
	private Long auditor;
    /**
     * 审核时间
     */
	private Date auditdate;
    /**
     * 审核结果
     */
	private String auditresult;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}