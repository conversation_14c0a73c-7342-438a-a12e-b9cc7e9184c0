package io.renren.modules.enterprise.ps03.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.common.service.CommonService;
import io.renren.common.exception.RenException;
import io.renren.common.file.ImageUtil;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.Result;
import io.renren.modules.admin.sys.dto.SysDictDataDTO;
import io.renren.modules.admin.sys.service.SysDictDataService;
import io.renren.modules.enterprise.kq05.service.Kq05Service;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.ps01.dao.Ps01Dao;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import io.renren.modules.enterprise.ps03.dao.Ps03Dao;
import io.renren.modules.enterprise.ps03.dto.EnterPersonDTO;
import io.renren.modules.enterprise.ps03.dto.ProjectInfoDTO;
import io.renren.modules.enterprise.ps03.dto.Ps03DTO;
import io.renren.modules.enterprise.ps03.dto.Ps03PageDTO;
import io.renren.modules.enterprise.ps03.entity.Ps03Entity;
import io.renren.modules.enterprise.ps03.service.Ps03Service;
import io.renren.modules.enterprise.ps04.dao.Ps04Dao;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.enterprise.ps07.dto.Ps07DTO;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 管理人员在职信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
@Service
public class Ps03ServiceImpl extends CrudServiceImpl<Ps03Dao, Ps03Entity, Ps03DTO> implements Ps03Service {
    private final static String IMAGE = "data:image";
    private final static String MANAGER = "2";
    @Autowired
    private CommonService commonService;
    @Autowired
    private SysDictDataService sysDictDataService;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Kq05Service kq05Service;
    @Autowired
    private Ps03Dao ps03Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Ps01Dao ps01Dao;
    @Autowired
    private Pj01Dao pj01Dao;

    @Override
    public QueryWrapper<Ps03Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps03Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ps03PageDTO> Ps03page(Map<String, Object> params) {
        paramsToLike(params, "name");
        //params.put("deptId", SecurityUser.getDeptId());
        params.put("cp0101", commonService.getUserCp0101());
        IPage<Ps03Entity> page = getPage(params, "", false);
        List<Ps03PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Ps03PageDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(Ps03DTO dto) {
        //保存或者修改PS01信息
        Ps01DTO ps01DTO = dto.getPs01DTO();
        int genderByIdCard = IdcardUtil.getGenderByIdCard(ps01DTO.getIdcardnumber());
        ps01DTO.setGender(genderByIdCard > 0 ? "1" : "2");
        ps01DTO.setWorkertype(MANAGER);
        if (StringUtils.isNotBlank(dto.getPhoto())) {
            dto.setPhoto(ImageUtil.base64ToImage(dto.getPhoto()));
        }
        // 如果是base64 文件则处理
        if (StrUtil.startWith(Objects.requireNonNull(ps01DTO).getHeadimageurl(), IMAGE)) {
            ps01DTO.setHeadimageurl(ImageUtil.base64ToImage(ps01DTO.getHeadimageurl()));
        }
        // 判断 ps01 中是否有人员信息
        Ps01Entity oldPs01 = ps01Dao.loadPs01(dto.getPs01DTO().getIdcardnumber());
        if(oldPs01 != null) {
            // 判断该人员是否在职
            if(isEmployed(oldPs01.getPs0101())) {
                throw new RenException("该人员处于在职状态");
            }
            // 判断该人员是否已存在
            if(isPresent(oldPs01.getPs0101(), commonService.getUserCp0101())) {
                throw new RenException("该人员已加入企业");
            }
            ps01DTO.setPs0101(oldPs01.getPs0101());
            ps01Dao.updateById(BeanUtil.copyProperties(ps01DTO, Ps01Entity.class));
            dto.setPs0101(oldPs01.getPs0101());
        } else {
            Ps01Entity ps01Entity = BeanUtil.copyProperties(ps01DTO, Ps01Entity.class);
            ps01Dao.insert(ps01Entity);
            dto.setPs0101(ps01Entity.getPs0101());
        }
        //保存 PS03 信息
        dto.setCp0101(commonService.getUserCp0101());
        dto.setInductiontime(DateUtil.parse(DateUtil.today()));
        dto.setManagestatus("1");
        Ps03Entity ps03Entity = BeanUtil.copyProperties(dto, Ps03Entity.class);
        ps03Dao.insert(ps03Entity);
        // 关联附件
        ot01Service.updateBusily(ps03Entity.getPs0301(), dto.getOt0101s().stream().map(Ot01DTO::getOt0101).collect(Collectors.toList()));
        //退场记录表增加数据
        /*Ps07DTO ps07DTO = new Ps07DTO();
        ps07DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
        ps07DTO.setInOrOut("1");
        ps07DTO.setPs0401(dto.getPs0401());
        ps07Service.save(ps07DTO);
        //查询当前用户下边的设备
        //人员注册、下发到设备
        kq05Service.saveCreatePerson(CommonUtils.userProjectInfo().getPj0101(), dto.getPs0401(), ps01Entity.getName(), dto.getPhoto(), PERSON_TYPE);*/
    }


    @Override
    public Ps03DTO getPs03(Long id) {
        Ps03DTO ps03DTO = get(id);
        Ps01Entity ps01Entity = ps01Dao.selectOne(new LambdaQueryWrapper<Ps01Entity>()
                .eq(Ps01Entity::getPs0101, ps03DTO.getPs0101()));
        ps03DTO.setPs01DTO(BeanUtil.copyProperties(ps01Entity, Ps01DTO.class));
        // 获取附件
        ps03DTO.setCertificateFiles(ot01Service.loadBusinessData(id, "50"));
        ps03DTO.setInsuranceFiles(ot01Service.loadBusinessData(id, "51"));

        return ps03DTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result enterPerson(EnterPersonDTO dto) {
        // 更新 ps03
        Ps03Entity ps03Entity = ps03Dao.selectById(dto.getPs0301());
        ps03Entity.setPj0101(dto.getPj0101());
        ps03Dao.updateById(ps03Entity);
        // 插入 ps04
        Ps04Entity ps04Entity = BeanUtil.copyProperties(dto, Ps04Entity.class);
        ps04Entity.setEntrytime(DateUtil.parse(DateUtil.today()));
        ps04Entity.setInOrOut("1");
        ps04Dao.insert(ps04Entity);
        // 添加考勤设备
        kq05Service.addDevicePerson(dto.getPj0101(), ps04Entity.getPs0401(), "2");

        return new Result();
    }

    @Override
    public Result<List<ProjectInfoDTO>> getProjectInfo() {

        List<ProjectInfoDTO> list = pj01Dao.getProjectInfo(commonService.getUserCp0101());
        return new Result().ok(list);
    }

    @Override
    public Result<List<SysDictDataDTO>> getJobType() {
        return null;
    }

    private boolean isPresent(Long ps0101, Long cp0101) {
        return ps03Dao.selectCount(new QueryWrapper<Ps03Entity>()
                .eq("ps0101", ps0101)
                .eq("cp0101", cp0101)
                .eq("managestatus", "1")) > 0;
    }

    private boolean isEmployed(Long ps0101) {
        List<String> list = ps03Dao.selectList(new QueryWrapper<Ps03Entity>()
                        .select("managestatus")
                        .eq("ps0101", ps0101)).stream()
                .map(Ps03Entity::getManagestatus).collect(Collectors.toList());

        return list.contains("1");
    }

}