package io.renren.modules.enterprise.ps02facedata.vo;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

/**
 * 人脸数据入参
 * <AUTHOR>
 * @date 2023/2/18
 */
@Data
@Validated
public class FaceRecoResVO {

    /**
     * 查询人脸库（可根据projectId来设置）
     */
    @NotBlank(message = "人脸库不能为空")
    private String projectId;

    /**
     * 人像图片 base64
     */
    @NotBlank(message = "人像图片不能为空")
    private String image;
}
