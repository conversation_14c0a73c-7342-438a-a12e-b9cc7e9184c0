package io.renren.modules.enterprise.ps03.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.admin.sys.dto.SysDictDataDTO;
import io.renren.modules.enterprise.ps03.dto.EnterPersonDTO;
import io.renren.modules.enterprise.ps03.dto.ProjectInfoDTO;
import io.renren.modules.enterprise.ps03.dto.Ps03DTO;
import io.renren.modules.enterprise.ps03.dto.Ps03PageDTO;
import io.renren.modules.enterprise.ps03.entity.Ps03Entity;

import java.util.List;
import java.util.Map;

/**
 * 管理人员在职信息表
 *
 * <AUTHOR> 
 * @since 1.0.0 2022-11-19
 */
public interface Ps03Service extends CrudService<Ps03Entity, Ps03DTO> {

    PageData<Ps03PageDTO> Ps03page(Map<String, Object> params);

    @Override
    void save(Ps03DTO dto);

    Ps03DTO getPs03(Long id);

    Result enterPerson(EnterPersonDTO dto);

    Result<List<ProjectInfoDTO>> getProjectInfo();

    Result<List<SysDictDataDTO>> getJobType();
}