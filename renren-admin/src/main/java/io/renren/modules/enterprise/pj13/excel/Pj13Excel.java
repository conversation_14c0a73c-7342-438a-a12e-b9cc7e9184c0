package io.renren.modules.enterprise.pj13.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@Data
public class Pj13Excel {
    @Excel(name = "主键")
    private BigDecimal id;
    @Excel(name = "退场业务id（工人id、管理人员id、班组id）")
    private BigDecimal exitId;
    @Excel(name = "退场时间")
    private Date exittime;
    @Excel(name = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @Excel(name = "审核人")
    private String auditor;
    @Excel(name = "审核时间")
    private Date auditdate;
    @Excel(name = "审核原因")
    private String auditreason;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;
    @Excel(name = "退场类型（1：工人退场、2：管理人员退场、3：班组退场）")
    private String exitType;

}