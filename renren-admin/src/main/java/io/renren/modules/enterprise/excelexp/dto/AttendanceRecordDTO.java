package io.renren.modules.enterprise.excelexp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * DO 考勤记录表对象
 *
 * @className: WorkerRecordDTO
 * @author: lrl
 * @date: 2021-05-18 10:36
 **/
@Data
@ApiModel(value = "考勤记录表对象")
public class AttendanceRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "班组名称")
    private String teamname;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String idcardnumber;

    @ApiModelProperty(value = "月第一天")
    private String day1;

    private String day2;

    private String day3;

    private String day4;

    private String day5;

    private String day6;

    private String day7;

    private String day8;

    private String day9;

    private String day10;

    private String day11;

    private String day12;

    private String day13;

    private String day14;

    private String day15;

    private String day16;

    private String day17;

    private String day18;

    private String day19;

    private String day20;

    private String day21;

    private String day22;

    private String day23;

    private String day24;

    private String day25;

    private String day26;

    private String day27;

    private String day28;

    private String day29;

    private String day30;

    private String day31;

    private String counts;

}
