package io.renren.modules.enterprise.ps13.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 关键岗位配置表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-24
 */
@Data
public class Ps13Excel {
    @Excel(name = "主键ID")
    private BigDecimal ps1301;
    @Excel(name = "岗位类型")
    private String jobtype;
    @Excel(name = "参建类型")
    private String corptype;
    @Excel(name = "岗位类型名称")
    private String jobtypename;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}