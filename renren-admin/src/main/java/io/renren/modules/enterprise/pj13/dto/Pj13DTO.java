package io.renren.modules.enterprise.pj13.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@Data
@ApiModel(value = "退场审核表")
public class Pj13DTO implements Serializable {
    private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "主键")
	private Long id;
		@ApiModelProperty(value = "退场业务id（工人id、管理人员id、班组id）")
	private Long exitId;
		@ApiModelProperty(value = "退场时间")
	private Date exittime;
	@ApiModelProperty(value = "进场时间")
	private Date entrytime;
		@ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
	private String auditstatus;
		@ApiModelProperty(value = "审核人")
	private String auditor;
		@ApiModelProperty(value = "审核时间")
	private Date auditdate;
		@ApiModelProperty(value = "审核原因")
	private String auditreason;
		@ApiModelProperty(value = "退场类型（1：工人退场、2：管理人员退场、3：班组退场）")
	private String exitType;
	@ApiModelProperty(value = "申请时间")
	private Date applydate;

}