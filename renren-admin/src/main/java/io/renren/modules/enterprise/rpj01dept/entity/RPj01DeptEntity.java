package io.renren.modules.enterprise.rpj01dept.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目和机构的关系表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-11
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("R_PJ01_DEPT")
public class RPj01DeptEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	private Long id;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 机构ID
     */
	private Long deptId;

}