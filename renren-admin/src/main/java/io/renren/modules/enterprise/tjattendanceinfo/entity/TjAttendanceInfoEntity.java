package io.renren.modules.enterprise.tjattendanceinfo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤统计表详情
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-05-14
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_TJ_ATTENDANCE_INFO")
public class TjAttendanceInfoEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	private BigDecimal id;
    /**
     * 项目ID
     */
	private BigDecimal pj0101;
    /**
     * 班组ID
     */
	private BigDecimal tm0101;
    /**
     * 人员ID
     */
	private BigDecimal userId;
    /**
     * 人员类型
     */
	private String personType;
    /**
     * 人员姓名
     */
	private String workername;
    /**
     * 岗位
     */
	private String jobtype;
    /**
     * 当前进退场状态
     */
	private String inOrOut;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 考勤月份
     */
	private String kqmonth;
    /**
     * 考勤天数
     */
	private BigDecimal kqdays;
    /**
     * 1号
     */
	private String day1;
    /**
     * 2号
     */
	private String day2;
    /**
     * 3号
     */
	private String day3;
    /**
     * 4号
     */
	private String day4;
    /**
     * 5号
     */
	private String day5;
    /**
     * 6号
     */
	private String day6;
    /**
     * 7号
     */
	private String day7;
    /**
     * 8号
     */
	private String day8;
    /**
     * 9号
     */
	private String day9;
    /**
     * 10号
     */
	private String day10;
    /**
     * 11号
     */
	private String day11;
    /**
     * 12号
     */
	private String day12;
    /**
     * 13号
     */
	private String day13;
    /**
     * 14号
     */
	private String day14;
    /**
     * 15号
     */
	private String day15;
    /**
     * 16号
     */
	private String day16;
    /**
     * 17号
     */
	private String day17;
    /**
     * 18号
     */
	private String day18;
    /**
     * 19号
     */
	private String day19;
    /**
     * 20号
     */
	private String day20;
    /**
     * 21号
     */
	private String day21;
    /**
     * 22号
     */
	private String day22;
    /**
     * 23号
     */
	private String day23;
    /**
     * 24号
     */
	private String day24;
    /**
     * 25号
     */
	private String day25;
    /**
     * 26号
     */
	private String day26;
    /**
     * 27号
     */
	private String day27;
    /**
     * 28号
     */
	private String day28;
    /**
     * 29号
     */
	private String day29;
    /**
     * 30号
     */
	private String day30;
    /**
     * 31号
     */
	private String day31;
    /**
     * 统计时间
     */
	private Date tjdate;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 参建企业
     */
	private BigDecimal cp0101;
    /**
     * 证件号码
     */
	private String idcardnumber;
    /**
     * 合计工时
     */
	private String kqhours;
    /**
     * 身份证住址
     */
	private String address;
    /**
     * 联系电话
     */
	private String cellphone;
    /**
     * 紧急联系电话
     */
	private String urgentlinkmanphone;
    /**
     * 工资卡开户行名称
     */
	private String payrollbankname;
    /**
     * 工资卡帐号
     */
	private String payrollbankcardnumber;
}