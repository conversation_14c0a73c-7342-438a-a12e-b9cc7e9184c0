package io.renren.modules.enterprise.pj05.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.pj05.dto.Pj05DTO;
import io.renren.modules.enterprise.pj05.excel.Pj05Excel;
import io.renren.modules.enterprise.pj05.service.Pj05Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 项目竣工验收信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("enterprise/pj05")
@Api(tags="项目竣工验收信息表")
public class Pj05Controller {
    @Autowired
    private Pj05Service pj05Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:pj05:page")
    public Result<PageData<Pj05DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Pj05DTO> page = pj05Service.page(params);

        return new Result<PageData<Pj05DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pj05:info")
    public Result<Pj05DTO> get(@PathVariable("id") Long id){
        Pj05DTO data = pj05Service.get(id);

        return new Result<Pj05DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:pj05:save")
    public Result save(@RequestBody Pj05DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj05Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:pj05:update")
    public Result update(@RequestBody Pj05DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj05Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pj05:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj05Service.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:pj05:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<Pj05DTO> list = pj05Service.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, Pj05Excel.class);
    }

}