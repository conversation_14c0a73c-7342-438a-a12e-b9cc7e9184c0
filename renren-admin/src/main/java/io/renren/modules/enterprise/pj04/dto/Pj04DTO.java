package io.renren.modules.enterprise.pj04.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 项目培训详情
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "项目培训详情")
public class Pj04DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pj0401;

    @ApiModelProperty(value = "项目培训ID")
    private Long pj0301;

    @ApiModelProperty(value = "建筑工人ID")
    private Long ps0201;

    @ApiModelProperty(value = "是否合格")
    private String ispass;

    @ApiModelProperty(value = "培训得分(分值0~100，可以保留1位小数)")
    private BigDecimal score;

    @ApiModelProperty(value = "备注")
    private String memo;

}