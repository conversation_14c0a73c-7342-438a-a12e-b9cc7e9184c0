package io.renren.modules.enterprise.ps02face.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.arcsoft.face.FaceInfo;
import com.arcsoft.face.toolkit.ImageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Base64Utils;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ps02face.dao.Ps02FaceDao;
import io.renren.modules.enterprise.ps02face.dto.FaceCheckedResDTO;
import io.renren.modules.enterprise.ps02face.dto.FaceRecoResDTO;
import io.renren.modules.enterprise.ps02face.dto.Ps02FaceDTO;
import io.renren.modules.enterprise.ps02face.entity.Ps02FaceEntity;
import io.renren.modules.enterprise.ps02face.service.IFaceEngineService;
import io.renren.modules.enterprise.ps02face.service.IPs02FaceService;
import io.renren.modules.enterprise.ps02face.service.ISaveOrUpdateRedisService;
import io.renren.modules.enterprise.ps02face.vo.FaceAddParamsVO;
import io.renren.modules.enterprise.ps02face.vo.FaceDataAddParamsVO;
import io.renren.modules.enterprise.ps02face.vo.FaceRecoResVO;
import io.renren.modules.enterprise.ps02facedata.dao.Ps02FaceDataDao;
import io.renren.modules.enterprise.ps02facedata.entity.Ps02FaceDataEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.arcsoft.face.toolkit.ImageFactory.getRGBData;

/**
 * 工人人脸库信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-02-20
 */
@Service
public class IPs02FaceServiceImpl extends CrudServiceImpl<Ps02FaceDao, Ps02FaceEntity, Ps02FaceDTO> implements IPs02FaceService {

    @Autowired
    private IFaceEngineService iFaceEngineService;

    @Autowired
    private ISaveOrUpdateRedisService iSaveOrUpdateRedisService;

    @Autowired
    private Ps02FaceDataDao ps02FaceDataDao;

    @Override
    public QueryWrapper<Ps02FaceEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps02FaceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public Result<String> faceFeatureAdd(FaceAddParamsVO faceAddParamsVO) {

        String image = faceAddParamsVO.getImage();
        byte[] bytes = Base64Utils.base64ToBytes(image);
        ImageInfo rgbData = getRGBData(bytes);
        List<FaceInfo> faceInfoList = iFaceEngineService.detectFaces(rgbData);
        if (CollectionUtil.isNotEmpty(faceInfoList)) {
            for (FaceInfo faceInfo : faceInfoList) {
                byte[] feature = iFaceEngineService.extractFaceFeature(rgbData, faceInfo);
                if (feature != null) {
                    Ps02FaceDataEntity ps02FaceDataEntity = new Ps02FaceDataEntity();
                    ps02FaceDataEntity.setFaceFeature(feature);
                    ps02FaceDataEntity.setPs0201(faceAddParamsVO.getPs0201());
                    ps02FaceDataEntity.setPj0101(Long.valueOf(faceAddParamsVO.getProjectId()));
                    ps02FaceDataEntity.setInOrOut(ps02FaceDataEntity.getInOrOut());
                    ps02FaceDataEntity.setCreateTime(new Date());
                    ps02FaceDataEntity.setUpdateTime(new Date());
                    ps02FaceDataEntity.setAreacode(faceAddParamsVO.getAreacode());
                    saveOrUpdateFace(ps02FaceDataEntity, faceAddParamsVO.getIsAdd());
                    // 存入数据库、redis
//                    Ps02FaceEntity ps02FaceDTO = new Ps02FaceEntity();
//                    ps02FaceDTO.setFaceFeature(feature);
//                    ps02FaceDTO.setPs0201(faceAddParamsVO.getPs0201());
//                    ps02FaceDTO.setPj0101(Long.valueOf(faceAddParamsVO.getProjectId()));
//                    ps02FaceDTO.setCreateTime(new Date());
//                    ps02FaceDTO.setUpdateTime(new Date());
//                    saveOrUpdateFace(ps02FaceDTO);
                    // 存入redis
//                    iSaveOrUpdateRedisService.insertFaceFeature(faceAddParamsVO.getProjectId(), ps02FaceDTO);
                }
            }
        }
        return new Result<String>().ok("操作成功");
    }

    @Override
    public Result<String> collectFaceDataFeatureAdd(FaceDataAddParamsVO faceAddParamsVO) {
        String image = faceAddParamsVO.getImage();
        byte[] bytes = Base64Utils.base64ToBytes(image);
        ImageInfo rgbData = getRGBData(bytes);
        List<FaceInfo> faceInfoList = iFaceEngineService.detectFaces(rgbData);
        if (CollectionUtil.isNotEmpty(faceInfoList)) {
            for (FaceInfo faceInfo : faceInfoList) {
                byte[] feature = iFaceEngineService.extractFaceFeature(rgbData, faceInfo);
                if (feature != null) {
                    // 存入数据库、redis
                    Ps02FaceDataEntity ps02FaceDTO = new Ps02FaceDataEntity();
                    ps02FaceDTO.setFaceFeature(feature);
                    ps02FaceDTO.setPs0201(faceAddParamsVO.getPs0201());
                    ps02FaceDTO.setPj0101(Long.valueOf(faceAddParamsVO.getProjectId()));
                    ps02FaceDTO.setAreacode(faceAddParamsVO.getAreacode());
                    ps02FaceDTO.setUpdateTime(new Date());
//                    saveOrUpdateFace(ps02FaceDTO);
                    // 存入redis
//                    iSaveOrUpdateRedisService.insertFaceFeature(faceAddParamsVO.getProjectId(), ps02FaceDTO);
                }
            }
        }
        return new Result<String>().ok("操作成功");
    }

    @Override
    public byte[] collectFaceDataFeature(String base64) {
        if (StringUtils.isNotEmpty(base64)) {
            byte[] bytes = Base64Utils.base64ToBytes(base64);
            ImageInfo rgbData = getRGBData(bytes);
            List<FaceInfo> faceInfoList = iFaceEngineService.detectFaces(rgbData);
            if (CollectionUtil.isNotEmpty(faceInfoList)) {
                for (FaceInfo faceInfo : faceInfoList) {
                    byte[] feature = iFaceEngineService.extractFaceFeature(rgbData, faceInfo);
                    if (feature != null) {
                        return feature;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Result<List<FaceCheckedResDTO>> faceSearch(FaceRecoResVO faceRecoResVO) {
        String image = faceRecoResVO.getImage();

        List<FaceCheckedResDTO> faceRecoResDTOList = Lists.newLinkedList();

        byte[] bytes = Base64Utils.base64ToBytes(image);
        ImageInfo rgbData = getRGBData(bytes);
        List<FaceInfo> faceInfoList = iFaceEngineService.detectFaces(rgbData);
        if (CollectionUtil.isNotEmpty(faceInfoList)) {
            for (FaceInfo faceInfo : faceInfoList) {
                byte[] feature = iFaceEngineService.extractFaceFeature(rgbData, faceInfo);
                if (feature != null) {
                    List<Ps02FaceEntity> batchSetCache = iSaveOrUpdateRedisService.batchGetFaceFeature(faceRecoResVO.getProjectId());
                    List<FaceRecoResDTO> userCompareInfos = iFaceEngineService.faceRecoSearch(feature, batchSetCache, 0.8f);
                    if (userCompareInfos.size() > 0) {
                        for (FaceRecoResDTO resDTO : userCompareInfos) {
                            FaceCheckedResDTO faceCheckedResDTO = new FaceCheckedResDTO();
                            faceCheckedResDTO.setPs0201(String.valueOf(resDTO.getPs0201()));
                            faceRecoResDTOList.add(faceCheckedResDTO);
                        }
                    }
                }
            }
        }
        return new Result<List<FaceCheckedResDTO>>().ok(faceRecoResDTOList);
    }

    /**
     * 持久化数据
     * @param ps02
     */
    private void saveOrUpdateFace(Ps02FaceDataEntity ps02, Boolean isAdd) {
        if (!isAdd) {
            ps02FaceDataDao.update(ps02, new QueryWrapper<Ps02FaceDataEntity>()
                    .eq("ps0201", ps02.getPs0201()));
        } else {
            ps02FaceDataDao.insert(ps02);
        }
    }

    /**
     * 判断是否存在
     *
     * @param ps02
     * @return
     */
    private void saveOrUpdateFace(Ps02FaceEntity ps02) {

        Integer ps0201 = baseDao.selectCount(new QueryWrapper<Ps02FaceEntity>()
                .eq("ps0201", ps02.getPs0201()));
        if (ps0201 > 0) {
            update(ps02, new QueryWrapper<Ps02FaceEntity>()
                    .eq("ps0201", ps02.getPs0201()));
        } else {
            insert(ps02);
        }
    }
}