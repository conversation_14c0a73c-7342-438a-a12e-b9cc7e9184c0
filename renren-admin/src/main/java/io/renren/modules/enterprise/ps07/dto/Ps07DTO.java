package io.renren.modules.enterprise.ps07.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目管理人员进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-06
 */
@Data
@ApiModel(value = "项目管理人员进退场信息")
public class Ps07DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0701;
    @ApiModelProperty(value = "管理人员主键ID")
    private Long ps0401;
    @ApiModelProperty(value = "进退场时间")
    private Date entryOrExitTime;
    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
    @ApiModelProperty(value = "备注")
    private String memo;

}