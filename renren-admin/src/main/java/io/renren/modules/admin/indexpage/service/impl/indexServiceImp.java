package io.renren.modules.admin.indexpage.service.impl;

import com.alibaba.fastjson.JSONObject;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.indexpage.dao.indexDao;
import io.renren.modules.admin.indexpage.dto.*;
import io.renren.modules.admin.indexpage.service.indexService;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.sys.dao.SysDictDataDao;
import io.renren.modules.admin.sys.dto.SysDictDataDTO;
import io.renren.modules.enterprise.pa01.dao.Pa01Dao;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.ps04.dao.Ps04Dao;
import io.renren.modules.enterprise.ps13.dao.Ps13Dao;
import io.renren.modules.enterprise.ps13.entity.Ps13Entity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 首页数据
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-27
 */
@Service
public class indexServiceImp implements indexService {
    @Autowired
    private indexDao indexDao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Pa01Dao pa01Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Ps13Dao ps13Dao;

    @Override
    public List getProjectSupplement() {
        ArrayList<Object> list = new ArrayList<>();
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        //项目信息完善提示
        JSONObject object = new JSONObject();
        Long pj01Count = pj01Dao.getProjectSupplement(pj0101);
        if (pj01Count > 0) {
            object.put("supplementType", "1");
            object.put("description", "项目信息未完善");
            object.put("pj0101", pj0101);
            list.add(object);
        }
        //工资专户完善提示
        JSONObject object1 = new JSONObject();
        Long pa01Count = pa01Dao.getSpecialAccountSupplement(pj0101);
        if (pa01Count < 1) {
            object1.put("supplementType", "2");
            object1.put("description", "工资专户信息未完善");
            object1.put("pj0101", pj0101);
            list.add(object1);
        }
        //关键岗位人员完善提示
        JSONObject object2 = new JSONObject();
        Long ps04Count = ps04Dao.getManagerSupplement(pj0101);
        List<Ps13Entity> keyJobs = ps13Dao.selectList(null);
        if (ps04Count < keyJobs.size()) {
            object2.put("supplementType", "3");
            object2.put("description", "关键岗位人员未完善");
            object2.put("pj0101", pj0101);
            list.add(object2);
        }
        return list;
    }

    @Override
    public ProjectDTO getProjectInfo() {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        return indexDao.getProjectInfo(pj0101);
    }

    @Override
    public PersonNumberDTO getPersonNumber() {
        Long deptId = SecurityUser.getDeptId();
        return indexDao.getPersonNumber(deptId);
    }

    @Override
    public AttendanceNumberDTO getAttendanceNumber() {
        Long deptId = SecurityUser.getDeptId();
        return indexDao.getAttendanceNumber(deptId);
    }

    @Override
    public String getTotalPayment() {
        Long deptId = SecurityUser.getDeptId();
        BigDecimal totalPayment = indexDao.getTotalPayment(deptId).divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_HALF_DOWN);
        return totalPayment + "万";
    }

    @Override
    public List<ParticipatingUnitsDTO> getParticipatingUnits() {
        Long deptId = SecurityUser.getDeptId();
        return indexDao.getParticipatingUnits(deptId);
    }

    @Override
    public List<ManagerAttendanceDTO> getManagerAttendance() {
        Long deptId = SecurityUser.getDeptId();
        return indexDao.getManagerAttendance(deptId);
    }

    @Override
    public List<EquipmentInfoDTO> getEquipmentInfo() {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        return indexDao.getEquipmentInfo(pj0101);
    }

    @Override
    public List<FileInfoDTO> getFileInfoDTO() {
        Long deptId = SecurityUser.getDeptId();
        return indexDao.getFileInfoDTO(deptId);
    }

    @Override
    public List<MonthAttendanceDTO> getMonthAttendance() {
        Long deptId = SecurityUser.getDeptId();
        return indexDao.getMonthAttendance(deptId);
    }
}