package io.renren.common.minio;


import io.minio.MinioClient;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * MinIo配置文件
 *
 * <AUTHOR>
 * @Date 2021-08-03 16:11
 */
@Data
@Component
public class MinIoClientConfig {
    /**
     * MinIo服务地址
     */
//    private final String endpoint = "https://jzgappendix.xygkcloud.com/";
    private final String endpoint = "https://lzerappendix.xygkcloud.com/";
    /**
     * MinIo用户名
     */
    private final String accessKey = "admin";
    /**
     * MinIo密码
     */
//    private final String secretKey = "XinYeGk@123";
    private final String secretKey = "ErXinYeGk@123";
    /**
     * 桶名称
     */
//    private final String bucketName = "lzercenter-test";
    private final String bucketName = "lzercenter";

    /**
     * 注入MinIo客户端
     *
     * @return
     */
    @Bean
    @SneakyThrows
    public MinioClient minioClient() {
        MinioClient minioClient = new MinioClient(endpoint, accessKey, secretKey);
        boolean bucketExists = minioClient.bucketExists(bucketName);
        if (!bucketExists){
            minioClient.makeBucket(bucketName);
        }
        return minioClient;
    }
}
