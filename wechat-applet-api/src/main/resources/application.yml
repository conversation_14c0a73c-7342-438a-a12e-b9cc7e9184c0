server:
  undertow:
    io-threads: 4
    worker-threads: 32
    buffer-size: 1024
    direct-buffers: true
  servlet:
    context-path: /mobile/
    session:
      cookie:
        http-only: true
  port: 8015
#  port: 9015

spring:
  profiles:
    active: prod
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 3MB
      max-request-size: 3MB
      enabled: true

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  typeAliasesPackage: com.xygk.applet.*.entity
  global-config:
    db-config:
      id-type: id_worker
    banner: false
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

aliyun:
  accessKeyId: LTAIctdBrVY9VhQc
  secret: s5Rus0677tBTEf4zFOmAQmUjxVFlG7
  IDCard:
    host: http://dm-51.data.aliyun.com
    path: /rest/160601/ocr/ocr_idcard.json
    appcode: 8a1f5f40d1764f73ad99818f4e51c64e

#文件上传配置
file:
  #mac系统
  macPath: ~/upload/
  #windows服务器上传的路径
  windowsPath: D:/upload/
  #linux服务器上传的路径
  linuxPath: /home/<USER>/


