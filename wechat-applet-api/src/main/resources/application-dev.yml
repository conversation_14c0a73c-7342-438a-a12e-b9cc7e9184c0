spring:
  datasource:
    druid:
#      #Oracle
      driver-class-name: oracle.jdbc.OracleDriver
      url: **************************************
      username: luzhou_management_center
      password: luzhou_management_center
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
#文件下载
download:
  installpackUrl: D:/download/app/ybzjapp.apk

# 虹软配置
config:
  arcface-sdk:
    app-id: APn3yXgphD7QveLkiQvi4xSX2XqFk5dCWAjjxwxUJKDo
    sdk-key: Bw45ks1jMxqYbjH9UdgcEttR7Z3Aq9NyiVEwdbfV4Hi4
    sdkLibPath: D:\gknmgFile\ymzjarcface\WIN64
    thread-pool-size: 5