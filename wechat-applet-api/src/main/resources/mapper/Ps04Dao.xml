<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xygk.applet.modules.dao.Ps04Dao">
    <select id="getManagerPersonInfoList"
            resultType="com.xygk.applet.supervision.dto.ManagerPersonPageInfoDto">
        select  b.ps0401,
                t.name,
                t.cellphone,
                t.workertype,
                b.photo,
                c.corpname,
                b.jobtype,
                b.in_or_out
                from b_ps01 t, b_ps03 a, b_ps04 b, b_cp01 c,r_pj01_dept d,b_pj01 e
                where t.ps0101 = a.ps0101
                and a.ps0301 = b.ps0301
                and a.cp0101 = c.cp0101
                and b.pj0101 = d.pj0101
                and b.pj0101 = e.pj0101
                and d.dept_id = #{deptId}
        <if test="personName != null and personName != ''">
            and t.name like '%'||#{personName}||'%'
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and b.pj0101 = #{pj0101}
        </if>
        <if test="inOrOut != null and inOrOut != ''">
            and b.in_or_out = #{inOrOut}
        </if>
        <if test="prjstatus != null and prjstatus != ''">
            and e.prjstatus = #{prjstatus}
        </if>
        <if test="iskeyjob == 1 and iskeyjob != ''">
            and b.jobtype in ('1001','1009')
        </if>
        <if test="iskeyjob == 0  and iskeyjob != ''">
            and b.jobtype not in ('1001','1009')
        </if>
        order by b.create_date desc
    </select>
    <select id="getManagerEngageInInfo" resultType="com.xygk.applet.modules.dto.Ps04DTO">
        select b.name,
               b.idcardnumber,
               (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
               d.corpname,
               d.corpcode,
               c.corptype,
               t.jobtype,
               t.entrytime,
               t.exittime,
               t.in_or_out
          from b_ps04 t, b_ps03 a, b_ps01 b, b_cp02 c, b_cp01 d
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.cp0201 = c.cp0201
           and c.cp0101 = d.cp0101
           and b.ps0101 = #{ps0101}
           order by t.ps0401 desc
    </select>
    <select id="getManagerPageInfoList" resultType="com.xygk.applet.supervision.dto.ManagerPageInfoDto">
        SELECT d.corpname,b.name,b.gender,b.cellphone,b.idcardnumber,
        a.jobtype,a.in_or_out,a.entrytime,a.exittime
        FROM b_ps04 a,b_ps01 b,b_cp02 c,b_cp01 d,b_ps03 e
        where a.ps0301=e.ps0301 and e.ps0101=b.ps0101
        and a.cp0201=c.cp0201 and c.cp0101=d.cp0101
        and a.pj0101=#{pj0101}
        order by a.ps0401 desc
    </select>
    <select id="selectListByIds" resultType="com.xygk.applet.supervision.dto.ManagerPersonInfoDto">
        SELECT b.name,b.cellphone,c.name projectname,t.jobtype,d.corpname,
        d.corpcode,e.corptype,t.entrytime,t.ps0401,t.cp0201
        FROM b_ps04 t,b_ps03 a, b_ps01 b, b_pj01 c, b_cp01 d, b_cp02 e
        WHERE t.ps0301 = a.ps0301 and a.ps0101 = b.ps0101 and t.pj0101 = c.pj0101
        and t.cp0201 = e.cp0201 and e.cp0101 = d.cp0101
        and t.ps0401 in
        <foreach item="item" collection="array" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="selectInfoById" resultType="com.xygk.applet.supervision.dto.ManagerDetailsInfoDto">
        select b.*,
               t.ps0401,
               t.photo,
               t.jobtype,
               t.entrytime,
               t.exittime,
               t.in_or_out,
               c.corptype,
               d.corpname
          from b_ps04 t, b_ps03 a, b_ps01 b, b_cp02 c, b_cp01 d
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.cp0201 = c.cp0201
           and c.cp0101 = d.cp0101
           and t.ps0401 = #{ps0401}
    </select>
    <select id="selectListForJC" resultType="com.xygk.applet.modules.entity.Ps04Entity">
        select *
        from B_PS04 t
        where t.pj0101=1629860343823667202
    </select>
    <select id="getKeyJobInfo" resultType="com.xygk.applet.project.dto.ManagerInfoDTO">
        select c.name,
               c.idcardnumber,
               c.cellphone,
               t.ps0401,
               t.photo,
               t.entrytime,
               t.in_or_out,
               t.jobtype,
               t.isconfirm,
               (select max(x.checkdate)
                  from b_kq02 x
                 where x.pj0101 = t.pj0101
                   and x.user_id = t.ps0401) as checkdate
          from B_PS04 t, b_cp02 a, b_ps03 b, b_ps01 c
         where t.ps0301 = b.ps0301
           and b.ps0101 = c.ps0101
           and t.cp0201 = a.cp0201
           and a.corptype = #{corptype}
           and t.pj0101 = #{pj0101}
           and t.in_or_out = '1'
           and t.jobtype = #{jobtype}
    </select>
    <select id="isInProject" resultType="java.lang.Long">
        select count(1)
          from B_PS04 t, B_ps03 a, b_ps01 b
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.pj0101 = #{pj0101}
           and b.ps0101 = #{ps0101}
    </select>
    <select id="isInOtherProject" resultType="java.lang.Long">
        select count(1)
          from B_PS04 t, B_ps03 a, b_ps01 b
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.pj0101 != #{pj0101}
           and a.cp0101 != #{cp0101}
           and b.ps0101 = #{ps0101}
           and t.in_or_out = '1'
    </select>
    <select id="selectPersonByIds" resultType="com.xygk.applet.supervision.dto.PersonDTO">
        select a.NAME, b.PS0401 userId, b.PHOTO imageUrl
        from b_ps01 a,b_ps04 b,b_ps03 c where a.ps0101=c.ps0101 and c.ps0301=b.ps0301
        and b.PS0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="getList" resultType="com.xygk.applet.project.dto.ManagerInfoDTO">
        select t.*, b.name, substr(b.idcardnumber,0,4)||'************'||substr(b.idcardnumber,17,2) as idcardnumber, b.cellphone, d.corpname, c.corptype
          from b_ps04 t, b_ps03 a, b_ps01 b, b_cp02 c, b_cp01 d
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.cp0201 = c.cp0201
           and c.cp0101 = d.cp0101
           and t.pj0101 = #{pj0101}
        <if test="name != null and name != ''">
            and (b.name like '%' || #{name} || '%' or b.cellphone like '%' || #{name} || '%')
        </if>
        <if test="inOrOut != null and inOrOut != ''">
            and t.IN_OR_OUT = #{inOrOut}
        </if>
         order by t.entrytime desc,t.rowid
    </select>
    <select id="getInfo" resultType="com.xygk.applet.project.dto.ManagerInfoDTO">
        select t.*, b.*, d.corpname, c.corptype
          from b_ps04 t, b_ps03 a, b_ps01 b, b_cp02 c, b_cp01 d
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.cp0201 = c.cp0201
           and c.cp0101 = d.cp0101
           and t.ps0401 = #{ps0401}
    </select>
    <select id="selectExitByPj0101" resultType="com.xygk.applet.modules.entity.Ps04Entity">
        select h.*
          from (select t.*
                  from B_PS04 t, b_ps03 a, b_ps01 b
                 where t.ps0301 = a.ps0301
                   and a.ps0101 = b.ps0101
                   and b.idcardnumber = #{idcardnumber}
                   and t.pj0101 = #{pj0101}
                   and t.in_or_out = '2'
                 order by t.exittime desc) h
         where rownum &lt; 2
    </select>
    <select id="selectIsPs04" resultType="com.xygk.applet.modules.dto.Ps04DTO">
        select t.*
          from b_ps04 t, b_ps03 a, b_ps01 b
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.pj0101 = #{pj0101}
           and a.ps0101 = #{ps0101}
           and t.in_or_out = '1'
    </select>
    <select id="selectPs04ByPs0101AndPj0101" resultType="com.xygk.applet.modules.dto.Ps04DTO">
        select a.*,c.name
          from b_ps04 a, b_ps03 b,b_ps01 c, sys_user d
         where a.ps0301 = b.ps0301
           and b.ps0101 = c.ps0101
           and c.ps0101 = d.ps0101
           and a.in_or_out = '1'
           and a.pj0101 = #{pj0101}
           and d.id = #{userId}
    </select>
    <select id="selectExist" resultType="java.lang.Long">
        select count(1)
          from b_ps04 t, b_ps03 a
         where t.ps0301 = a.ps0301
           and t.pj0101 = #{pj0101}
           and a.ps0101 = #{ps0101}
    </select>
    <insert id="insertProjectPmInfo">
        insert into zjnmg.project_pm_info
              (pj0101,
               corp_name,
               corp_code,
               corp_type,
               p_type,
               pm_name,
               pm_id_card_type,
               pm_id_card_number,
               pm_phone,
               id,
               busiid,
               grant_org,
               nation,
               politics_type,
               culture_level_type,
               address,
               head_image)
            values
              (#{PJ0101},
               #{CORP_NAME},
               #{CORP_CODE},
               #{CORP_TYPE},
               #{P_TYPE},
               #{PM_NAME},
               '1',
               #{PM_ID_CARD_NUMBER},
               #{PM_PHONE},
               zjnmg.seq_project_pm_info.nextval,
               #{BUSIID},
               substr(#{GRANT_ORG}, 0, 10),
               #{NATION},
               #{POLITICS_TYPE},
               #{CULTURE_LEVEL_TYPE},
               #{ADDRESS},
               #{HEAD_IMAGE})
    </insert>
    <insert id="insertProjectWorkerInoutInfo">
        insert into zjnmg.project_worker_inout_info
              (pj0101, id_card_number, in_out, occur_time, id)
            values
              (#{PJ0101},
               #{ID_CARD_NUMBER},
               '1',
               #{OCCUR_TIME},
               zjnmg.seq_project_worker_inout_info.nextval)
    </insert>
    <select id="selectSeqAttInfo" resultType="java.lang.Long">
        select zjnmg.seq_att_info.nextval from dual
    </select>
    <insert id="insertAttInfo">
        insert into zjnmg.att_info
              (att_id, att_name, att_type, area_code, busiid, file_path)
            values
              (#{ATT_ID},
               #{ATT_NAME},
               'jpg',
               '5115',
               #{ATT_ID},
               #{FILE_PATH})
    </insert>
    <select id="selectListForDevice2" resultType="com.xygk.applet.modules.dto.Ps04DTO">
        select t.*,b.name from b_ps04 t,b_ps03 a,b_ps01 b
        where t.ps0301=a.ps0301 and a.ps0101=b.ps0101 and t.pj0101=1647797734075674625 and t.in_or_out='1'
    </select>

    <select id="getManagerSupplement" resultType="java.lang.Long">
        select count(1)
                from B_PS04 t, b_cp02 a
                where t.pj0101 = #{pj0101}
                  and t.in_or_out = '1'
                  and t.jobtype = '1009'
                  and t.cp0201 = a.cp0201
                  and a.corptype = '9'
                  and rownum  &lt; 2
    </select>

    <select id="getSupplement" resultType="java.lang.Long">

        select count(1)
         from B_PS04 t, b_cp02 a
         where t.pj0101 = #{pj0101}
           and t.in_or_out = '1'
           and t.jobtype = '1001'
           and t.cp0201 = a.cp0201
           and a.corptype = '7'
           and rownum  &lt; 2
    </select>
    <update id="updatemore">
        update b_ps04 t set t.ps0301=#{ps0301} where t.ps0301=#{oldps0301}
    </update>

    <select id="getKeyJobCount" resultType="java.lang.Long">

        select count(1)
        from B_PS04 t, b_cp02 a
        where t.pj0101 = #{pj0101}
          and t.in_or_out = '1'
          and t.jobtype = #{jobtype}
          and t.cp0201 = a.cp0201
          and a.corptype = #{corptype}
    </select>
</mapper>
