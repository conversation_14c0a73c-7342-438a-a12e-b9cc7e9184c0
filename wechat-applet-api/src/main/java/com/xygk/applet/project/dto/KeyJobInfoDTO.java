package com.xygk.applet.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "关键岗位信息")
public class KeyJobInfoDTO {
    @ApiModelProperty(value = "关键岗位名称")
    private String name;

    @ApiModelProperty(value = "岗位类型")
    private String jobtype;

    @ApiModelProperty(value = "关键岗位列表")
    private List<ManagerInfoDTO> list;
}
