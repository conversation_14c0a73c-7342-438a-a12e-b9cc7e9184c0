package com.xygk.applet.project.controller;

import com.alibaba.fastjson.JSONObject;
import com.xygk.applet.common.annotation.Login;
import com.xygk.applet.common.enums.RoleEnum;
import com.xygk.applet.modules.service.HomeService;
import com.xygk.applet.project.dto.*;
import io.renren.common.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title HomeController
 * @Description 首页接口（项目端）
 * @Date 2022/12/27 16:24
 * @Copyright 2019-2025
 */
@RestController
@RequestMapping("/api/project/home")
@Api(tags = "首页接口（项目端）")
public class HomeController {
    @Autowired
    private HomeService homeService;

    @Login(role = RoleEnum.PROJECT)
    @GetMapping("getPorjectInfo")
    @ApiOperation("获取项目信息")
    public Result<ProjectInfoDTO> getPorjectInfo(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("pj0101", pj0101);
        ProjectInfoDTO data = homeService.getPorjectInfo(params);
        if (StringUtils.isBlank(data.getCategory())) {
            data.setIscomplete("1");
        }
        return new Result<ProjectInfoDTO>().ok(data);
    }

    @Login(role = RoleEnum.PROJECT)
    @GetMapping("getKeyJobInfo")
    @ApiOperation("获取关键岗位信息")
    public Result<List<KeyJobInfoDTO>> getKeyJobInfo(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("pj0101", pj0101);
        List<KeyJobInfoDTO> data = homeService.getKeyJobInfo(params);
        return new Result<List<KeyJobInfoDTO>>().ok(data);
    }

    @Login(role = RoleEnum.PROJECT)
    @GetMapping("getAttendanceTj")
    @ApiOperation("获取考勤统计信息")
    public Result<JSONObject> getAttendanceTj(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("pj0101", pj0101);
        JSONObject data = homeService.getAttendanceTj(params);
        return new Result<JSONObject>().ok(data);
    }

    @Login(role = RoleEnum.PROJECT)
    @GetMapping("getAttendanceMonthTj")
    @ApiOperation("按照月度获取考勤信息")
    public Result<List<AttendanceTjDTO>> getAttendanceMonthTj(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("pj0101", pj0101);
        List<AttendanceTjDTO> data = homeService.getAttendanceMonthTj(params);
        return new Result<List<AttendanceTjDTO>>().ok(data);
    }

    @Login(role = RoleEnum.PROJECT)
    @GetMapping("getDeviceInfo")
    @ApiOperation("获取考勤设备信息")
    public Result<List<DeviceInfoDTO>> getDeviceInfo(@ApiIgnore @RequestAttribute("pj0101") Long pj0101, @ApiIgnore @RequestParam Map<String, Object> params) {
        params.put("pj0101", pj0101);
        List<DeviceInfoDTO> list = homeService.getDeviceInfo(params);
        return new Result<List<DeviceInfoDTO>>().ok(list);
    }
}
