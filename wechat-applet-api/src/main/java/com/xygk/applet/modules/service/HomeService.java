package com.xygk.applet.modules.service;

import com.alibaba.fastjson.JSONObject;
import com.xygk.applet.modules.dto.DictType;
import com.xygk.applet.modules.dto.SysUserDTO;
import com.xygk.applet.modules.entity.SysUserEntity;
import com.xygk.applet.project.dto.*;
import io.renren.common.service.CrudService;

import java.util.List;
import java.util.Map;

/**
 * 首页
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-08
 */
public interface HomeService extends CrudService<SysUserEntity, SysUserDTO> {
    /**
     * 首页项目信息
     * @param params
     * @return
     */
    ProjectInfoDTO getPorjectInfo(Map<String, Object> params);

    /**
     * 关键岗位信息
     * @param params
     * @return
     */
    List<KeyJobInfoDTO> getKeyJobInfo(Map<String, Object> params);

    /**
     * 获取考勤统计信息
     * @param params
     * @return
     */
    JSONObject getAttendanceTj(Map<String, Object> params);

    /**
     * 按照月度获取考勤信息
     *
     * @param params
     * @return
     */
    List<AttendanceTjDTO> getAttendanceMonthTj(Map<String, Object> params);

    /**
     * 获取设备信息
     * @param params
     * @return
     */
    List<DeviceInfoDTO> getDeviceInfo(Map<String, Object> params);
}
