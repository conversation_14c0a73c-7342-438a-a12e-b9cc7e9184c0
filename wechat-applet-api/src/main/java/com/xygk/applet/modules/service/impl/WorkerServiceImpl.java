package com.xygk.applet.modules.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xygk.applet.common.constant.CommonConstants;
import com.xygk.applet.modules.dao.*;
import com.xygk.applet.modules.dto.*;
import com.xygk.applet.modules.entity.*;
import com.xygk.applet.modules.service.*;
import com.xygk.applet.modules.vo.FaceAddParamsVO;
import com.xygk.applet.project.dto.PersonInfoDTO;
import com.xygk.applet.project.dto.WorkerInfoDTO;
import com.xygk.applet.project.event.FaceAddParamsEvent;
import com.xygk.applet.supervision.dto.PersonDTO;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 通用方法
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkerServiceImpl extends CrudServiceImpl<SysUserDao, SysUserEntity, SysUserDTO> implements WorkerService {
    @Autowired
    private BPs01Dao ps01Dao;
    @Autowired
    private SysDictTypeDao dictTypeDao;
    @Autowired
    private BPs02Dao ps02Dao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Tm01Dao tm01Dao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private BPs06Dao ps06Dao;
    @Autowired
    private SupDeviceTaskService taskService;
    @Autowired
    private IPs02FaceService iPs02FaceService;
    @Autowired
    private BPs06Service bPs06Service;
    @Autowired
    private BOt01Service bOt01Service;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public QueryWrapper<SysUserEntity> getWrapper(Map<String, Object> params) {
        return null;
    }

    @Override
    public PageData<WorkerInfoDTO> getList(Map<String, Object> params) {
        IPage<SysUserEntity> page = getPage(params, "", false);
        List<WorkerInfoDTO> list = ps02Dao.getList(params);
        return getPageData(list, page.getTotal(), WorkerInfoDTO.class);
    }

    @Override
    public Result saveInfo(WorkerInfoDTO dto) {
        Result<Object> result = new Result<>();
        //处理人员基本信息
        String idcardnumber = dto.getIdcardnumber();
        //判断证件号码是否正确
        boolean validCard = IdcardUtil.isValidCard(idcardnumber);
        if (!validCard) {
            return result.error("证件号码不正确，请检查后重试！");
        }
        //判断身份证是否失效
        /*boolean expiry = dto.getExpirydate().before(new Date());
        if (expiry) {
            return result.error("证件已过期！");
        }*/
        //处理性别
        int genderByIdCard = IdcardUtil.getGenderByIdCard(idcardnumber);
        dto.setGender(genderByIdCard == 1 ? "1" : "2");
        //处理民族
        //String nation = dictTypeDao.getNationByLabel(dto.getNation());
        //dto.setNation(nation);
        //处理出生日期
        dto.setBirthday(IdcardUtil.getBirthDate(dto.getIdcardnumber()));
        //处理籍贯
        dto.setAreacode(idcardnumber.substring(0, 6));
        //处理人员类别
        dto.setWorkertype("1");
        //处理头像
        dto.setHeadimageurl(dto.getIssuecardpicurl());

        BPs01Entity ps01 = ps01Dao.selectOne(new QueryWrapper<BPs01Entity>().eq("IDCARDNUMBER", idcardnumber));
        if (ps01 == null) {
            ps01 = new BPs01Entity();
            BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01.setHeadimageurl(dto.getIssuecardpicurl());
            ps01Dao.insert(ps01);
        } else {
            BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01.setHeadimageurl(dto.getIssuecardpicurl());
            ps01Dao.updateById(ps01);
        }
        dto.setPs0101(ps01.getPs0101());
        //处理工人信息
        //判断该项目下边人员是否存在
        Long isExist = ps02Dao.selectIsExist(dto.getPj0101(), dto.getPs0101());
        if (isExist > 0) {
            return result.error("项目中已存在该人员！请勿重复录入");
        }
        dto.setEntrytime(dto.getEntrytime() == null ? new Date() : dto.getEntrytime());
        dto.setInOrOut("1");
        //处理班组长问题
        if ("1".equals(dto.getIsteamleader())) {
            ps02Dao.updateTeamleader(dto.getTm0101());
            Tm01Entity tm01 = tm01Dao.selectById(dto.getTm0101());
            tm01.setResponsiblepersonname(dto.getName());
            tm01.setResponsiblepersonidnumber(dto.getIdcardnumber());
            tm01.setResponsiblepersonphone(dto.getCellphone());
            tm01Dao.updateById(tm01);
        }
        BPs02Entity ps02 = new BPs02Entity();
        BeanUtil.copyProperties(dto, ps02, CopyOptions.create().setIgnoreNullValue(true));
        ps02Dao.insert(ps02);

        //保存进退场信息
        BPs06Entity ps06Entity = new BPs06Entity();
        ps06Entity.setEntryOrExitTime(DateUtil.parse(DateUtil.today())).setInOrOut("1").setPs0201(dto.getPs0201());
        ps06Dao.insert(ps06Entity);


        //添加设备下发信息
        List<PersonDTO> personDTOList = this.getPersonIntoDevices(ps02.getPs0201(), ps01.getName(), ps02.getIssuecardpicurl());
        //人员注册、下发到设备
        taskService.personIntoDevicesByPj0101(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.WORKER_TYPE, dto.getPj0101());
        FaceAddParamsVO faceAddParamsVO = new FaceAddParamsVO();
        faceAddParamsVO.setProjectId(String.valueOf(ps02.getPj0101()));
        faceAddParamsVO.setPs0201(ps02.getPs0201());
        faceAddParamsVO.setImage(dto.getIssuecardpicurl());
        faceAddParamsVO.setInOrOut(dto.getInOrOut());
        applicationEventPublisher.publishEvent(new FaceAddParamsEvent(faceAddParamsVO));

        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getSpecialWorkCertificateFiles())) {
            bOt01Service.updateUnlessFiles("13", ps02.getPs0201());
            bOt01Service.doFileRelation(dto.getSpecialWorkCertificateFiles(), ps02.getPs0201());
        }
        return result;
    }

    @Override
    public Result updateInfo(WorkerInfoDTO dto) {
        Result<Object> result = new Result<>();
        Pj01Entity pj01 = pj01Dao.selectById(dto.getPj0101());
        //修改人员基本信息
        BPs01Entity ps01 = ps01Dao.selectById(dto.getPs0101());
        if (!dto.getIdcardnumber().equals(ps01.getIdcardnumber())) {
            return result.error("检测到证件号码发生变更，不允许修改证件号码！");
        }
        BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
        int genderByIdCard = IdcardUtil.getGenderByIdCard(dto.getIdcardnumber());
        ps01.setGender(genderByIdCard == 1 ? "1" : "2");
        ps01.setHeadimageurl(dto.getIssuecardpicurl());
        ps01Dao.updateById(ps01);
        //修改工人信息
        BPs02Entity ps02 = ps02Dao.selectById(dto.getPs0201());
        Long tm0101 = ps02.getTm0101();
        //处理班组长问题
        if ("1".equals(dto.getIsteamleader())) {
            ps02Dao.updateTeamleader(dto.getTm0101());
            Tm01Entity tm01 = tm01Dao.selectById(dto.getTm0101());
            tm01.setResponsiblepersonname(dto.getName());
            tm01.setResponsiblepersonidnumber(dto.getIdcardnumber());
            tm01.setResponsiblepersonphone(dto.getCellphone());
            tm01Dao.updateById(tm01);
        }
        BeanUtil.copyProperties(dto, ps02, CopyOptions.create().setIgnoreNullValue(true));
        ps02Dao.updateById(ps02);
        if (!"0".equals(pj01.getStupstatus())) {
            if ("1".equals(ps02.getInOrOut())) {
                if (!tm0101.equals(dto.getTm0101())) {
                    throw new RenException("已上报省厅，不允许修改所属班组！请退场后修改重新入场！");
                }
            }
        }

        FaceAddParamsVO faceAddParamsVO = new FaceAddParamsVO();
        faceAddParamsVO.setProjectId(String.valueOf(ps02.getPj0101()));
        faceAddParamsVO.setPs0201(ps02.getPs0201());
        faceAddParamsVO.setImage(dto.getIssuecardpicurl());
        faceAddParamsVO.setInOrOut(dto.getInOrOut());
        applicationEventPublisher.publishEvent(new FaceAddParamsEvent(faceAddParamsVO));

        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getSpecialWorkCertificateFiles())) {
            bOt01Service.updateUnlessFiles("13", dto.getPs0201());
            bOt01Service.doFileRelation(dto.getSpecialWorkCertificateFiles(), dto.getPs0201());
        }
        return result;
    }

    @Override
    public Result getInfo(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        WorkerInfoDTO dto = ps02Dao.getInfo(Long.valueOf(params.get("ps0201").toString()));
        if (ObjectUtil.isNotNull(dto)) {
            List<BOt01DTO> specialWorkCertificateFiles = bOt01Service.getBusinessData(dto.getPs0201(), "13");
            dto.setSpecialWorkCertificateFiles(specialWorkCertificateFiles);
        }
        return result.ok(dto);
    }

    @Override
    public Result exit(WorkerInfoDTO dto) {
        Result<Object> result = new Result<>();
        Long ps0201 = dto.getPs0201();
        //处理退场数据
        BPs02Entity ps02 = ps02Dao.selectById(ps0201);
        ps02.setExittime(new Date());
        ps02.setInOrOut("2");
        ps02.setCreateDate(null);
        ps02Dao.updateById(ps02);
        bPs06Service.batchInsertInOrOut(CollectionUtil.toList(dto.getPs0201()), "2");
        //写入设备删除人员任务
        List<PersonDTO> personDTOS = ps02Dao.selectPersonByIds(CollectionUtil.toList(ps0201));
        taskService.personIntoDevicesByPj0101(personDTOS, CommonConstants.PERSON_DELETE, CommonConstants.WORKER_TYPE, dto.getPj0101());
        return result;
    }

    @Override
    public Result entry(WorkerInfoDTO dto) {
        Result<Object> result = new Result<>();
        Long ps0201 = dto.getPs0201();
        //处理入场数据
        BPs02Entity ps02 = ps02Dao.selectById(ps0201);
        ps02.setEntrytime(new Date());
        Date exittime = ps02.getExittime();
        ps02.setExittime(null);
        ps02.setInOrOut("1");
        ps02Dao.updateById(ps02);
        Pj01Entity pj01 = pj01Dao.selectById(ps02.getPj0101());
        if (!"0".equals(pj01.getStupstatus())) {
            long hours = DateUtil.between(exittime, new Date(), DateUnit.HOUR);
            if (hours < 12) {
                throw new RenException("已上报省厅，" + (12 - hours) + "小时后才可重新入场该人员！");
            }
            Tm01Entity tm01 = tm01Dao.selectById(ps02.getTm0101());
            Cp02Entity cp02 = cp02Dao.selectById(tm01.getCp0201());
            Cp01Entity cp01 = cp01Dao.selectById(cp02.getCp0101());
            BPs01Entity ps01 = ps01Dao.selectById(ps02.getPs0101());
            //写入头像上报表
            Map<String, Object> headImageParams = new HashMap<>();
            Long att_id = ps02Dao.selectSeqAttInfo();
            headImageParams.put("ATT_ID", att_id);
            headImageParams.put("ATT_NAME", ps01.getPs0101() + ps02.getPs0201());
            headImageParams.put("FILE_PATH", ps02.getIssuecardpicurl());
            ps02Dao.insertAttInfo(headImageParams);
            //写入建筑工人上报表
            Map<String, Object> workerParams = new HashMap<>();
            workerParams.put("PJ0101", pj01.getPj0101());
            workerParams.put("CORP_NAME", cp01.getCorpname());
            workerParams.put("CORP_CODE", cp01.getCorpcode());
            workerParams.put("TEAM_SYS_NO", tm01.getTeamsysno());
            workerParams.put("TEAM_NAME", tm01.getTeamname());
            workerParams.put("WORKER_NAME", ps01.getName());
            workerParams.put("IS_TEAM_LEADER", "1".equals(ps02.getIsteamleader()) ? "是" : "否");
            workerParams.put("ID_CARD_NUMBER", ps01.getIdcardnumber());
            if (IdcardUtil.isValidCard(ps01.getIdcardnumber())) {
                workerParams.put("AGE", IdcardUtil.getAgeByIdCard(ps01.getIdcardnumber()));
            } else {
                workerParams.put("AGE", "30");
            }
            workerParams.put("GENDER", "1".equals(ps01.getGender()) ? "男" : "女");
            workerParams.put("NATION", ps01.getNation());
            workerParams.put("ADDRESS", ps01.getAddress());
            workerParams.put("HEAD_IMAGE", att_id);
            workerParams.put("POLITICS_TYPE", ps01.getPoliticstype());
            workerParams.put("CULTURE_LEVEL_TYPE", ps01.getCultureleveltype());
            workerParams.put("GRANT_ORG", ps01.getGrantorg());
            workerParams.put("WORK_TYPE", ps02.getWorktypecode());
            workerParams.put("NATIVE_PLACE", ps01.getAreacode());
            workerParams.put("MOBILE", ps01.getCellphone());
            workerParams.put("BUSIID", ps02.getPs0201());
            ps02Dao.insertProjectWorkerInfo(workerParams);
            //写入人员进场数据上报表
            Map<String, Object> inoutParams = new HashMap<>();
            inoutParams.put("PJ0101", pj01.getPj0101());
            inoutParams.put("ID_CARD_NUMBER", ps01.getIdcardnumber());
            inoutParams.put("OCCUR_TIME", new Date());
            inoutParams.put("BUSIID", ps02.getPs0201());
            ps02Dao.insertProjectWorkerInoutInfo(inoutParams);
        }
        bPs06Service.batchInsertInOrOut(CollectionUtil.toList(dto.getPs0201()), "1");
        //添加设备下发信息
        List<PersonDTO> personDTOS = ps02Dao.selectPersonByIds(CollectionUtil.toList(dto.getPs0201()));
        //人员注册、下发到设备
        taskService.personIntoDevicesByPj0101(personDTOS, CommonConstants.PERSON_INTO, CommonConstants.WORKER_TYPE, dto.getPj0101());
        return result;
    }

    @Override
    public Result getPersonInfo(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        PersonInfoDTO personInfo = baseDao.getPersonInfo(params);
        if (personInfo == null) {
            return result.error("人员库中未找到此人员信息,请手动添加");
        }
        if ("1".equals(params.get("workertype").toString())) {
            personInfo.setIssuecardpicurl(personInfo.getHeadimageurl());
        } else {
            personInfo.setPhoto(personInfo.getHeadimageurl());
        }
        return result.ok(personInfo);
    }

    @Override
    public PageData<WorkerInfoDTO> getWorkerListByTm0101(Map<String, Object> params) {
        IPage<SysUserEntity> page = getPage(params, "", false);
        List<WorkerInfoDTO> list = ps02Dao.getList(params);
        return getPageData(list, page.getTotal(), WorkerInfoDTO.class);
    }

    /**
     * 获取人员信息状态
     * <p>
     * 0：未在当前项目查询到此人
     * 1：人员处于退场状态
     * 2：人员处于进场状态
     *
     * @param userId
     * @param params
     * @return
     */
    @Override
    public Result<BPs02WorkerStatusInfoDTO> getWorkerStatusInfo(Long userId, Map<String, Object> params) {

        BPs02WorkerStatusInfoDTO statusInfoDTO = new BPs02WorkerStatusInfoDTO();
        List<BPs02WorkerStatusDTO> workerStatusInfo = ps02Dao.getWorkerStatus(params);
        if (ObjectUtil.isNull(workerStatusInfo)) {
            statusInfoDTO.setResult("0");
            return new Result<BPs02WorkerStatusInfoDTO>().ok(statusInfoDTO);
        }
        BPs02WorkerStatusInfoDTO workerStatusCount = ps02Dao.getWorkerStatusCount(params);
        if (ObjectUtil.isNull(workerStatusCount)) {
            statusInfoDTO.setResult("1");
            return new Result<BPs02WorkerStatusInfoDTO>().ok(statusInfoDTO);
        } else {
            statusInfoDTO.setResult("2");
            statusInfoDTO.setUserType(workerStatusCount.getUserType());
            statusInfoDTO.setUserId(workerStatusCount.getUserId());
            return new Result<BPs02WorkerStatusInfoDTO>().ok(statusInfoDTO);
        }
    }

    /**
     * 获取项目基础信息
     *
     * @param userId
     * @param params
     * @return
     */
    @Override
    public Result<BPj01ProjectInfoDTO> getProjectInfo(Long userId, Map<String, Object> params) {

        return new Result<BPj01ProjectInfoDTO>().ok(pj01Dao.getProjectInfo(params));
    }

    /**
     * 获取班组信息
     *
     * @param userId
     * @param params
     * @return
     */
    @Override
    public Result<List<DictData>> getProjectTeamList(Long userId, Map<String, Object> params) {
        return new Result<List<DictData>>().ok(pj01Dao.getTeamCreditList(params));
    }

    /**
     * 获取人员信息
     *
     * @param userId
     * @param params
     * @return
     */
    @Override
    public Result<BPs02WorkerInfoDTO> getPs02WorkerInfo(Long userId, Map<String, Object> params) {

        return new Result<BPs02WorkerInfoDTO>().ok(ps02Dao.getPs02WorkerInfo(params));
    }

    /**
     * 项目获取人员信息
     *
     * @param userId
     * @param params
     * @return
     */
    @Override
    public Result<BPs02WorkerInfoDTO> getProjectPs02WorkerInfo(Long userId, Map<String, Object> params) {

        return new Result<BPs02WorkerInfoDTO>().ok(ps02Dao.getProjectPs02WorkerInfo(params));
    }

    private List<PersonDTO> getPersonIntoDevices(Long userId, String name, String imageUrl) {
        ArrayList<PersonDTO> personDTOList = new ArrayList<>();
        PersonDTO personDTO = new PersonDTO();
        personDTO.setUserId(userId);
        personDTO.setName(name);
        personDTO.setImageUrl(imageUrl);
        personDTOList.add(personDTO);
        return personDTOList;
    }
}
