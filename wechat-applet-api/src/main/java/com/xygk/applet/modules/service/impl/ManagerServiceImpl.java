package com.xygk.applet.modules.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xygk.applet.common.constant.CommonConstants;
import com.xygk.applet.modules.dao.*;
import com.xygk.applet.modules.dto.BOt01DTO;
import com.xygk.applet.modules.dto.Ps04DTO;
import com.xygk.applet.modules.dto.SysUserDTO;
import com.xygk.applet.modules.entity.*;
import com.xygk.applet.modules.service.BOt01Service;
import com.xygk.applet.modules.service.ManagerService;
import com.xygk.applet.modules.service.SupDeviceTaskService;
import com.xygk.applet.project.dto.ManagerInfoDTO;
import com.xygk.applet.supervision.dto.PersonDTO;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 通用方法
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ManagerServiceImpl extends CrudServiceImpl<SysUserDao, SysUserEntity, SysUserDTO> implements ManagerService {
    @Autowired
    private BPs01Dao ps01Dao;
    @Autowired
    private BPs02Dao ps02Dao;
    @Autowired
    private Ps03Dao ps03Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private BOt01Service ot01Service;
    @Autowired
    private SysDictTypeDao dictTypeDao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private SupDeviceTaskService taskService;
    @Autowired
    private Ps13Dao ps13Dao;

    @Override
    public QueryWrapper<SysUserEntity> getWrapper(Map<String, Object> params) {
        return null;
    }

    @Override
    public PageData<ManagerInfoDTO> getList(Map<String, Object> params) {
        IPage<SysUserEntity> page = getPage(params, "", false);
        List<ManagerInfoDTO> list = ps04Dao.getList(params);
        return getPageData(list, page.getTotal(), ManagerInfoDTO.class);
    }

    @Override
    public Result saveInfo(ManagerInfoDTO dto) {
        Result<Object> result = new Result<>();
        //处理人员基本信息
        String idcardnumber = dto.getIdcardnumber();
        //判断证件号码是否正确
        boolean validCard = IdcardUtil.isValidCard(idcardnumber);
        if (!validCard) {
            return result.error("证件号码不正确，请检查后重试！");
        }
        //处理性别
        int genderByIdCard = IdcardUtil.getGenderByIdCard(idcardnumber);
        dto.setGender(genderByIdCard == 1 ? "1" : "2");
        //处理民族
        //String nation = dictTypeDao.getNationByLabel(dto.getNation());
        //dto.setNation(nation);
        //处理出生日期
        dto.setBirthday(IdcardUtil.getBirthDate(dto.getIdcardnumber()));
        //处理籍贯
        dto.setAreacode(idcardnumber.substring(0, 6));
        //处理人员类别
        dto.setWorkertype("2");
        //处理头像
        dto.setHeadimageurl(dto.getPhoto());
        BPs01Entity ps01 = ps01Dao.selectOne(new QueryWrapper<BPs01Entity>().eq("IDCARDNUMBER", idcardnumber));
        if (ps01 == null) {
            ps01 = new BPs01Entity();
            BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01.setHeadimageurl(dto.getHeadimageurl());
            ps01Dao.insert(ps01);
        } else {
            BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
            ps01.setHeadimageurl(dto.getHeadimageurl());
            ps01Dao.updateById(ps01);
        }
        //校验人员在选择入场的企业在职信息
        Cp02Entity cp02 = cp02Dao.selectById(dto.getCp0201());
        //校验人员入场问题（本项目管理人员、工人，其他项目管理人员）
        checkPersonEnter(ps01.getPs0101(), dto.getPj0101(), cp02.getCp0101());
        Ps03Entity ps03 = ps03Dao.selectOne(new QueryWrapper<Ps03Entity>().eq("CP0101", cp02.getCp0101()).eq("PS0101", ps01.getPs0101()));
        if (ps03 == null) {
            ps03 = new Ps03Entity();
            ps03.setPj0101(dto.getPj0101());
            ps03.setCp0101(cp02.getCp0101());
            ps03.setPs0101(ps01.getPs0101());
            ps03.setManagestatus("1");
            ps03.setPhoto(dto.getPhoto());
            ps03Dao.insert(ps03);
        } else {
            ps03.setPj0101(dto.getPj0101());
            ps03.setPhoto(dto.getPhoto());
            ps03Dao.updateById(ps03);
        }
        dto.setPs0301(ps03.getPs0301());
        //处理管理人员信息
        Ps04Entity ps04 = new Ps04Entity();
        BeanUtil.copyProperties(dto, ps04, CopyOptions.create().setIgnoreNullValue(true));
        ps04.setEntrytime(dto.getEntrytime() == null ? new Date() : dto.getEntrytime());
        ps04.setInOrOut("1");
        ps04Dao.insert(ps04);
        //校验关键岗位
        Integer count = ps13Dao.selectCount(new QueryWrapper<Ps13Entity>()
                .eq("jobtype", dto.getJobtype())
                .eq("corptype", cp02.getCorptype()));
        if (count > 0) {
            if (dto.getCertificateFiles().size() > 0) {
                ot01Service.doFileRelation(dto.getCertificateFiles(), ps04.getPs0401());
            } else {
                return result.error("关键岗位证书文件不能为空！");
            }
        }
        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getManagerCertificateFiles())) {
            ot01Service.updateUnlessFiles("54", ps04.getPs0401());
            ot01Service.doFileRelation(dto.getManagerCertificateFiles(), ps04.getPs0401());
        }

        // 添加考勤设备下发待处理数据
        List<PersonDTO> personDTOList = this.getPersonIntoDevices(ps04.getPs0401(), ps01.getName(), ps04.getPhoto());
        taskService.personIntoDevicesByPj0101(personDTOList, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE, ps04.getPj0101());
        return result;
    }

    @Override
    public Result updateInfo(ManagerInfoDTO dto) {
        Result<Object> result = new Result<>();
        Pj01Entity pj01 = pj01Dao.selectById(dto.getPj0101());
        //处理人员基本信息
        BPs01Entity ps01 = ps01Dao.selectById(dto.getPs0101());
        if (!dto.getIdcardnumber().equals(ps01.getIdcardnumber())) {
            return result.error("检测到证件号码发生变更，不允许修改证件号码！");
        }
        BeanUtil.copyProperties(dto, ps01, CopyOptions.create().setIgnoreNullValue(true));
        int genderByIdCard = IdcardUtil.getGenderByIdCard(dto.getIdcardnumber());
        ps01.setGender(genderByIdCard == 1 ? "1" : "2");
        ps01.setHeadimageurl(dto.getPhoto());
        ps01Dao.updateById(ps01);
        //处理人员企业在职信息
        Cp02Entity cp02 = cp02Dao.selectById(dto.getCp0201());
        Ps03Entity ps03 = ps03Dao.selectOne(new QueryWrapper<Ps03Entity>().eq("CP0101", cp02.getCp0101()).eq("PS0101", ps01.getPs0101()));
        if (ps03 == null) {
            ps03 = new Ps03Entity();
            ps03.setPj0101(dto.getPj0101());
            ps03.setCp0101(cp02.getCp0101());
            ps03.setPs0101(ps01.getPs0101());
            ps03.setManagestatus("1");
            ps03.setPhoto(dto.getPhoto());
            ps03Dao.insert(ps03);
        } else {
            ps03.setPj0101(dto.getPj0101());
            ps03.setPhoto(dto.getPhoto());
            ps03Dao.updateById(ps03);
        }
        //处理管理人员信息
        Ps04Entity ps04 = ps04Dao.selectById(dto.getPs0401());
        Long cp0201 = ps04.getCp0201();
        String jobtype = ps04.getJobtype();
        BeanUtil.copyProperties(dto, ps04, CopyOptions.create().setIgnoreNullValue(true));
        ps04Dao.updateById(ps04);
        //校验关键岗位
        Integer count = ps13Dao.selectCount(new QueryWrapper<Ps13Entity>()
                .eq("jobtype", dto.getJobtype())
                .eq("corptype", cp02.getCorptype()));
        if (count > 0) {
            if (dto.getCertificateFiles().size() > 0) {
                ot01Service.updateUnlessFiles("50", dto.getPs0401());
                ot01Service.doFileRelation(dto.getCertificateFiles(), ps04.getPs0401());
            } else {
                return result.error("关键岗位证书文件不能为空！");
            }
        }
        if (!"0".equals(pj01.getStupstatus())) {
            if ("1".equals(ps04.getInOrOut())) {
                if (!cp0201.equals(dto.getCp0201()) || !jobtype.equals(dto.getJobtype())) {
                    throw new RenException("已上报省厅，不允许修改所属单位或岗位！请退场后修改重新入场！");
                }
            }
        }

        // 处理附件问题
        if (CollectionUtil.isNotEmpty(dto.getManagerCertificateFiles())) {
            ot01Service.updateUnlessFiles("54", ps04.getPs0401());
            ot01Service.doFileRelation(dto.getManagerCertificateFiles(), ps04.getPs0401());
        }
        return result;
    }

    @Override
    public Result getInfo(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        ManagerInfoDTO dto = ps04Dao.getInfo(Long.valueOf(params.get("ps0401").toString()));
        List<BOt01DTO> files = ot01Service.getBusinessData(dto.getPs0401(), "50");
        dto.setCertificateFiles(files);
        if (ObjectUtil.isNotNull(dto)) {
            List<BOt01DTO> managerCertificateFiles = ot01Service.getBusinessData(dto.getPs0401(), "54");
            dto.setManagerCertificateFiles(managerCertificateFiles);
        }
        return result.ok(dto);
    }

    @Override
    public Result entry(ManagerInfoDTO dto) {
        Result<Object> result = new Result<>();
        Long ps0401 = dto.getPs0401();
        //处理入场数据
        Ps04Entity ps04 = ps04Dao.selectById(ps0401);
        Pj01Entity pj01 = pj01Dao.selectById(ps04.getPj0101());
        //判断该人员是否已在场
        Long ps0101 = ps03Dao.selectById(ps04.getPs0301()).getPs0101();
        List<Ps04DTO> isPs04 = ps04Dao.selectIsPs04(ps0101, ps04.getPj0101());
        if (isPs04.size() > 0) {
            return result.error("该人员已入场，请勿重复入场！");
        }
        ps04.setEntrytime(new Date());
        Date exittime = ps04.getExittime();
        ps04.setExittime(null);
        ps04.setInOrOut("1");
        ps04Dao.updateById(ps04);
        if (!"0".equals(pj01.getStupstatus())) {
            //查询本项目是否存在该人员历史信息
            long hours = DateUtil.between(exittime, new Date(), DateUnit.HOUR);
            if (hours < 12) {
                throw new RenException("已上报省厅，" + (12 - hours) + "小时后才可重新入场该人员！");
            }
            Cp02Entity cp02 = cp02Dao.selectById(ps04.getCp0201());
            Cp01Entity cp01 = cp01Dao.selectById(cp02.getCp0101());
            BPs01Entity ps01 = ps01Dao.selectById(ps0101);
            //写入头像上报表
            Map<String, Object> headImageParams = new HashMap<>();
            Long att_id = ps04Dao.selectSeqAttInfo();
            headImageParams.put("ATT_ID", att_id);
            headImageParams.put("ATT_NAME", ps01.getPs0101() + ps04.getPs0401());
            headImageParams.put("FILE_PATH", ps04.getPhoto());
            ps04Dao.insertAttInfo(headImageParams);
            //写入管理人员上报表
            Map<String, Object> pmParams = new HashMap<>();
            pmParams.put("PJ0101", pj01.getPj0101());
            pmParams.put("CORP_NAME", cp01.getCorpname());
            pmParams.put("CORP_CODE", cp01.getCorpcode());
            pmParams.put("CORP_TYPE", cp02.getCorptype());
            pmParams.put("P_TYPE", ps04.getJobtype());
            pmParams.put("PM_NAME", ps01.getName());
            pmParams.put("PM_ID_CARD_NUMBER", ps01.getIdcardnumber());
            pmParams.put("PM_PHONE", ps01.getCellphone());
            pmParams.put("GRANT_ORG", ps01.getGrantorg());
            pmParams.put("NATION", ps01.getNation());
            pmParams.put("POLITICS_TYPE", ps01.getPoliticstype());
            pmParams.put("CULTURE_LEVEL_TYPE", ps01.getCultureleveltype());
            pmParams.put("ADDRESS", ps01.getAddress());
            pmParams.put("HEAD_IMAGE", att_id);
            pmParams.put("BUSIID", ps04.getPs0401());
            ps04Dao.insertProjectPmInfo(pmParams);
            //写入人员进场数据上报表
            Map<String, Object> inoutParams = new HashMap<>();
            inoutParams.put("PJ0101", pj01.getPj0101());
            inoutParams.put("ID_CARD_NUMBER", ps01.getIdcardnumber());
            inoutParams.put("OCCUR_TIME", new Date());
            ps04Dao.insertProjectWorkerInoutInfo(inoutParams);
        }
        //添加设备下发信息
        List<PersonDTO> personDTOS = ps04Dao.selectPersonByIds(CollectionUtil.toList(dto.getPs0401()));
        //人员注册、下发到设备
        taskService.personIntoDevicesByPj0101(personDTOS, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE, dto.getPj0101());
        return result;
    }

    @Override
    public Result exit(ManagerInfoDTO dto) {
        Result<Object> result = new Result<>();
        Long ps0401 = dto.getPs0401();
        //处理退场数据
        Ps04Entity ps04 = ps04Dao.selectById(ps0401);
        ps04.setExittime(new Date());
        ps04.setInOrOut("2");
        ps04Dao.updateById(ps04);
        //处理企业在职信息
        Ps03Entity ps03 = ps03Dao.selectById(ps04.getPs0301());
        ps03.setPj0101(null);
        ps03Dao.updateById(ps03);
        //写入设备删除人员任务
        List<PersonDTO> personDTOS = ps04Dao.selectPersonByIds(CollectionUtil.toList(ps0401));
        taskService.personIntoDevicesByPj0101(personDTOS, CommonConstants.PERSON_DELETE, CommonConstants.MANAGER_TYPE, dto.getPj0101());
        return result;
    }

    private void checkPersonEnter(Long ps0101, Long pj0101, Long cp0101) {
        //判断该人员是否在本项目工人列表中
        BPs02Entity ps02 = ps02Dao.selectOne(new QueryWrapper<BPs02Entity>().eq("PS0101", ps0101).eq("PJ0101", pj0101).eq("IN_OR_OUT", "1"));
        if (ps02 != null) {
            throw new RenException("该人员已在本项目工人队伍入场，请将工人退场后重试！");
        }
        //判断该人员是否在本项目管理人员列表中
        Long isps04 = ps04Dao.isInProject(ps0101, pj0101);
        if (isps04 > 0) {
            throw new RenException("该人员已存在本项目管理人员队伍中，请勿重复录入！");
        }
        //判断该人员是否在其他项目管理人员列表中
        Long isotherps04 = ps04Dao.isInOtherProject(ps0101, pj0101, cp0101);
        if (isotherps04 > 0) {
            throw new RenException("该人员已在其他项目,其他企业管理人员队伍中入场，请检查人员所属企业或联系项目退场后重试！");
        }
    }

    private List<PersonDTO> getPersonIntoDevices(Long userId, String name, String imageUrl) {
        ArrayList<PersonDTO> personDTOList = new ArrayList<>();
        PersonDTO personDTO = new PersonDTO();
        personDTO.setUserId(userId);
        personDTO.setName(name);
        personDTO.setImageUrl(imageUrl);
        personDTOList.add(personDTO);
        return personDTOList;
    }
}
