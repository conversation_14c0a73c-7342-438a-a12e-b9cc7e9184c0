package com.xygk.applet.modules.service.impl;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xygk.applet.modules.dao.*;
import com.xygk.applet.modules.dto.DictData;
import com.xygk.applet.modules.dto.DictType;
import com.xygk.applet.modules.dto.SysUserDTO;
import com.xygk.applet.modules.entity.Ps13Entity;
import com.xygk.applet.modules.entity.SysUserEntity;
import com.xygk.applet.modules.service.CommonService;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 通用方法
 *
 * <AUTHOR>
 * @since 1.0.0 2021-10-08
 */
@Service
public class CommonServiceImpl extends CrudServiceImpl<SysUserDao, SysUserEntity, SysUserDTO> implements CommonService {
    @Autowired
    private SysDictTypeDao dictTypeDao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Tm01Dao tm01Dao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Pa01Dao pa01Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Ps13Dao ps13Dao;


    @Override
    public QueryWrapper<SysUserEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<SysUserEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public List<DictType> getDictList() {
        List<DictType> typeList = dictTypeDao.getDictTypeList();
        List<DictData> dataList = dictTypeDao.getDictDataList();
        for (DictType type : typeList) {
            for (DictData data : dataList) {
                if (type.getId().equals(data.getDictTypeId())) {
                    type.getDataList().add(data);
                }
            }
        }
        return typeList;
    }

    @Override
    public List<DictData> getCompanyForProjectList(Map<String, Object> params) {
        List<DictData> list = cp02Dao.getCompanyForProjectList(params);
        return list;
    }

    @Override
    public List<DictData> getTeamForProjectList(Map<String, Object> params) {
        List<DictData> list = tm01Dao.getTeamForProjectList(params);
        return list;
    }

    @Override
    public List<String> getEnterpriseList() {
        List<String> list = cp01Dao.getEnterpriseList();
        return list;
    }

    @Override
    public List getProjectSupplement(Long pj0101) {
        ArrayList<Object> list = new ArrayList<>();
        //项目信息完善提示
        JSONObject object = new JSONObject();
        Long pj01Count = pj01Dao.getProjectSupplement(pj0101);
        if (pj01Count > 0) {
            object.put("supplementType", "1");
            object.put("description", "项目信息未完善");
            object.put("pj0101", pj0101);
            list.add(object);
        }
        //工资专户完善提示
        JSONObject object1 = new JSONObject();
        Long pa01Count = pa01Dao.getSpecialAccountSupplement(pj0101);
        if (pa01Count < 1) {
            object1.put("supplementType", "2");
            object1.put("description", "工资专户信息未完善");
            object1.put("pj0101", pj0101);
            list.add(object1);
        }
        //关键岗位人员完善提示
        List<Ps13Entity> ps13List = ps13Dao.selectList(null);
        for (Ps13Entity ps13 : ps13List) {
            JSONObject obj = new JSONObject();
            Long count = ps04Dao.getKeyJobCount(ps13.getJobtype(), ps13.getCorptype(), pj0101);
            if (count < 1) {
                obj.put("supplementType", ps13.getJobtype());
                obj.put("description", "关键岗位人员（" + ps13.getJobtypename() + "）未完善");
                obj.put("pj0101", pj0101);
                list.add(obj);
            }
        }
//        JSONObject object2 = new JSONObject();
//        Long ps04Count = ps04Dao.getManagerSupplement(pj0101);
//        if (ps04Count < 1) {
//            object2.put("supplementType", "3");
//            object2.put("description", "关键岗位人员（项目经理）未完善");
//            object2.put("pj0101", pj0101);
//            list.add(object2);
//        }
//        //关键岗位人员完善提示
//        JSONObject object3 = new JSONObject();
//        Long ps04SupplementCount = ps04Dao.getSupplement(pj0101);
//        if (ps04SupplementCount < 1) {
//            object3.put("supplementType", "4");
//            object3.put("description", "关键岗位人员（总监理工程师）未完善");
//            object3.put("pj0101", pj0101);
//            list.add(object3);
//        }

        return list;
    }

    @Override
    public List<DictData> getJobtypesByCorptype(String corptype) {
        if (!"7".equals(corptype) && !"8".equals(corptype) && !"9".equals(corptype)) {
            corptype = "99";
        }
        List<DictData> list = cp02Dao.getJobtypesByCorptype(corptype);
        return list;
    }

    @Override
    public Result queryPersonAge(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        JSONObject object = new JSONObject();
        object.put("flag", true);
        String idCardNumber = params.get("idCardNumber").toString();
        //验证身份证合法性
        boolean valid = IdcardUtil.isValidCard(idCardNumber);
        if (!valid) {
            return result.error("无效证件号码");
        }
        //获取人员性别
        int gender = IdcardUtil.getGenderByIdCard(idCardNumber);
        //获取人员年龄
        int age = IdcardUtil.getAgeByIdCard(idCardNumber);
        if (1 == gender) {
            if (age > 60) {
                object.put("flag", false);
                object.put("message", "该人员今年" + age + "岁，年龄大于60岁，是否继续录入？");
            }
        } else {
            if (age > 55) {
                object.put("flag", false);
                object.put("message", "该人员今年" + age + "岁，年龄大于55岁，是否继续录入？");
            }
        }
        return result.ok(object);
    }
}
