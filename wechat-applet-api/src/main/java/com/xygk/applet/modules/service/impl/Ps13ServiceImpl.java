package com.xygk.applet.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xygk.applet.modules.dao.Ps13Dao;
import com.xygk.applet.modules.dto.Ps13DTO;
import com.xygk.applet.modules.entity.Ps13Entity;
import com.xygk.applet.modules.service.Ps13Service;
import io.renren.common.service.impl.CrudServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 关键岗位配置表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-24
 */
@Service
public class Ps13ServiceImpl extends CrudServiceImpl<Ps13Dao, Ps13Entity, Ps13DTO> implements Ps13Service {

    @Override
    public QueryWrapper<Ps13Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps13Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}