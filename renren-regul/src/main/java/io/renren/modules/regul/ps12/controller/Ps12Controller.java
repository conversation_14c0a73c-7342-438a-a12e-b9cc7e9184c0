package io.renren.modules.regul.ps12.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.password.PasswordUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.admin.sys.dto.SysDeptDTO;
import io.renren.modules.admin.sys.dto.SysUserDTO;
import io.renren.modules.admin.sys.entity.SysDeptEntity;
import io.renren.modules.admin.sys.entity.SysUserEntity;
import io.renren.modules.admin.sys.service.SysDeptService;
import io.renren.modules.admin.sys.service.SysParamsService;
import io.renren.modules.admin.sys.service.SysUserService;
import io.renren.modules.regul.ps12.dto.Ps12DTO;
import io.renren.modules.regul.ps12.service.Ps12Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 项目负责人员表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-05-22
 */
@RestController
@RequestMapping("regul/ps12")
@Api(tags="项目负责人员表")
public class Ps12Controller {
    @Autowired
    private Ps12Service ps12Service;
    @Autowired
    private SysParamsService sysParamsService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysDeptService sysDeptService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("regul:ps12:page")
    public Result<PageData<Ps12DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps12DTO> page = ps12Service.pageList(params);

        return new Result<PageData<Ps12DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("regul:ps12:info")
    public Result<Ps12DTO> get(@PathVariable("id") Long id){
        Ps12DTO data = ps12Service.get(id);

        return new Result<Ps12DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("regul:ps12:save")
    public Result save(@RequestBody Ps12DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        SysUserDTO sysUserDTO = sysUserService.getByUsername(dto.getPs1203());
        if (ObjectUtil.isNotNull(sysUserDTO)) {
            return new Result().error("该手机号已使用");
        }

        Long deptId = ps12Service.savaDept(dto);
        dto.setDeptId(deptId);
        dto.setWhether("1");
        savaSysUserInfo(dto);
        SysUserDTO user = sysUserService.getByUsername(StrUtil.replace(PinyinUtil.getPinyin(dto.getPs1202()), " ", ""));
        dto.setUserId(user.getId());
        ps12Service.save(dto);

        return new Result();
    }

    private void savaSysUserInfo(Ps12DTO dto) {

        SysUserDTO sysUserDTO = new SysUserDTO();
        String password = "xygk@123";
        sysUserDTO.setRealName(dto.getPs1202());
        sysUserDTO.setGender(dto.getPs1204());
        sysUserDTO.setUsername(StrUtil.replace(PinyinUtil.getPinyin(dto.getPs1202()), " ", ""));
        sysUserDTO.setMobile(dto.getPs1203());
        sysUserDTO.setPassword(password);
        sysUserDTO.setDeptId(dto.getDeptId());
        sysUserDTO.setSuperAdmin(0);
        sysUserDTO.setStatus(1);
        sysUserDTO.setUserType("4");
        // 用户角色
        List collect = new ArrayList();
        collect.add(1925081453306892289L);
        sysUserDTO.setRoleIdList(collect);
        sysUserService.save(sysUserDTO);
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("regul:ps12:update")
    public Result update(@RequestBody Ps12DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        if ("0".equals(dto.getWhether())) {
            SysUserEntity user = sysUserService.selectById(dto.getUserId());
            user.setStatus(0);
            sysUserService.updateById(user);
        }
        ps12Service.update(dto);


        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("regul:ps12:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps12Service.delete(ids);

        return new Result();
    }


}