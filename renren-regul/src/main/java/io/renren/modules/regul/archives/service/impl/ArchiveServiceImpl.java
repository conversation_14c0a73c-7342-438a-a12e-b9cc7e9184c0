package io.renren.modules.regul.archives.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.utils.TimePeriodNode;
import io.renren.common.utils.TimePeriodTreeUtils;
import io.renren.common.utils.TreeUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.regul.archives.dao.ArchiveDao;
import io.renren.modules.regul.archives.dto.*;
import io.renren.modules.regul.archives.service.ArchiveService;
import io.renren.modules.regul.cg09.dao.Cg09Dao;
import io.renren.modules.regul.cg09.dto.Cg09DTO;
import io.renren.modules.regul.pj01.dao.Pj01Dao;
import io.renren.modules.regul.pj01.entity.Pj01Entity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class ArchiveServiceImpl extends BaseServiceImpl<ArchiveDao, ArchiveInfo> implements ArchiveService {
    @Autowired
    private ArchiveDao archiveDao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Cg09Dao cg09Dao;

    private static final String MONTH_TYPE = "1";
    private static final String FILE_TYPE = "703";


    @Override
    public PageData<PersonPage> personPageList(Map<String, Object> params) {
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<PersonPage> list = archiveDao.selectPersonPageList(params);
        return getPageData(list, page.getTotal(), PersonPage.class);
    }

    @Override
    public PageData<PartUnitPage> partUnitPageList(Map<String, Object> params) {
        if (ObjectUtil.isNotEmpty(params.get("corpType"))) {
            String corpType = (String) params.get("corpType");
            params.put("corpType", Arrays.asList(corpType.split(",")));
        }
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<PartUnitPage> list = archiveDao.selectPartUnitPageList(params);
        return getPageData(list, page.getTotal(), PartUnitPage.class);
    }

    @Override
    public PageData<TeamPage> teamPageList(Map<String, Object> params) {
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<TeamPage> list = archiveDao.selectTeamPageList(params);
        return getPageData(list, page.getTotal(), TeamPage.class);
    }

    @Override
    public PageData<FilePage> filePage(Map<String, Object> params) {
        params.put("fileType", FILE_TYPE);
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<FilePage> list = baseDao.selectFilePageList(params);
        return getPageData(list, page.getTotal(), FilePage.class);
    }

    @Override
    public List<Cg09DTO> getFileTree(Map<String, Object> params) {
        String pj0101 = (String) params.get("pj0101");
        Pj01Entity pj01Entity = pj01Dao.selectById(pj0101);
        Date startTime = ObjectUtil.isNull(pj01Entity.getStartdate()) ? new Date() : pj01Entity.getStartdate();
        Date endTime = (pj01Entity.getCompleteDate() != null && DateTime.now().isBefore(new DateTime(pj01Entity.getCompleteDate())))
                ? new Date()
                : (pj01Entity.getCompleteDate() != null ? pj01Entity.getCompleteDate() : new Date());
        List<Cg09DTO> list = cg09Dao.getFileTreeList(params);
        // 为 type 值为 1 的数据添加时间段树
        addTimePeriodTreeToTypeOne(list, startTime, endTime);
        return TreeUtils.build(list);
    }

    @Override
    public PageData<ProjectPage> projectPage(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<ArchiveInfo> page = getPage(params, "", false);
        List<ProjectPage> list = baseDao.selectProjectPageList(params);
        return getPageData(list, page.getTotal(), ProjectPage.class);
    }

    /**
     * 为 type 值为 1 的数据添加时间段树
     *
     * @param list 原始数据列表
     */
    private void addTimePeriodTreeToTypeOne(List<Cg09DTO> list, Date startTime, Date endTime) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 在扁平列表中找到所有 type 为 1 的节点
        List<Cg09DTO> typeOneNodes = new ArrayList<>();
        for (Cg09DTO dto : list) {
            if (MONTH_TYPE.equals(dto.getType())) {
                typeOneNodes.add(dto);
            }
        }

        // 为每个 type为1的节点添加时间段树
        for (Cg09DTO typeOneNode : typeOneNodes) {
            // 生成时间段树（使用传入的开始和结束时间）
            List<TimePeriodNode> timePeriodTree = TimePeriodTreeUtils.buildTimePeriodTree(
                    startTime, endTime);
            // 将 TimePeriodNode 转换为Cg09DTO并添加到原始列表中
            List<Cg09DTO> timePeriod = convertTimePeriodNodesToCg09Dto(timePeriodTree, typeOneNode.getId(), typeOneNode.getTreeLevel());
            list.addAll(timePeriod);
        }
    }

    /**
     * 将 TimePeriodNode 转换为 Cg09DTO
     *
     * @param timePeriodNodes 时间段节点列表
     * @param parentId        父节点ID
     * @param parentTreeLevel 父节点的treeLevel
     * @return 转换后的 Cg09DTO 列表
     */
    private List<Cg09DTO> convertTimePeriodNodesToCg09Dto(List<TimePeriodNode> timePeriodNodes, Long parentId, Integer parentTreeLevel) {
        List<Cg09DTO> result = new ArrayList<>();

        // 递归处理所有节点
        for (TimePeriodNode node : timePeriodNodes) {
            Cg09DTO dto = new Cg09DTO();

            // 生成稳定的确定性ID，确保相同输入总是产生相同ID
            Long nodeId = generateStableNodeId(parentId, node);

            // 设置基本属性
            dto.setId(nodeId);
            dto.setCg0901(nodeId);
            dto.setFileName(node.getDisplayName());

            // 根据节点类型设置不同的属性
            if (node.getNodeType() == TimePeriodNode.NodeType.YEAR) {
                dto.setPid(parentId);
                dto.setType("year");
                dto.setTreeLevel(parentTreeLevel != null ? parentTreeLevel + 1 : 4);
                // 年份倒序排列：使用负数，年份越大，sort值越小
                dto.setSort(-node.getYear());
            } else if (node.getNodeType() == TimePeriodNode.NodeType.MONTH) {
                dto.setPid(parentId);
                dto.setType("month");
                // 月份节点的TreeLevel应该比其父节点（年份节点）的TreeLevel多1
                dto.setTreeLevel(parentTreeLevel != null ? parentTreeLevel + 1 : 5);
                // 月份倒序排列：使用负数，月份越大，sort值越小
                dto.setSort(-node.getMonth());
            }

            result.add(dto);

            // 递归处理子节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                List<Cg09DTO> childDto = convertTimePeriodNodesToCg09Dto(node.getChildren(), nodeId, dto.getTreeLevel());
                result.addAll(childDto);
            }
        }

        return result;
    }

    /**
     * 生成稳定的确定性节点ID
     * 使用哈希算法确保相同的输入总是产生相同的ID，避免Long溢出问题
     *
     * @param parentId 父节点ID
     * @param node     时间段节点
     * @return 稳定的节点ID
     */
    private Long generateStableNodeId(Long parentId, TimePeriodNode node) {
        // 构建唯一标识字符串
        String uniqueKey;
        if (node.getNodeType() == TimePeriodNode.NodeType.YEAR) {
            // 年份节点：parentId + "_YEAR_" + year
            uniqueKey = parentId + "_YEAR_" + node.getYear();
        } else if (node.getNodeType() == TimePeriodNode.NodeType.MONTH) {
            // 月份节点：parentId + "_MONTH_" + year + "_" + month
            uniqueKey = parentId + "_MONTH_" + node.getYear() + "_" + node.getMonth();
        } else {
            // 如果节点类型未知，使用随机ID生成方式
            return IdUtil.getSnowflakeNextId();
        }

        // 使用字符串的hashCode生成稳定的ID
        // 为了避免负数和冲突，使用绝对值并确保在Long范围内
        long hash = Math.abs((long) uniqueKey.hashCode());

        // 为了进一步减少冲突概率，可以在hash基础上加上时间戳的低位
        // 但这里我们追求完全确定性，所以只使用hash

        // 确保ID为正数且在合理范围内（避免与现有业务ID冲突）
        // 使用一个较大的基数来区分时间段节点和普通业务节点
        return 9000000000000000L + (hash % 1000000000000000L);
    }
}
