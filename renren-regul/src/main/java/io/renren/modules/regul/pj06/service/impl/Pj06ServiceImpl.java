package io.renren.modules.regul.pj06.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constant.Constant;
import io.renren.common.generator.PasswordGenerator;
import io.renren.common.page.PageData;
import io.renren.common.password.PasswordUtils;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.message.service.SysSmsService;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.sys.dao.SysDeptDao;
import io.renren.modules.admin.sys.dao.SysRoleUserDao;
import io.renren.modules.admin.sys.dao.SysUserDao;
import io.renren.modules.admin.sys.entity.SysDeptEntity;
import io.renren.modules.admin.sys.entity.SysRoleUserEntity;
import io.renren.modules.admin.sys.entity.SysUserEntity;
import io.renren.modules.admin.sys.service.SysParamsService;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.regul.cp01.dao.Cp01Dao;
import io.renren.modules.regul.cp01.entity.Cp01Entity;
import io.renren.modules.regul.cp02.dao.Cp02Dao;
import io.renren.modules.regul.cp02.entity.Cp02Entity;
import io.renren.modules.regul.pj01.dao.Pj01Dao;
import io.renren.modules.regul.pj01.entity.Pj01Entity;
import io.renren.modules.regul.pj06.dao.Pj06Dao;
import io.renren.modules.regul.pj06.dto.Pj06DTO;
import io.renren.modules.regul.pj06.entity.Pj06Entity;
import io.renren.modules.regul.pj06.service.Pj06Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Pj06ServiceImpl extends CrudServiceImpl<Pj06Dao, Pj06Entity, Pj06DTO> implements Pj06Service {
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private SysSmsService smsService;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private SysDeptDao deptDao;
    @Autowired
    private SysUserDao userDao;
    @Autowired
    private SysRoleUserDao roleUserDao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private SysParamsService sysParamsService;

    @Override
    public QueryWrapper<Pj06Entity> getWrapper(Map<String, Object> params) {
        String name = (String) params.get("name");
        QueryWrapper<Pj06Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(name), "name", name);
        wrapper.orderByDesc("CREATE_DATE");
        return wrapper;
    }

    @Override
    public PageData<Pj06DTO> pageList(Map<String, Object> params) {
        // 分页
        IPage<Pj06Entity> page = getPage(params, "", false);
        // 查询
        List<Pj06DTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), Pj06DTO.class);
    }

    @Override
    public Pj06DTO getInfo(Map<String, Object> params) {
        Long pj0601 = Long.valueOf(params.get("pj0601").toString());
        Pj06DTO dto = baseDao.selectInfoById(pj0601);
        //查询三方主体单位
        Cp01Entity construct = cp01Dao.selectById(dto.getConstructcode());
        Cp01Entity contract = cp01Dao.selectById(dto.getContractcode());
        Cp01Entity supervise = cp01Dao.selectById(dto.getSupervisecode());
        dto.setConstructname(construct.getCorpname() + "—" + construct.getCorpcode());
        dto.setContractname(contract.getCorpname() + "—" + contract.getCorpcode());
        dto.setSupervisename(supervise.getCorpname() + "—" + supervise.getCorpcode());
        //查询项目注册附件
        List<Ot01DTO> registerFiles = ot01Service.loadBusinessData(pj0601, "09");
        dto.setRegisterFiles(registerFiles);
        return dto;
    }

    @Override
    public Result audit(Pj06DTO dto) {
        Result<Object> result = new Result<>();
        //保存审核结果
        dto.setAuditdate(new Date());
        dto.setAuditor(SecurityUser.getUser().getRealName());
        Pj06Entity pj06 = baseDao.selectById(dto.getPj0601());
//        pj06.setPs1201(dto.getPs1201());
        if (!"0".equals(pj06.getAuditstatus())) {
            return result.error("已审核，请勿重复审核");
        }
        //对通过操作做后续操作
        if ("1".equals(dto.getAuditstatus())) {
            //创建项目机构
            SysDeptEntity dept = createDept(pj06);
            //创建项目信息
            Pj01Entity pj01 = createPj01(pj06, dept);
            //创建项目账号
            //查询密码重置设置的密码
//            String password = sysParamsService.getValue(Constant.PASSWORD);
            String password = PasswordGenerator.generatePassword(Constant.PASSWORD_LENGTH);
            SysUserEntity user = createUser(pj06, dept, password);
            //关联用户与角色
            SysRoleUserEntity roleUser = new SysRoleUserEntity();
            roleUser.setUserId(user.getId());
            roleUser.setRoleId(1254300243697975297L);
            roleUserDao.insert(roleUser);
            //生成三方主体单位相关数据
            //生成建设单位相关数据
            createCompanys(pj06.getConstructcode(), "8", pj01.getPj0101());
            //生成总包单位相关数据
            createCompanys(pj06.getContractcode(), "9", pj01.getPj0101());
            //生成监理单位相关数据
            createCompanys(pj06.getSupervisecode(), "7", pj01.getPj0101());
            //维护项目机构关系
            Map<String, Object> paramMap = new HashMap<>(1);
            paramMap.put("Cpj0101", pj01.getPj0101());
            userDao.pcDealRPj01Dept(paramMap);
            //发送审核通过通知短信
            JSONObject jo = new JSONObject(2);
            jo.put("username", pj06.getLinkphone());
            jo.put("password", password);
            smsService.send("1001", pj06.getLinkphone(), jo.toJSONString());
            dto.setAuditresult("通过");
        } else {
            //发送审核失败通知短信
            JSONObject jo = new JSONObject(1);
            jo.put("notPassMsg", dto.getAuditresult());
            smsService.send("1002", pj06.getLinkphone(), jo.toJSONString());
        }
        BeanUtil.copyProperties(dto, pj06, CopyOptions.create().setIgnoreNullValue(true));
        baseDao.updateById(pj06);
        return result;
    }

    /**
     * 创建机构信息
     *
     * @param pj06
     * @return
     */
    private SysDeptEntity createDept(Pj06Entity pj06) {
        SysDeptEntity dept = new SysDeptEntity();
        dept.setPid(1067246875800000063L);
        dept.setName(pj06.getName());
        dept.setAreacode(pj06.getAreacode());
        deptDao.insert(dept);
        return dept;
    }

    /**
     * 创建项目信息
     *
     * @param pj06
     * @param dept
     * @return
     */
    private Pj01Entity createPj01(Pj06Entity pj06, SysDeptEntity dept) {
        Pj01Entity pj01 = new Pj01Entity();
        BeanUtil.copyProperties(pj06, pj01, CopyOptions.create().setIgnoreNullValue(true));
        pj01.setPj0101(pj06.getPj0601());
        pj01.setPrjstatus("3");
        pj01.setDeptId(dept.getId());
        pj01Dao.insert(pj01);
        return pj01;
    }

    /**
     * 创建用户信息
     *
     * @param pj06
     * @param dept
     * @return
     */
    private SysUserEntity createUser(Pj06Entity pj06, SysDeptEntity dept, String password) {
        SysUserEntity user = new SysUserEntity();
        user.setUsername(pj06.getLinkphone());
        user.setPassword(PasswordUtils.encode(password));
        user.setRealName(pj06.getLinkman());
        user.setMobile(pj06.getLinkphone());
        user.setDeptId(dept.getId());
        user.setSuperAdmin(0);
        user.setStatus(1);
        user.setUserType("1");
        userDao.insert(user);
        return user;
    }

    /**
     * 创建三方主体参建单位数据
     *
     * @param cp0101
     * @param corptype
     * @param pj0101
     */
    private void createCompanys(Long cp0101, String corptype, Long pj0101) {
        Cp02Entity cp02 = new Cp02Entity();
        cp02.setCp0101(cp0101);
        cp02.setPj0101(pj0101);
        cp02.setCorptype(corptype);
        cp02.setEntrytime(new Date());
        cp02.setInOrOut("1");
        cp02Dao.insert(cp02);
    }

}