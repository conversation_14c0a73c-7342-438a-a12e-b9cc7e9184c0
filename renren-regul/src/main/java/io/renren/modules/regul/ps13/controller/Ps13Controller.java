package io.renren.modules.regul.ps13.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.admin.sys.dto.SysDictDataDTO;
import io.renren.modules.admin.sys.service.SysDictDataService;
import io.renren.modules.regul.ps13.dto.Ps13DTO;
import io.renren.modules.regul.ps13.service.Ps13Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 关键岗位配置表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-24
 */
@RestController
@RequestMapping("regul/ps13")
@Api(tags="关键岗位配置表")
public class Ps13Controller {
    @Autowired
    private Ps13Service ps13Service;
    @Autowired
    private SysDictDataService sysDictDataService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("regul:ps13:page")
    public Result<PageData<Ps13DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<Ps13DTO> page = ps13Service.page(params);

        return new Result<PageData<Ps13DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("regul:ps13:info")
    public Result<Ps13DTO> get(@PathVariable("id") Long id){
        Ps13DTO data = ps13Service.get(id);

        return new Result<Ps13DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("regul:ps13:save")
    public Result save(@RequestBody Ps13DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        SysDictDataDTO dict = sysDictDataService.getByDictTypeAndValue("JOBTYPE", dto.getJobtype());
        dto.setJobtypename(dict.getDictLabel());
        ps13Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("regul:ps13:update")
    public Result update(@RequestBody Ps13DTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps13Service.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("regul:ps13:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps13Service.delete(ids);

        return new Result();
    }

}