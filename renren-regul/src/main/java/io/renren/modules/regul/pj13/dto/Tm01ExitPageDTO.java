package io.renren.modules.regul.pj13.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "班组退场分页")
public class Tm01ExitPageDTO {
    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "班组名称")
    private String teamname;
    @ApiModelProperty(value = "项目状态")
    private String prjstatus;
    @ApiModelProperty(value = "所属项目")
    private String projectName;
    @ApiModelProperty(value = "班组id")
    private Long tm0101;
    @ApiModelProperty(value = "负责人姓名")
    private String responsiblepersonname;
    @ApiModelProperty(value = "负责人联系电话")
    private String responsiblepersonphone;
    @ApiModelProperty(value = "负责人身份证")
    private String responsiblepersonidnumber;
    @ApiModelProperty(value = "入场时间")
    private Date entrytime;
    @ApiModelProperty(value = "退场时间")
    private Date exittime;
    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)")
    private String auditstatus;
    @ApiModelProperty(value = "审核失败原因")
    private String auditreason;
    @ApiModelProperty(value = "审核人")
    private String auditor;
}
