package io.renren.modules.regul.pj13.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.constant.Constant;
import io.renren.common.constants.CommonConstants;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.regul.pj13.dao.Pj13Dao;
import io.renren.modules.regul.pj13.dto.Pj13DTO;
import io.renren.modules.regul.pj13.dto.Ps02ExitPageDTO;
import io.renren.modules.regul.pj13.dto.Ps04ExitPageDTO;
import io.renren.modules.regul.pj13.dto.Tm01ExitPageDTO;
import io.renren.modules.regul.pj13.entity.Pj13Entity;
import io.renren.modules.regul.pj13.service.Pj13Service;
import io.renren.modules.regul.ps02.dao.Ps02Dao;
import io.renren.modules.regul.ps02.entity.Ps02Entity;
import io.renren.modules.regul.ps04.dao.Ps04Dao;
import io.renren.modules.regul.ps04.entity.Ps04Entity;
import io.renren.modules.regul.ps06.service.BPs06Service;
import io.renren.modules.regul.tm01.dao.Tm01Dao;
import io.renren.modules.regul.tm01.entity.Tm01Entity;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import io.renren.modules.supdevicetask.service.SupDeviceTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@Service
public class Pj13ServiceImpl extends CrudServiceImpl<Pj13Dao, Pj13Entity, Pj13DTO> implements Pj13Service {
    @Autowired
    private Ps02Dao ps02Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Tm01Dao tm01Dao;
    @Autowired
    private SupDeviceTaskService supDeviceTaskService;
    @Autowired
    private BPs06Service bPs06Service;

    @Override
    public QueryWrapper<Pj13Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pj13Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ps02ExitPageDTO> ps02ExitPage(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        params.put("deptId", SecurityUser.getDeptId());
        Page<Ps02ExitPageDTO> page = new Page<>(curPage, limit);
        List<Ps02ExitPageDTO> list = baseDao.ps02ExitPage(page, params);
        return getPageData(list, page.getTotal(), Ps02ExitPageDTO.class);
    }

    @Override
    public PageData<Ps04ExitPageDTO> ps04ExitPage(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        params.put("deptId", SecurityUser.getDeptId());
        Page<Ps04ExitPageDTO> page = new Page<>(curPage, limit);
        List<Ps04ExitPageDTO> list = baseDao.ps04ExitPage(page, params);
        return getPageData(list, page.getTotal(), Ps04ExitPageDTO.class);
    }

    @Override
    public PageData<Tm01ExitPageDTO> tm01ExitPage(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        params.put("deptId", SecurityUser.getDeptId());
        Page<Tm01ExitPageDTO> page = new Page<>(curPage, limit);
        List<Tm01ExitPageDTO> list = baseDao.tm01ExitPage(page, params);
        return getPageData(list, page.getTotal(), Tm01ExitPageDTO.class);
    }

    @Override
    public void audit(Pj13DTO dto) {
        Pj13Entity pj13 = baseDao.selectById(dto.getId());
        pj13.setAuditstatus(dto.getAuditstatus());
        pj13.setAuditreason(dto.getAuditreason());
        pj13.setAuditor(SecurityUser.getUser().getRealName());
        pj13.setAuditdate(new Date());

        baseDao.updateById(pj13);
        if ("1".equals(dto.getAuditstatus())) {
            switch (pj13.getExitType()) {
                case "1":
                    Ps02Entity ps02 = ps02Dao.selectById(pj13.getExitId());
                    exitPs02(pj13.getExitId(), ps02.getPj0101());
                    break;
                case "2":
                    Ps04Entity ps04 = ps04Dao.selectById(pj13.getExitId());
                    exitPs04(pj13.getExitId(), ps04.getPj0101());
                    break;
                case "3":
                    Tm01Entity tm01 = tm01Dao.selectById(pj13.getExitId());
                    exitTm01(pj13.getExitId(), tm01.getPj0101());
                    break;
                default:
            }
        }
    }

    private void exitPs02(Long ps0201, Long pj0101) {
        List<Long> longs = new ArrayList<>();
        longs.add(ps0201);
        ps02Dao.updateInOrOutByIds(longs, "2");

        // 进退场记录
        bPs06Service.insertInOrOut(ps0201, "2");
        List<PersonDTO> personDTOList = ps02Dao.selectPersonByIds(longs);

        supDeviceTaskService.personIntoDevicesByPj0101(personDTOList, CommonConstants.PERSON_DELETE, CommonConstants.WORKER_TYPE, pj0101);
    }

    private void exitPs04(Long ps0401, Long pj0101) {
        List<Long> longs = new ArrayList<>();
        longs.add(ps0401);
        ps04Dao.updateInOrOutByIds(longs, "2");
        ps04Dao.exitPs03ByIds(longs);

        List<PersonDTO> personDTOList = ps04Dao.selectPersonByIds(longs);

        supDeviceTaskService.personIntoDevicesByPj0101(personDTOList, CommonConstants.PERSON_DELETE, CommonConstants.WORKER_TYPE, pj0101);
    }

    private void exitTm01(Long tm0101, Long pj0101) {
        // 如果班组下有未退场的工人，则无法退场班组
        long count = ps02Dao.countPs02ByTm0101(tm0101);
        if (count > 0) {
            throw new RenException("该班组下有未退场的工人，请先退场工人");
        }
        List<Long> longs = new ArrayList<>();
        longs.add(tm0101);
        tm01Dao.teamOutByIds(longs);
//        List<PersonDTO> personDTOList = tm01Dao.selectPersonByTeamIds(longs);
//
//        supDeviceTaskService.personIntoDevicesByPj0101(personDTOList, CommonConstants.PERSON_DELETE, CommonConstants.WORKER_TYPE, pj0101);
    }
}