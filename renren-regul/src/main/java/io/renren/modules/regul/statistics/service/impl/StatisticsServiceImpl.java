package io.renren.modules.regul.statistics.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.regul.pj01.dao.Pj01Dao;
import io.renren.modules.regul.pj01.dto.Pj01DTO;
import io.renren.modules.regul.pj01.entity.Pj01Entity;
import io.renren.modules.regul.pj01.handler.Pj01ExportInfoHandler;
import io.renren.modules.regul.statistics.dto.*;
import io.renren.modules.regul.statistics.service.StatisticsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工人工资单
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Service
public class StatisticsServiceImpl extends CrudServiceImpl<Pj01Dao, Pj01Entity, Pj01DTO> implements StatisticsService {

    @Autowired
    private Pj01ExportInfoHandler pj01ExportInfoHandler;

    @Value("${expFile.attendance3Export}")
    private String ATTENDANCE3_EXPORT;
    @Value("${expFile.attendance32Export}")
    private String ATTENDANCE32_EXPORT;
    @Value("${expFile.attendance4Export}")
    private String ATTENDANCE4_EXPORT;
    @Value("${expFile.attendance42Export}")
    private String ATTENDANCE42_EXPORT;
    @Value("${expFile.attendance5Export}")
    private String ATTENDANCE5_EXPORT;
    @Value("${expFile.attendance52Export}")
    private String ATTENDANCE52_EXPORT;

    @Override
    public QueryWrapper<Pj01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pj01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public Result areaStatistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        List<AreaStatisticsDTO> list = baseDao.areaStatistics(params);
        return result.ok(list);
    }


    @Override
    public Result workerStatistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params.put("deptId", SecurityUser.getDeptId());
        List<WorkerStatisticsDTO> list = baseDao.workerStatistics(params);
        return result.ok(list);
    }

    @Override
    public PageData<Attendance1StatisticsDTO> attendance1Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        params.put("deptId", SecurityUser.getDeptId());
        // 从请求参数中获取值
        String tjdateYear = params.get("tjdateyear") != null ? params.get("tjdateyear").toString() : null;
        String tjdateMonth = params.get("tjdatemonth") != null ? params.get("tjdatemonth").toString() : null;
        String tjdateDay = params.get("tjdate") != null ? params.get("tjdate").toString() : null;

        // 调用方法生成 tjdate 参数
        String tjdate = assembleDateParam(tjdateYear, tjdateMonth, tjdateDay);

        // 将拼接后的 tjdate 放入参数中
        params.put("tjdate", tjdate);
        List<Attendance1StatisticsDTO> list = baseDao.attendance1Statistics(params);
        return getPageData(list, page.getTotal(), Attendance1StatisticsDTO.class);
    }

    @Override
    public PageData<Attendance2StatisticsDTO> attendance2Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        params.put("deptId", SecurityUser.getDeptId());
        List<Attendance2StatisticsDTO> list = baseDao.attendance2Statistics(params);
        return getPageData(list, page.getTotal(), Attendance2StatisticsDTO.class);
    }

    @Override
    public Result attendance3Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        List<Attendance3StatisticsDTO> list = baseDao.attendance3Statistics(params);
        return result.ok(list);
    }

    @Override
    public void exportAttendance3(Map<String, Object> params, HttpServletResponse response) throws IOException {
        File file = new File(ATTENDANCE3_EXPORT);
        if (!file.exists()) {
            throw new IOException("配置的模板文件不存在");
        }
        //查询导出数据
        params = getAreacodeParams(params);
        List<Attendance3StatisticsDTO> list = baseDao.attendance3Statistics(params);
        String description = baseDao.attendance31Statistics(params);
        TemplateExportParams exportParams = new TemplateExportParams(ATTENDANCE3_EXPORT);
        Map<String, Object> data = new HashMap<>();
        String tjdate = (String) params.get("tjdate");
        if (StringUtils.isBlank(tjdate)) {
            throw new IOException("请选择导出时间");
        }
        tjdate = DateUtil.format(DateUtil.parse(tjdate), "yyyy年MM月dd日");
        data.put("list", list);
        data.put("tjdate", tjdate);
        data.put("description", description);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(tjdate + "更新率统计", "UTF-8") + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    @Override
    public Result attendance31Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        String description = baseDao.attendance31Statistics(params);
        return result.ok(description);
    }

    @Override
    public PageData<Attendance32StatisticsDTO> attendance32Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        //params = getAreacodeParams(params);

        // 从请求参数中获取值
        String tjdateYear = params.get("tjdateyear") != null ? params.get("tjdateyear").toString() : null;
        String tjdateMonth = params.get("tjdatemonth") != null ? params.get("tjdatemonth").toString() : null;
        String tjdateDay = params.get("tjdate") != null ? params.get("tjdate").toString() : null;

        // 调用方法生成 tjdate 参数
        String tjdate = assembleDateParam(tjdateYear, tjdateMonth, tjdateDay);

        // 将拼接后的 tjdate 放入参数中
        params.put("tjdate", tjdate);

        List<Attendance32StatisticsDTO> list = baseDao.attendance32Statistics(params);
        return getPageData(list, page.getTotal(), Attendance32StatisticsDTO.class);
    }

    @Override
    public void exportAttendance32(Map<String, Object> params, HttpServletResponse response) throws IOException {
        File file = new File(ATTENDANCE32_EXPORT);
        if (!file.exists()) {
            throw new IOException("配置的模板文件不存在");
        }
        //查询导出数据
        //params = getAreacodeParams(params);
        List<Attendance32StatisticsDTO> list = baseDao.attendance32Statistics(params);
        TemplateExportParams exportParams = new TemplateExportParams(ATTENDANCE32_EXPORT);
        Map<String, Object> data = new HashMap<>();
        String tjdate = (String) params.get("tjdate");
        if (StringUtils.isBlank(tjdate)) {
            throw new IOException("请选择导出时间");
        }
        tjdate = DateUtil.format(DateUtil.parse(tjdate), "yyyy年MM月dd日");
        data.put("list", list);
        data.put("tjdate", tjdate);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(tjdate + "更新率统计-详情", "UTF-8") + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    @Override
    public Result attendance4Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        List<Attendance4StatisticsDTO> list = baseDao.attendance4Statistics(params);
        return result.ok(list);
    }

    @Override
    public void exportAttendance4(Map<String, Object> params, HttpServletResponse response) throws IOException {
        File file = new File(ATTENDANCE4_EXPORT);
        if (!file.exists()) {
            throw new IOException("配置的模板文件不存在");
        }
        //查询导出数据
        params = getAreacodeParams(params);
        List<Attendance4StatisticsDTO> list = baseDao.attendance4Statistics(params);
        String description = baseDao.attendance41Statistics(params);
        TemplateExportParams exportParams = new TemplateExportParams(ATTENDANCE4_EXPORT);
        Map<String, Object> data = new HashMap<>();
        String tjdate = (String) params.get("tjdate");
        if (StringUtils.isBlank(tjdate)) {
            throw new IOException("请选择导出时间");
        }
        tjdate = DateUtil.format(DateUtil.parse(tjdate), "yyyy年MM月dd日");
        data.put("list", list);
        data.put("tjdate", tjdate);
        data.put("description", description);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(tjdate + "到岗率统计", "UTF-8") + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    @Override
    public Result attendance41Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        String description = baseDao.attendance41Statistics(params);
        return result.ok(description);
    }

    @Override
    public PageData<Attendance42StatisticsDTO> attendance42Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        //params = getAreacodeParams(params);
        // 从请求参数中获取值
        String tjdateYear = params.get("tjdateyear") != null ? params.get("tjdateyear").toString() : null;
        String tjdateMonth = params.get("tjdatemonth") != null ? params.get("tjdatemonth").toString() : null;
        String tjdateDay = params.get("tjdate") != null ? params.get("tjdate").toString() : null;

        // 调用方法生成 tjdate 参数
        String tjdate = assembleDateParam(tjdateYear, tjdateMonth, tjdateDay);

        // 将拼接后的 tjdate 放入参数中
        params.put("tjdate", tjdate);

        List<Attendance42StatisticsDTO> list = baseDao.attendance42Statistics(params);
        return getPageData(list, page.getTotal(), Attendance42StatisticsDTO.class);
    }

    @Override
    public void exportAttendance42(Map<String, Object> params, HttpServletResponse response) throws IOException {
        File file = new File(ATTENDANCE42_EXPORT);
        if (!file.exists()) {
            throw new IOException("配置的模板文件不存在");
        }
        //查询导出数据
        //params = getAreacodeParams(params);
        List<Attendance42StatisticsDTO> list = baseDao.attendance42Statistics(params);
        TemplateExportParams exportParams = new TemplateExportParams(ATTENDANCE42_EXPORT);
        Map<String, Object> data = new HashMap<>();
        String tjdate = (String) params.get("tjdate");
        if (StringUtils.isBlank(tjdate)) {
            throw new IOException("请选择导出时间");
        }
        tjdate = DateUtil.format(DateUtil.parse(tjdate), "yyyy年MM月dd日");
        data.put("list", list);
        data.put("tjdate", tjdate);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(tjdate + "到岗率统计-详情", "UTF-8") + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    @Override
    public PageData<Attendance43StatisticsDTO> attendance43Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        DateTime start = DateUtil.parse(startdate, "yyyy-MM-dd");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM-dd");
        if (DateUtil.between(start, end, DateUnit.DAY) > 31) {
            throw new RenException("选择时间请勿超过31天！");
        }
        List<Attendance43StatisticsDTO> list = baseDao.attendance43Statistics(params);
        for (Attendance43StatisticsDTO dto : list) {
            List<AttendanceDayDTO> kqlist = baseDao.getAttendanceDayList(dto.getUserId(), startdate, enddate);
            dto.setKqlist(kqlist);
        }
        return getPageData(list, page.getTotal(), Attendance43StatisticsDTO.class);
    }

    @Override
    public PageData<Attendance44StatisticsDTO> attendance44Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        DateTime start = DateUtil.parse(startdate, "yyyy-MM");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM");
        if (DateUtil.between(start, end, DateUnit.WEEK) > 156) {
            throw new RenException("选择时间请勿超过3年！");
        }
        List<Attendance44StatisticsDTO> list = baseDao.attendance44Statistics(params);
        for (Attendance44StatisticsDTO dto : list) {
            List<AttendanceMonthDTO> kqlist = baseDao.getAttendanceMonthList(dto.getUserId(), startdate, enddate);
            dto.setKqlist(kqlist);
        }
        return getPageData(list, page.getTotal(), Attendance44StatisticsDTO.class);
    }

    @Override
    public void exportAttendance44(Map<String, Object> params, HttpServletResponse response) throws IOException {
        //查询导出数据
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        DateTime start = DateUtil.parse(startdate, "yyyy-MM");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM");
        if (DateUtil.between(start, end, DateUnit.WEEK) > 156) {
            throw new RenException("选择时间请勿超过3年！");
        }
        List<Attendance44StatisticsDTO> datalist = baseDao.attendance44Statistics(params);
        for (Attendance44StatisticsDTO dto : datalist) {
            List<AttendanceMonthDTO> kqlist = baseDao.getAttendanceMonthList(dto.getUserId(), startdate, enddate);
            dto.setKqlist(kqlist);
        }
        ArrayList<ExcelExportEntity> colList = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("序号", "sno");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("姓名", "name");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("证件号码", "idcardnumber");
        colEntity.setNeedMerge(false);
        colEntity.setWidth(30);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("参建单位", "corpname");
        colEntity.setWidth(30);
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("岗位", "jobtype");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("考勤天数", "kqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("统计天数", "days");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("到岗率", "arrivepercent");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        ExcelExportEntity dateColGroup = new ExcelExportEntity("考勤日期", "kqlist");
        List<ExcelExportEntity> dateColList = new ArrayList<ExcelExportEntity>();
        for (AttendanceMonthDTO dto : datalist.get(0).getKqlist()) {
            dateColList.add(new ExcelExportEntity(dto.getMonth(), dto.getMonth()));
        }
        dateColGroup.setList(dateColList);
        colList.add(dateColGroup);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        //存储每一行中的日期数据
        List<Map<String, Object>> dataListChild = null;
        //存储表格中的每一行数据
        Map<String, Object> valMap = null;
        for (Attendance44StatisticsDTO dto : datalist) {
            valMap = new HashMap(12);
            valMap.put("sno", dto.getSno());
            valMap.put("name", dto.getName());
            valMap.put("idcardnumber", dto.getIdcardnumber());
            valMap.put("corpname", dto.getCorpname());
            valMap.put("jobtype", dto.getJobtype());
            valMap.put("kqs", dto.getKqs());
            valMap.put("days", dto.getDays());
            valMap.put("todaykqs", dto.getTodaykqs());
            valMap.put("arrivepercent", dto.getArrivepercent());
            dataListChild = new ArrayList<>();
            Map<String, Object> dateMap = new HashMap<String, Object>();
            for (AttendanceMonthDTO attendanceMonthDTO : dto.getKqlist()) {
                dateMap.put(attendanceMonthDTO.getMonth(), attendanceMonthDTO.getKqs());
            }
            dataListChild.add(dateMap);
            valMap.put("kqlist", dataListChild);
            list.add(valMap);
        }
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("管理人员到岗详情统计", "UTF-8") + ".xls");
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(startdate + "——" + enddate + "管理人员到岗详情统计", "数据"), colList,
                list);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        workbook.close();
    }
    @Override
    public void exportAttendance44SJ(Map<String, Object> params, HttpServletResponse response) throws IOException {
        //查询导出数据
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        DateTime start = DateUtil.parse(startdate, "yyyy-MM");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM");
        if (DateUtil.between(start, end, DateUnit.WEEK) > 156) {
            throw new RenException("选择时间请勿超过3年！");
        }
        List<Attendance44StatisticsDTO> datalist = baseDao.attendance44SJStatistics(params);
        for (Attendance44StatisticsDTO dto : datalist) {
            List<AttendanceMonthDTO> kqlist = baseDao.getAttendanceMonthList(dto.getUserId(), startdate, enddate);
            dto.setKqlist(kqlist);
        }
        ArrayList<ExcelExportEntity> colList = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("序号", "sno");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("项目名称", "projectname");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("姓名", "name");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("证件号码", "idcardnumber");
        colEntity.setNeedMerge(false);
        colEntity.setWidth(30);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("参建单位", "corpname");
        colEntity.setWidth(30);
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("岗位", "jobtype");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("考勤天数", "kqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("统计天数", "days");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("到岗率", "arrivepercent");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        ExcelExportEntity dateColGroup = new ExcelExportEntity("考勤日期", "kqlist");
        List<ExcelExportEntity> dateColList = new ArrayList<ExcelExportEntity>();
        for (AttendanceMonthDTO dto : datalist.get(0).getKqlist()) {
            dateColList.add(new ExcelExportEntity(dto.getMonth(), dto.getMonth()));
        }
        dateColGroup.setList(dateColList);
        colList.add(dateColGroup);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        //存储每一行中的日期数据
        List<Map<String, Object>> dataListChild = null;
        //存储表格中的每一行数据
        Map<String, Object> valMap = null;
        for (Attendance44StatisticsDTO dto : datalist) {
            valMap = new HashMap(12);
            valMap.put("sno", dto.getSno());
            valMap.put("projectname", dto.getProjectname());
            valMap.put("name", dto.getName());
            valMap.put("idcardnumber", dto.getIdcardnumber());
            valMap.put("corpname", dto.getCorpname());
            valMap.put("jobtype", dto.getJobtype());
            valMap.put("kqs", dto.getKqs());
            valMap.put("days", dto.getDays());
            valMap.put("todaykqs", dto.getTodaykqs());
            valMap.put("arrivepercent", dto.getArrivepercent());
            dataListChild = new ArrayList<>();
            Map<String, Object> dateMap = new HashMap<String, Object>();
            for (AttendanceMonthDTO attendanceMonthDTO : dto.getKqlist()) {
                dateMap.put(attendanceMonthDTO.getMonth(), attendanceMonthDTO.getKqs());
            }
            dataListChild.add(dateMap);
            valMap.put("kqlist", dataListChild);
            list.add(valMap);
        }
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("管理人员到岗详情统计", "UTF-8") + ".xls");
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(startdate + "——" + enddate + "管理人员到岗详情统计", "数据"), colList,
                list);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        workbook.close();
    }
    @Override
    public JSONObject attendance45Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        Long userId = Long.valueOf(params.get("userId").toString());
        DateTime start = DateUtil.parse(startdate, "yyyy-MM");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM");
        if (DateUtil.between(start, end, DateUnit.WEEK) > 156) {
            throw new RenException("选择时间请勿超过3年！");
        }
        JSONObject object = new JSONObject();
        String description = baseDao.attendance45Description(params);
        object.put("description", description);
        List<Attendance45StatisticsDTO> list = baseDao.attendance45Statistics(params);
        for (Attendance45StatisticsDTO dto : list) {
            List<AttendanceDayDTO> kqlist = baseDao.getAttendanceDayList(userId, dto.getMonth() + "-01", dto.getMonth() + "-31");
            dto.setKqlist(kqlist);
        }
        object.put("list", list);
        return object;
    }

    @Override
    public void exportAttendance45(Map<String, Object> params, HttpServletResponse response) throws IOException {
        //查询导出数据
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        Long userId = Long.valueOf(params.get("userId").toString());
        DateTime start = DateUtil.parse(startdate, "yyyy-MM");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM");
        if (DateUtil.between(start, end, DateUnit.WEEK) > 156) {
            throw new RenException("选择时间请勿超过3年！");
        }
        List<Attendance45StatisticsDTO> datalist = baseDao.attendance45Statistics(params);
        for (Attendance45StatisticsDTO dto : datalist) {
            List<AttendanceDayDTO> kqlist = baseDao.getAttendanceDayList(userId, dto.getMonth() + "-01", dto.getMonth() + "-31");
            dto.setKqlist(kqlist);
        }
        ArrayList<ExcelExportEntity> colList = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("月份", "month");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("考勤天数", "kqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("选择天数", "days");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("到岗率", "arrivepercent");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        ExcelExportEntity dateColGroup = new ExcelExportEntity("考勤日期", "kqlist");
        List<ExcelExportEntity> dateColList = new ArrayList<ExcelExportEntity>();
        for (AttendanceDayDTO dto : datalist.get(0).getKqlist()) {
            dateColList.add(new ExcelExportEntity(dto.getDay(), dto.getDay()));
        }
        dateColGroup.setList(dateColList);
        colList.add(dateColGroup);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        //存储每一行中的日期数据
        List<Map<String, Object>> dataListChild = null;
        //存储表格中的每一行数据
        Map<String, Object> valMap = null;
        for (Attendance45StatisticsDTO dto : datalist) {
            valMap = new HashMap(12);
            valMap.put("month", dto.getMonth());
            valMap.put("kqs", dto.getKqdays());
            valMap.put("days", dto.getDays());
            valMap.put("arrivepercent", dto.getArrivepercent());
            dataListChild = new ArrayList<>();
            Map<String, Object> dateMap = new HashMap<String, Object>();
            for (AttendanceDayDTO attendanceDayDTO : dto.getKqlist()) {
                dateMap.put(attendanceDayDTO.getDay(), attendanceDayDTO.getIskq());
            }
            dataListChild.add(dateMap);
            valMap.put("kqlist", dataListChild);
            list.add(valMap);
        }
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("管理人员到岗详情统计", "UTF-8") + ".xls");
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(startdate + "——" + enddate + "管理人员到岗详情统计", "数据"), colList,
                list);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        workbook.close();
    }

    @Override
    public void exportAttendance43(Map<String, Object> params, HttpServletResponse response) throws IOException {
        //查询导出数据
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        DateTime start = DateUtil.parse(startdate, "yyyy-MM-dd");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM-dd");
        if (DateUtil.between(start, end, DateUnit.DAY) > 31) {
            throw new RenException("选择时间请勿超过31天！");
        }
        List<Attendance43StatisticsDTO> datalist = baseDao.attendance43Statistics(params);
        for (Attendance43StatisticsDTO dto : datalist) {
            List<AttendanceDayDTO> kqlist = baseDao.getAttendanceDayList(dto.getUserId(), startdate, enddate);
            dto.setKqlist(kqlist);
        }
        ArrayList<ExcelExportEntity> colList = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("序号", "sno");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("项目名称", "projectname");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("姓名", "name");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("证件号码", "idcardnumber");
        colEntity.setNeedMerge(false);
        colEntity.setWidth(30);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("参建单位", "corpname");
        colEntity.setWidth(30);
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("岗位", "jobtype");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("考勤天数", "kqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("选择天数", "days");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("今日考勤", "todaykqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("到岗率", "arrivepercent");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        ExcelExportEntity dateColGroup = new ExcelExportEntity("考勤日期", "kqlist");
        List<ExcelExportEntity> dateColList = new ArrayList<ExcelExportEntity>();
        for (AttendanceDayDTO dto : datalist.get(0).getKqlist()) {
            dateColList.add(new ExcelExportEntity(dto.getDay(), dto.getDay()));
        }
        dateColGroup.setList(dateColList);
        colList.add(dateColGroup);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        //存储每一行中的日期数据
        List<Map<String, Object>> dataListChild = null;
        //存储表格中的每一行数据
        Map<String, Object> valMap = null;
        for (Attendance43StatisticsDTO dto : datalist) {
            valMap = new HashMap(12);
            valMap.put("sno", dto.getSno());
            valMap.put("projectname", dto.getProjectname());
            valMap.put("name", dto.getName());
            valMap.put("idcardnumber", dto.getIdcardnumber());
            valMap.put("corpname", dto.getCorpname());
            valMap.put("jobtype", dto.getJobtype());
            valMap.put("kqs", dto.getKqs());
            valMap.put("days", dto.getDays());
            valMap.put("todaykqs", dto.getTodaykqs());
            valMap.put("arrivepercent", dto.getArrivepercent());
            dataListChild = new ArrayList<>();
            Map<String, Object> dateMap = new HashMap<String, Object>();
            for (AttendanceDayDTO attendanceDayDTO : dto.getKqlist()) {
                dateMap.put(attendanceDayDTO.getDay(), attendanceDayDTO.getIskq());
            }
            dataListChild.add(dateMap);
            valMap.put("kqlist", dataListChild);
            list.add(valMap);
        }
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("管理人员到岗详情统计", "UTF-8") + ".xls");
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(startdate + "——" + enddate + "管理人员到岗详情统计（标记说明：√-到岗，△-请假）", "数据"), colList,
                list);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        workbook.close();
    }

    @Override
    public void exportAttendance43CP(Map<String, Object> params, HttpServletResponse response) throws IOException {
        //查询导出数据
        String startdate = params.get("startdate").toString();
        String enddate = params.get("enddate").toString();
        DateTime start = DateUtil.parse(startdate, "yyyy-MM-dd");
        DateTime end = DateUtil.parse(enddate, "yyyy-MM-dd");
        if (DateUtil.between(start, end, DateUnit.DAY) > 31) {
            throw new RenException("选择时间请勿超过31天！");
        }
        List<Attendance43StatisticsDTO> datalist = baseDao.attendance43CPStatistics(params);
        for (Attendance43StatisticsDTO dto : datalist) {
            List<AttendanceDayDTO> kqlist = baseDao.getAttendanceDayList(dto.getUserId(), startdate, enddate);
            dto.setKqlist(kqlist);
        }
        ArrayList<ExcelExportEntity> colList = new ArrayList<>();
        ExcelExportEntity colEntity = new ExcelExportEntity("序号", "sno");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("项目名称", "projectname");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("姓名", "name");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("证件号码", "idcardnumber");
        colEntity.setNeedMerge(false);
        colEntity.setWidth(30);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("参建单位", "corpname");
        colEntity.setWidth(30);
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("单位类型", "corptype");
        colEntity.setWidth(30);
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("岗位", "jobtype");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("考勤天数", "kqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("施工天数", "days");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("今日考勤", "todaykqs");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        colEntity = new ExcelExportEntity("到岗率", "arrivepercent");
        colEntity.setNeedMerge(false);
        colList.add(colEntity);
        ExcelExportEntity dateColGroup = new ExcelExportEntity("考勤日期", "kqlist");
        List<ExcelExportEntity> dateColList = new ArrayList<ExcelExportEntity>();
        for (AttendanceDayDTO dto : datalist.get(0).getKqlist()) {
            dateColList.add(new ExcelExportEntity(dto.getDay(), dto.getDay()));
        }
        dateColGroup.setList(dateColList);
        colList.add(dateColGroup);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        //存储每一行中的日期数据
        List<Map<String, Object>> dataListChild = null;
        //存储表格中的每一行数据
        Map<String, Object> valMap = null;
        for (Attendance43StatisticsDTO dto : datalist) {
            valMap = new HashMap(12);
            valMap.put("sno", dto.getSno());
            valMap.put("projectname", dto.getProjectname());
            valMap.put("name", dto.getName());
            valMap.put("idcardnumber", dto.getIdcardnumber());
            valMap.put("corpname", dto.getCorpname());
            valMap.put("corptype", dto.getCorptype());
            valMap.put("jobtype", dto.getJobtype());
            valMap.put("kqs", dto.getKqs());
            valMap.put("days", dto.getDays());
            valMap.put("todaykqs", dto.getTodaykqs());
            valMap.put("arrivepercent", dto.getArrivepercent());
            dataListChild = new ArrayList<>();
            Map<String, Object> dateMap = new HashMap<String, Object>();
            for (AttendanceDayDTO attendanceDayDTO : dto.getKqlist()) {
                dateMap.put(attendanceDayDTO.getDay(), attendanceDayDTO.getIskq());
            }
            dataListChild.add(dateMap);
            valMap.put("kqlist", dataListChild);
            list.add(valMap);
        }
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("管理人员到岗详情统计", "UTF-8") + ".xls");
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(startdate + "——" + enddate + "管理人员到岗详情统计（标记说明：√-到岗，△-请假）", "数据"), colList,
                list);
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        workbook.close();
    }

    @Override
    public Result attendance5Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        List<Attendance5StatisticsDTO> list = baseDao.attendance5Statistics(params);
        return result.ok(list);
    }

    @Override
    public void exportAttendance5(Map<String, Object> params, HttpServletResponse response) throws IOException {
        File file = new File(ATTENDANCE5_EXPORT);
        if (!file.exists()) {
            throw new IOException("配置的模板文件不存在");
        }
        //查询导出数据
        params = getAreacodeParams(params);
        List<Attendance5StatisticsDTO> list = baseDao.attendance5Statistics(params);
        String description = baseDao.attendance51Statistics(params);
        TemplateExportParams exportParams = new TemplateExportParams(ATTENDANCE5_EXPORT);
        Map<String, Object> data = new HashMap<>();
        String tjdate = (String) params.get("tjdate");
        if (StringUtils.isBlank(tjdate)) {
            throw new IOException("请选择导出时间");
        }
        tjdate = DateUtil.format(DateUtil.parse(tjdate), "yyyy年MM月dd日");
        data.put("list", list);
        data.put("tjdate", tjdate);
        data.put("description", description);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(tjdate + "关键岗位统计", "UTF-8") + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    @Override
    public Result attendance51Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        String description = baseDao.attendance51Statistics(params);
        return result.ok(description);
    }

    @Override
    public PageData<Attendance52StatisticsDTO> attendance52Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        //params = getAreacodeParams(params);
        List<Attendance52StatisticsDTO> list = baseDao.attendance52Statistics(params);
        for (Attendance52StatisticsDTO dto : list) {
            dto.setList(baseDao.selectTj03InfoDetail(dto.getId()));
        }
        return getPageData(list, page.getTotal(), Attendance52StatisticsDTO.class);
    }

    @Override
    public void exportAttendance52(Map<String, Object> params, HttpServletResponse response) throws IOException {
//        File file = new File(ATTENDANCE52_EXPORT);
//        if (!file.exists()) {
//            throw new IOException("配置的模板文件不存在");
//        }
        String tjdate = (String) params.get("tjdate");
        if (StringUtils.isBlank(tjdate)) {
            throw new IOException("请选择导出时间");
        }
        tjdate = DateUtil.format(DateUtil.parse(tjdate), "yyyy年MM月dd日");
        //查询导出数据
        //params = getAreacodeParams(params);
        List<Attendance52StatisticsDTO> list = baseDao.attendance52Statistics(params);
        for (Attendance52StatisticsDTO dto : list) {
            dto.setList(baseDao.selectTj03InfoDetail(dto.getId()));
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet();
        // 创建表头
        XSSFRow mainRow = sheet.createRow(0);
        XSSFRow subRow = sheet.createRow(1);
        int colIndex = 0;

        XSSFCell mainCell = mainRow.createCell(colIndex);
        mainCell.setCellValue("关键岗位-项目统计情况" + tjdate);
        sheet.setColumnWidth(colIndex, 15000);
        List<Tj03InfoDetailDTO> dataList = list.get(0).getList();
        sheet.addMergedRegion(new CellRangeAddress(0, 0, colIndex, + 2 * (dataList.size())));

        subRow.createCell(colIndex++).setCellValue("项目名称");
        for (Tj03InfoDetailDTO dto : dataList) {
            sheet.setColumnWidth(colIndex, 5000);
            sheet.setColumnWidth(colIndex + 1, 5000);
            subRow.createCell(colIndex).setCellValue("录入" + dto.getJobname() + "人数");
            subRow.createCell(colIndex + 1).setCellValue("考勤" + dto.getJobname() + "人数");

            colIndex += 2;
        }

        // 填充数据
        int rowIndex = 2;
        for (Attendance52StatisticsDTO dto : list) {
            XSSFRow row = sheet.createRow(rowIndex++);
            int cellIndex = 0;
            row.createCell(cellIndex++).setCellValue(dto.getName());
            for (Tj03InfoDetailDTO data : dto.getList()) {
                row.createCell(cellIndex++).setCellValue(data.getAddCount());
                row.createCell(cellIndex++).setCellValue(data.getKqCount());
            }
        }

        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(tjdate + "关键岗位统计-详情", "UTF-8") + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    @Override
    public PageData<Salary1StatisticsDTO> salary1Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        params.put("deptId", SecurityUser.getDeptId());
        List<Salary1StatisticsDTO> list = baseDao.salary1Statistics(params);
        return getPageData(list, page.getTotal(), Salary1StatisticsDTO.class);
    }

    @Override
    public Result salary2Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        List<Salary2StatisticsDTO> list = baseDao.salary2Statistics(params);
        return result.ok(list);
    }

    @Override
    public PageData<Salary3StatisticsDTO> salary3Statistics(Map<String, Object> params) {
        IPage<Pj01Entity> page = getPage(params, "", false);
        params.put("deptId", SecurityUser.getDeptId());
        List<Salary3StatisticsDTO> list = baseDao.salary3Statistics(params);
        return getPageData(list, page.getTotal(), Salary3StatisticsDTO.class);
    }

    @Override
    public Result complaint1Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        List<ComplaintStatisticsDTO> list = baseDao.complaint1Statistics(params);
        return result.ok(list);
    }

    @Override
    public Result complaint2Statistics(Map<String, Object> params) {
        Result<Object> result = new Result<>();
        params = getAreacodeParams(params);
        List<ComplaintStatisticsDTO> list = baseDao.complaint2Statistics(params);
        return result.ok(list);
    }

    /**
     * 导出关键岗位人员到岗信息
     *
     * @param params
     * @param response
     */
    @Override
    public void exportAttendanceProjectInfoExcel(Map<String, Object> params, HttpServletResponse response) {

        List<ProjectDateAttendanceInfoDTO> projectDateAttendanceInfoDTOList = baseDao.projectDateAttendanceInfoDTOList(params);
        if (CollectionUtil.isEmpty(projectDateAttendanceInfoDTOList)) {
            return;
        }
        List<Map<String, Object>> sheetsList = Lists.newArrayList();
        // 使用Stream进行分组并将结果转换为 List<String, List<MyObject>>
        List<Map.Entry<String, List<ProjectDateAttendanceInfoDTO>>> result = projectDateAttendanceInfoDTOList
                .stream()
                .collect(Collectors.groupingBy(ProjectDateAttendanceInfoDTO::getAreacode))
                .entrySet()
                .stream()
                .collect(Collectors.toList());

        String today = MapUtil.getStr(params, "today");
//        String today = DateUtil.today();
        for (Map.Entry<String, List<ProjectDateAttendanceInfoDTO>> stringListEntry : result) {

            String title = today + stringListEntry.getKey() +"关键岗位人员未到岗情况统计-统计时间：" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm");

            Map<String, Object> exportParams = pj01ExportInfoHandler
                    .createExportParams(title, stringListEntry.getKey(), "20", stringListEntry.getValue());
            sheetsList.add(exportParams);
        }

        export(response, sheetsList, today);
    }

    /**
     * 关键岗位人员到岗统计
     *
     * @param params
     * @return
     */
    @Override
    public Result<List<AttendanceKeyPositionsStatisticsDTO>> getAttendanceKeyPositionsStatistics(Map<String, Object> params) {
        // 参数检查
        String areaCode = MapUtil.getStr(params, "areaCode");
        if (StrUtil.isBlank(areaCode)) {
            areaCode = SecurityUser.getDeptAreaCode();
        }
        // 区域代码处理
        if (areaCode.substring(4, 6).equals(Constant.CITY_LASECODE)) {
            areaCode = areaCode.substring(0, 4);
        }
        params.put("areaCode", areaCode);
        // 获取考勤重点岗位统计信息
        List<AttendanceKeyPositionsStatisticsDTO> attendanceKeyPositionsStatistics = baseDao.getAttendanceKeyPositionsStatistics(params);
        // 返回结果封装
        return new Result<List<AttendanceKeyPositionsStatisticsDTO>>().ok(attendanceKeyPositionsStatistics);
    }


    /**
     * 导出关键岗位人员
     *
     * @param params
     */
    @Override
    public void exportAttendanceKeyPositionsStatistics(Map<String, Object> params, HttpServletResponse response) {

        // 参数检查
        String areaCode = MapUtil.getStr(params, "areaCode");
        if (StrUtil.isBlank(areaCode)) {
            areaCode = SecurityUser.getDeptAreaCode();
        }
        // 区域代码处理
        if (areaCode.substring(4, 6).equals(Constant.CITY_LASECODE)) {
            areaCode = areaCode.substring(0, 4);
        }
        params.put("areaCode", areaCode);
        List<AttendanceKeyPositionsStatisticsDTO> attendanceKeyPositionsStatistics = baseDao.getAttendanceKeyPositionsStatistics(params);
        if (CollUtil.isEmpty(attendanceKeyPositionsStatistics)) {
            throw new RenException("未查询到相关数据");
        }
        String today = MapUtil.getStr(params, "today");
        ExportParams exportParams = new ExportParams("关键岗位人员到岗统计-统计情况（" + today + "）", "");
        //此处格式对应下文文件名后缀xlsx
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, AttendanceKeyPositionsStatisticsDTO.class, attendanceKeyPositionsStatistics);
        pj01ExportInfoHandler.downLoadExcel(today + "关键岗位人员到岗统计-统计情况" + ".xls", response, workbook);
    }

    /**
     * 导出
     *
     * @param response
     * @param sheetsList 导出数据
     * @param today      时间（yyyy-MM-dd）
     */
    private void export(HttpServletResponse response, List<Map<String, Object>> sheetsList, String today) {
        if (sheetsList.size() > 0) {
            pj01ExportInfoHandler.orderByCityState(sheetsList);
            Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);

            // 创建红色字体样式
            Font redFont = workbook.createFont();
            redFont.setColor(Font.COLOR_RED);
            redFont.setFontHeightInPoints((short) 11);

            HSSFCellStyle redCellStyle = (HSSFCellStyle) workbook.createCellStyle();
            redCellStyle.setAlignment(HorizontalAlignment.CENTER);
            redCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            redCellStyle.setFont(redFont);

            // 设置样式的列索引
            int MANAGER_ATTENDANCE_COL = 5;
            int DIRECTOR_ATTENDANCE_COL = 7;

            int numberOfSheets = workbook.getNumberOfSheets();
            for (int i = 0; i < numberOfSheets; i++) {
                Sheet sheet = workbook.getSheetAt(i);
                for (int i1 = 2; i1 < sheet.getPhysicalNumberOfRows(); i1++) {
                    Row row = sheet.getRow(i1);
                    if (row != null) {
                        Cell managerAttendance = row.getCell(MANAGER_ATTENDANCE_COL);
                        Cell directorAttendance = row.getCell(DIRECTOR_ATTENDANCE_COL);

                        // 根据条件设置样式
                        setCellStyleIfCondition(managerAttendance, row.getCell(MANAGER_ATTENDANCE_COL - 1), redCellStyle, "否");
                        setCellStyleIfCondition(directorAttendance, row.getCell(DIRECTOR_ATTENDANCE_COL - 1), redCellStyle, "否");
                    }
                }
            }
            pj01ExportInfoHandler.downLoadExcel(today + "关键岗位人员未到岗情况统计" + ".xls", response, workbook);
        }
    }

    /**
     * 设置表格样式
     *
     * @param conditionCell  表格
     * @param targetCell     操作表格
     * @param style          设置样式
     * @param conditionValue 条件
     */
    private static void setCellStyleIfCondition(Cell conditionCell, Cell targetCell, CellStyle style, String conditionValue) {
        if (conditionCell != null) {
            String conditionCellValue = conditionCell.getStringCellValue();
            if (conditionValue.equals(conditionCellValue)) {
                targetCell.setCellStyle(style);
                conditionCell.setCellStyle(style);
            }
        }
    }

    private Map<String, Object> getAreacodeParams(Map<String, Object> params) {
        String areacode = (String) params.get("areacode");
        if (StringUtils.isBlank(areacode)) {
            areacode = SecurityUser.getDeptAreaCode();
            String lastcode = areacode.substring(4);
            if (Constant.CITY_LASECODE.equals(lastcode)) {
                params.put("areacode", areacode.substring(0, 4));
            } else {
                params.put("areacode", areacode);
            }
        }
        return params;
    }

    /**
     * 将年、月、日参数拼接为统一的 tjdate 参数格式
     * @param year  年（如 "2025"）
     * @param month 月（如 "07"）
     * @param day   日（如 "22"）
     * @return      格式化后的 tjdate（如 "2025-07-22"、"2025-07"、"2025"）
     */
    public static String assembleDateParam(String year, String month, String day) {
        if (year == null || year.trim().isEmpty()) {
            return null; // 年份为空则返回 null
        }

        if (month != null && !month.trim().isEmpty() && day != null && !day.trim().isEmpty()) {
            // 年月日齐全
            return String.format("%s-%s-%s", year, month, day);
        } else if (month != null && !month.trim().isEmpty()) {
            // 年月存在
            return String.format("%s-%s", year, month);
        } else {
            // 只有年
            return year;
        }
    }
}