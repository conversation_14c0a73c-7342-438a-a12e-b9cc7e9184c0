package io.renren.modules.regul.pj13.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 退场审核表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ13")
public class Pj13Entity {
	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@TableId
	private Long id;
    /**
     * 退场业务id（工人id、管理人员id、班组id）
     */
	private Long exitId;
    /**
     * 退场时间
     */
	private Date exittime;
	/**
	 * 进场时间
	 */
	private Date entrytime;
    /**
     * 审核状态(0待审核，1通过，2不通过)
     */
	private String auditstatus;
    /**
     * 审核人
     */
	private String auditor;
    /**
     * 审核时间
     */
	private Date auditdate;
    /**
     * 审核原因
     */
	private String auditreason;
    /**
     * 退场类型（1：工人退场、2：管理人员退场、3：班组退场）
     */
	private String exitType;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
	/**
	 * 申请时间
	 */
	private Date applydate;
}