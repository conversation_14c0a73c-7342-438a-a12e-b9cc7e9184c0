package io.renren.modules.regul.ps02.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import io.renren.modules.regul.pa03.dto.Pa03DTO;
import io.renren.modules.regul.pj01.dto.Kq02PageDTO;
import io.renren.modules.regul.ps02.dto.Ps02ContractDTO;
import io.renren.modules.regul.ps02.dto.Ps02DTO;
import io.renren.modules.regul.ps02.dto.Ps02EmpRecordDTO;
import io.renren.modules.regul.ps02.dto.Ps02PageDTO;
import io.renren.modules.regul.ps02.entity.Ps02Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps02Dao extends BaseDao<Ps02Entity> {

    /**
     * 查询分页数据
     *
     * @param params params
     * @return List<Ps02DTO>
     */
    List<Ps02PageDTO> getListData(Map<String, Object> params);

    /**
     * 工人退场
     *
     * @param ids  ps0201
     * @param type 进场或退场
     */
    void updateInOrOutByIds(@Param("list") List<Long> ids, @Param("type") String type);

    /**
     * 查询人员信息
     *
     * @param longs 人员id
     * @return
     */
    List<PersonDTO> selectPersonByIds(List<Long> longs);

    /**
     * 查询项目项目的人员是否存在
     *
     * @param pj0101 项目ID
     * @param ps0101 人员ID
     * @return
     */
    Integer selectCountPs0101(@Param("pj0101") Long pj0101, @Param("ps0101") Long ps0101);

    Ps02ContractDTO exportContract(String ps0201);

    /**
     * 查询工人用工记录信息
     *
     * @param params
     * @return
     */
    List<Ps02EmpRecordDTO> getEmpRecordListData(Map<String, Object> params);

    /**
     * 详情
     *
     * @param ps0201
     * @return
     */
    Ps02DTO getInfoById(Long ps0201);

    /**
     * 工资流水列表
     *
     * @param params
     * @return
     */
    List<Pa03DTO> salaryPage(Map<String, Object> params);

    /**
     * 考勤数据
     *
     * @param params
     * @return
     */
    List<Kq02PageDTO> getAttendancePageList(Map<String, Object> params);

    /**
     * 工人列表
     *
     * @param
     * @return
     */
    List<Ps02PageDTO> selectListByTm0101(Map<String, Object> params);

    /**
     * 获取班组在场工人数量
     *
     * @param
     * @return
     */
    long countPs02ByTm0101(Long tm0101);
}