package io.renren.modules.regul.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 关键岗位统计-详情
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "关键岗位统计-详情")
public class Attendance52StatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "项目名称")
    private String name;
    /*@ApiModelProperty(value = "主管部门")
    private String areacode;*/
    @ApiModelProperty(value = "关键岗位详情")
    private List<Tj03InfoDetailDTO> list;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "统计时间")
    private Date tjdate;
}