package io.renren.modules.regul.region.dto;

import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 电子围栏区域审核
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Data
@ApiModel(value = "电子围栏区域审核")
public class RegionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "id")
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "主管部门")
    private String areacode;
    @ApiModelProperty(value = "电子围栏区域")
    private String region;
    @ApiModelProperty(value = "申请时间")
    private Date createDate;

    @ApiModelProperty(value = "审核状态")
    private String auditstatus;
    @ApiModelProperty(value = "审核时间")
    private Date auditdate;
    @ApiModelProperty(value = "审核人")
    private String auditor;
    @ApiModelProperty(value = "审核结果")
    private String auditresult;
    @ApiModelProperty(value = "标注位置")
    private String markLocation;
    @ApiModelProperty(value = "项目总平图")
    private List<Ot01DTO> regionFiles;
    @ApiModelProperty(value = "项目状态")
    private String prjstatus;
}