package io.renren.modules.regul.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "关键岗位详情")
public class Tj03InfoDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "录入人数")
    private Long addCount;
    @ApiModelProperty(value = "考勤人数")
    private Long kqCount;
    @ApiModelProperty(value = "岗位类型")
    private String jobtype;
    @ApiModelProperty(value = "岗位名称")
    private String jobname;
}
