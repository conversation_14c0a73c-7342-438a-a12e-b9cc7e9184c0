package io.renren.modules.regul.cg09.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.regul.cg09.dto.Cg09DTO;
import io.renren.modules.regul.cg09.entity.Cg09Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 检查清单档案配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-09
 */
@Mapper
public interface Cg09Dao extends BaseDao<Cg09Entity> {

    /**
     * 获取文件树
     *
     * @param params Map<String, Object>
     * @return List<Cg09DTO>
     */
    List<Cg09DTO> getFileTreeList(Map<String, Object> params);

    /**
     * 获取列表
     *
     * @param params Map<String, Object>
     * @return List<Cg09DTO>
     */
    List<Cg09DTO> getTableList(Map<String, Object> params);

    /**
     * 根据档案ID和年月查询附件数量
     *
     * @param params 查询参数，包含cg0901、fileYear、pj0101
     * @return 附件数量
     */
    Integer getFileCountByArchiveAndYear(Map<String, Object> params);

    /**
     * 根据档案ID和年份查询附件数量
     *
     * @param params 查询参数，包含cg0901、year、pj0101
     * @return 附件数量
     */
    Integer getFileCountByArchiveAndYearOnly(Map<String, Object> params);

}