package io.renren.modules.regul.ps04.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constants.CommonConstants;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.ot01.dao.Ot01Dao;
import io.renren.modules.ot01.dto.Ot01DTO;
import io.renren.modules.ot01.service.Ot01Service;
import io.renren.modules.regul.pj01.dto.Kq02PageDTO;
import io.renren.modules.regul.ps01.dao.Ps01Dao;
import io.renren.modules.regul.ps01.dto.Ps01DTO;
import io.renren.modules.regul.ps03.dao.Ps03Dao;
import io.renren.modules.regul.ps03.entity.Ps03Entity;
import io.renren.modules.regul.ps04.dao.Ps04AuditDao;
import io.renren.modules.regul.ps04.dao.Ps04Dao;
import io.renren.modules.regul.ps04.dto.Ps04AuditDTO;
import io.renren.modules.regul.ps04.dto.Ps04ConfirmDTO;
import io.renren.modules.regul.ps04.dto.Ps04DTO;
import io.renren.modules.regul.ps04.dto.Ps04PageDTO;
import io.renren.modules.regul.ps04.entity.Ps04AuditEntity;
import io.renren.modules.regul.ps04.entity.Ps04Entity;
import io.renren.modules.regul.ps04.service.Ps04Service;
import io.renren.modules.supdevicetask.dto.PersonDTO;
import io.renren.modules.supdevicetask.service.SupDeviceTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Ps04ServiceImpl extends CrudServiceImpl<Ps04Dao, Ps04Entity, Ps04DTO> implements Ps04Service {

    @Autowired
    private Ot01Dao ot01Dao;
    @Autowired
    private Ps01Dao ps01Dao;
    @Autowired
    private Ps03Dao ps03Dao;
    @Autowired
    private Ps04AuditDao ps04AuditDao;
    @Autowired
    private SupDeviceTaskService taskService;
    @Autowired
    private Ot01Service ot01Service;

    @Override
    public QueryWrapper<Ps04Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps04Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ps04PageDTO> ps04Page(Map<String, Object> params) {
        IPage<Ps04Entity> page = getPage(params, "", false);
//        String areacode = MapUtil.getStr(params, "areacode");
//        if (StringUtils.isEmpty(areacode)) {
//            String deptAreacode = SecurityUser.getDeptAreaCode();
//            String lastcode = deptAreacode.substring(4);
//            if (Constant.CITY_LASECODE.equals(lastcode)) {
//                params.put("areacode", deptAreacode.substring(0, 4));
//            } else {
//                params.put("areacode", deptAreacode);
//            }
//        }
        params.put("deptId", SecurityUser.getDeptId());
        List<Ps04PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Ps04PageDTO.class);
    }

    @Override
    public PageData<Ps04AuditDTO> auditPageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<Ps04AuditDTO> list = ps04AuditDao.auditPageList(params);
        return getPageData(list, page.getTotal(), Ps04AuditDTO.class);
    }

    @Override
    public Ps04AuditDTO getInfo(Map<String, Object> params) {
        Long ps0401 = Long.valueOf(params.get("ps0401").toString());
        Ps04AuditDTO dto = ps04AuditDao.getInfoById(params);
        //查询基础信息
        Ps03Entity ps03 = ps03Dao.selectById(dto.getPs0301());
        Ps01DTO ps01DTO = ps01Dao.selectInfoById(ps03.getPs0101());
        dto.setPs01DTO(ps01DTO);
        if ("1".equals(dto.getInOrOut())) {
            //查询证书附件
            List<Ot01DTO> certificateFiles = ot01Dao.getBusinessData(dto.getPs0301(), "11");
            dto.setCertificateFiles(certificateFiles);
            //查询社保附件
            List<Ot01DTO> insuranceFiles = ot01Dao.getBusinessData(dto.getPs0301(), "12");
            dto.setInsuranceFiles(insuranceFiles);
        } else if ("2".equals(dto.getInOrOut())) {
            List<Ot01DTO> exitFiles = ot01Dao.getBusinessData(ps0401, "13");
            dto.setExitFiles(exitFiles);
        }
        return dto;
    }

    @Override
    public Ps04DTO getManagerInfo(Map<String, Object> params) {
        Long ps0101 = Long.valueOf(params.get("ps0101").toString());
        Ps04DTO dto = baseDao.getManagerInfo(ps0101);
        if (dto == null) {
            dto = new Ps04DTO();
        }
        //查询人员基础信息
        Ps01DTO ps01DTO = ps01Dao.selectInfoById(ps0101);
        dto.setPs01DTO(ps01DTO);
        //查询证书信息
//        List<Ot01DTO> certificateFiles = ot01Dao.getBusinessData(dto.getPs0301(), "50");
        List<Ot01DTO> certificateFiles = ot01Dao.getBusinessData(dto.getPs0401(), "50");
        dto.setCertificateFiles(certificateFiles);
        //查询社保信息
//        List<Ot01DTO> insuranceFiles = ot01Dao.getBusinessData(dto.getPs0301(), "51");
        List<Ot01DTO> insuranceFiles = ot01Dao.getBusinessData(dto.getPs0301(), "51");
        dto.setInsuranceFiles(insuranceFiles);

        if (ObjectUtil.isNotNull(dto)) {
            List<Ot01DTO> ot01DTOS = ot01Service.loadBusinessData(dto.getPs0401(), "54");
            dto.setManagerCertificateFiles(ot01DTOS);
        }
        return dto;
    }

    @Override
    public PageData<Ps04DTO> empRecordPage(Map<String, Object> params) {
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<Ps04DTO> list = baseDao.empRecordPage(params);
        return getPageData(list, page.getTotal(), Ps04DTO.class);
    }

    @Override
    public PageData<Kq02PageDTO> attendancePageList(Map<String, Object> params) {
        // 分页
        IPage<Ps04Entity> page = getPage(params, "CHECKDATE", false);
        List<Kq02PageDTO> list = baseDao.getAttendancePageList(params);
        return getPageData(list, page.getTotal(), Kq02PageDTO.class);
    }

    @Override
    public void audit(Ps04AuditDTO dto) {
        dto.setAuditdate(new Date());
        dto.setAuditor(SecurityUser.getUser().getRealName());
        //保存审核结果
        Ps04AuditEntity ps04Audit = ps04AuditDao.selectOne(new QueryWrapper<Ps04AuditEntity>().eq("PS0401", dto.getPs0401()).eq("IN_OR_OUT", dto.getInOrOut()).eq("auditstatus", "0"));
        if ("1".equals(dto.getAuditstatus())) {
            dto.setAuditresult("通过");
            if ("1".equals(dto.getInOrOut())) {
                //更新管理人员当前项目
                Ps03Entity ps03 = ps03Dao.selectById(ps04Audit.getPs0301());
                ps03.setPj0101(ps04Audit.getPj0101());
                ps03Dao.updateById(ps03);
                //管理人员数据写入正式表
                Ps04Entity ps04 = new Ps04Entity();
                BeanUtil.copyProperties(ps04Audit, ps04, CopyOptions.create().setIgnoreNullValue(true));
                baseDao.insert(ps04);
                //下发管理人员
                List<PersonDTO> list = ps03Dao.selectDeviceInfo(ps04Audit.getPs0401());
                taskService.personIntoDevicesByPj0101(list, CommonConstants.PERSON_INTO, CommonConstants.MANAGER_TYPE, ps04.getPj0101());
            } else if ("2".equals(dto.getInOrOut())) {
                //更新管理人员当前项目
                Ps03Entity ps03 = ps03Dao.selectById(ps04Audit.getPs0301());
                ps03.setPj0101(null);
                ps03Dao.updateById(ps03);
                //更新正式表进退场状态
                Ps04Entity ps04 = baseDao.selectById(dto.getPs0401());
                BeanUtil.copyProperties(ps04Audit, ps04, CopyOptions.create().setIgnoreNullValue(true));
                baseDao.updateById(ps04);
            }
        }
        BeanUtil.copyProperties(dto, ps04Audit, CopyOptions.create().setIgnoreNullValue(true));
        ps04AuditDao.updateById(ps04Audit);
    }

    @Override
    public PageData<Ps04ConfirmDTO> confirmPageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<Ps04ConfirmDTO> list = baseDao.confirmPageList(params);
        return getPageData(list, page.getTotal(), Ps04ConfirmDTO.class);
    }

    @Override
    public void confirm(Ps04PageDTO dto) {
        //保存审核结果
        Ps04Entity ps04 = baseDao.selectById(dto.getPs0401());
        ps04.setConfirmDate(new Date());
        ps04.setConfirmor(SecurityUser.getUser().getRealName());
        ps04.setIsconfirm(dto.getIsconfirm());
        if ("1".equals(dto.getIsconfirm())) {
            ps04.setConfirmresult("通过");
        } else if ("2".equals(dto.getIsconfirm())) {
            ps04.setConfirmresult(dto.getConfirmresult());
        }
        baseDao.updateById(ps04);
    }
}