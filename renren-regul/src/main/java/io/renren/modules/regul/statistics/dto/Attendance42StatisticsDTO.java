package io.renren.modules.regul.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 到岗率-项目列表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-01-03
 */
@Data
@ApiModel(value = "到岗率-项目列表")
public class Attendance42StatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "总承包企业")
    private String corpname;
    @ApiModelProperty(value = "在场人数")
    private String inmanagers;
    @ApiModelProperty(value = "出勤人数")
    private String kqmanagers;
    @ApiModelProperty(value = "综合到岗率")
    private String synthesizepercent;
    @ApiModelProperty(value = "总包单位在场人数")
    private String zonginmanagers;
    @ApiModelProperty(value = "总包单位出勤人数")
    private String zongkqmanagers;
    @ApiModelProperty(value = "总包单位到岗率")
    private String zongpercent;
/*    @ApiModelProperty(value = "项目经理到岗率")
    private String jinglipercent;*/
    @ApiModelProperty(value = "监理单位在场人数")
    private String jianinmanagers;
    @ApiModelProperty(value = "监理单位出勤人数")
    private String jiankqmanagers;
    @ApiModelProperty(value = "监理单位到岗率")
    private String jianpercent;
/*    @ApiModelProperty(value = "总监理工程师到岗率")
    private String zongjianpercent;*/
/*    @ApiModelProperty(value = "项目经理和总监理工程师到岗率")
    private String jingandjianpercent;*/
    @ApiModelProperty(value = "其他单位在场人数")
    private String qitainmanagers;
    @ApiModelProperty(value = "其他单位出勤人数")
    private String qitakqmanagers;
    @ApiModelProperty(value = "其他单位到岗率")
    private String qitapercent;
    @ApiModelProperty(value = "建设单位在场人数")
    private String sheinmanagers;
    @ApiModelProperty(value = "建设单位出勤人数")
    private String shekqmanagers;
    @ApiModelProperty(value = "建设单位到岗率")
    private String shepercent;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "统计时间")
    private Date tjdate;
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;
}