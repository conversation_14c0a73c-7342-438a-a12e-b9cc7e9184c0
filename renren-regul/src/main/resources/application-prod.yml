spring:
  datasource:
    druid:
      #Oracle
      driver-class-name: oracle.jdbc.OracleDriver
#      正式环境
      url: **************************************
      username: luzhou_er_center
      password: luzhou_er_center
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  #RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: xygk
    password: xygk@123
    virtual-host: /
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password: ErCenter@123$!
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
#生产环境,需要屏蔽所有Swagger的相关资源
knife4j:
  production: true
expFile:
  attendanceRecord: /u01/pro/template/AttendanceRecord.xlsx    #考勤表导出模板文件路径
  payrollRegister: /u01/pro/template/PayrollRegister.xlsx    #工资发放花名册导出模板文件路径
  workerRecord: /u01/pro/template/WorkerRecord.xlsx   #备案表导出模板文件路径
  projectLedger: /u01/pro/template/ProjectLedger.xlsx    #项目台账导出模板文件路径
  contractExport: /u01/pro/template/四川省建筑工人简易劳动合同书.docx
  acceptfileExport: /u01/pro/template/投诉受理通知书.docx
  attendance3Export: /u01/pro/template/更新率统计导出模板.xlsx
  attendance32Export: /u01/pro/template/更新率详情统计导出模板.xlsx
  attendance4Export: /u01/pro/template/到岗率统计导出模板.xlsx
  attendance42Export: /u01/pro/template/到岗率详情统计导出模板.xlsx
  attendance43Export: /u01/pro/template/个人到岗详情统计导出模板.xlsx
  attendance5Export: /u01/pro/template/关键岗位统计导出模板.xlsx
  attendance52Export: /u01/pro/template/关键岗位详情统计导出模板.xlsx
  bankJointExport: /u01/pro/template/开户银行对接情况统计.xlsx
  bankProjectsExport: /u01/pro/template/项目完善银行专户情况统计.xlsx
  projectExport: /u01/pro/template/项目综合情况统计.xlsx
  pj01tzExport: /u01/pro/template/未入库项目台账.xls
  tjScoreExport: /u01/pro/template/各区县月实名制管理考核得分统计.xlsx
  patrolExport: /u01/pro/template/项目巡检报告.docx