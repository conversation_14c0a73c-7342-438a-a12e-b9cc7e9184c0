<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="io.renren.modules.home.dao.HomeDao">
    <select id="getCurrentArea" resultType="io.renren.modules.home.dto.AreaDto">
        select y.dict_label              as label,
               y.dict_value              as value,
               (select count(1)
                from b_pj01 t,
                     sys_dept a
                where t.dept_id = a.id
                  and a.areacode = y.dict_value
                  and t.prjstatus = '3') as projects
        from sys_dict_type x,
             sys_dict_data y
        where x.dict_type = 'VIRAREACODE'
          and x.id = y.dict_type_id
          and y.status = '1'
          and y.dict_value like '%' || #{areaCode} || '%'
        order by y.sort
    </select>


    <select id="getConstructionProjects" resultType="java.lang.Long">
        select count(1)
        from b_pj01 t,
             R_PJ01_DEPT b
        where t.PJ0101 = b.PJ0101
          and b.DEPT_ID = #{deptId}
          and t.prjstatus = '3'
    </select>

    <select id="getManagerSum" resultType="java.lang.Long">
        select count(1)
        from b_ps04 t,
             b_pj01 a,
             r_pj01_dept b
        where t.pj0101 = a.pj0101
          and a.PJ0101 = b.PJ0101
          and a.prjstatus = '3'
          and t.in_or_out = '1'
          and b.DEPT_ID = #{deptId}
    </select>

    <select id="getWorkerSum" resultType="java.lang.Long">
        select count(1)
        from b_ps02 t,
             b_pj01 a,
             r_pj01_dept b
        where t.pj0101 = a.pj0101
          and a.PJ0101 = b.PJ0101
          and a.prjstatus = '3'
          and t.in_or_out = '1'
          and b.DEPT_ID = #{deptId}
    </select>

    <select id="getPersonAttendance" resultType="java.lang.Long">
        select count(distinct (c.user_id))
        from b_pj01 a,
             r_pj01_dept b,
             b_kq02_now c
        where a.pj0101 = c.pj0101
          and a.PJ0101 = b.PJ0101
          and to_char(c.checkdate, 'yyyy-MM-dd') = to_char(sysdate, 'yyyy-MM-dd')
          and b.dept_id = #{deptId}
    </select>
    <select id="getSalarySum" resultType="java.lang.Long">
        select nvl(round(sum(t.accountnum) / 10000, 2), 0)
        from B_PA02 t,
             b_pj01 a,
             r_pj01_dept b
        where t.pj0101 = a.pj0101
          and a.PJ0101 = b.PJ0101
          and t.accounttype = '2'
          and b.DEPT_ID = #{deptId}
    </select>
    <select id="getAttendances" resultType="io.renren.modules.home.dto.StatisticsAttendance">
        select substr(t.day, 4, 5)                                                          as days,
               (select count(distinct (x.user_id))
                from b_kq02 x,
                     b_pj01 y,
                     r_pj01_dept z
                where x.pj0101 = y.pj0101
                  and y.PJ0101 = z.PJ0101
                  and z.dept_id = #{deptId}
                  and to_char(x.checkdate, 'yyyy-MM-dd') = substr(#{month}, 0, 5) || t.day) as attendanceNum
        from SYS_DAYS t
        where substr(t.day, 0, 2) = substr(#{month}, 6, 7)
        order by t.id
    </select>
    <select id="getWarnList" resultType="java.lang.String">
        select (select x.warname from b_jg02 x where x.wartype = t.wartype) as wartype
        from B_JG01 t,
             b_pj01 a,
             sys_dept b
        where t.pj0101 = a.pj0101
          and a.dept_id = b.id
          and t.warstatus = '1'
    </select>
    <select id="getSalaryChart" resultType="io.renren.modules.home.dto.SalaryDto">
        select t.month,
               (select nvl(round(sum(x.accountnum) / 10000, 2), 0)
                from b_pa02 x,
                     b_pj01 y,
                     sys_Dept z
                where x.pj0101 = y.pj0101
                  and y.dept_id = z.id
                  and to_char(x.accountdate, 'yyyy-MM') = t.month
                  and x.accounttype = '2') as salarys
        from (SELECT TO_CHAR(add_months(to_date(to_char(trunc(sysdate, 'mm') + 1,
                                                        'yyyy-mm'),
                                                'yyyy-mm'),
                                        - (ROWNUM - 1)),
                             'yyyy-mm') as month
              FROM dual
              CONNECT BY ROWNUM &lt;= #{month}
              ORDER BY TO_CHAR(add_months(to_date(to_char(trunc(sysdate, 'mm') + 1,
                                                          'yyyy-mm'),
                                                  'yyyy-mm'),
                                          - (ROWNUM - 1)),
                               'yyyy-mm') ASC) t
    </select>
    <select id="getSalaryList" resultType="io.renren.modules.regul.pa02.dto.Pa02DTO">
        select h.*
        from (select a.name as comaccountname, round(t.accountnum / 10000, 2) as accountnum, t.accountdate
              from B_PA02 t,
                   b_pj01 a,
                   sys_dept b
              where t.pj0101 = a.pj0101
                and a.dept_id = b.id
                and t.accounttype = '2'
              order by t.accountdate desc) h
        where rownum &lt; 100
    </select>
    <select id="getPauseProjects" resultType="java.lang.Long">
        select count(1)
        from b_pj01 t,
             r_pj01_dept a
        where t.PJ0101 = a.DEPT_ID
          and t.prjstatus = '5'
          and a.DEPT_ID = #{deptId}
    </select>
    <select id="getBankJoint" resultType="io.renren.modules.regul.bankjoint.dto.BankJointDTO">
        select h.*
        from (select t.*,
                     (select count(1)
                      from b_pa01 a,
                           b_pj01 b,
                           sys_dept c
                      where a.pj0101 = b.pj0101
                        and b.dept_id = c.id
                        and a.pay_bank_code = t.pay_bank_code
                        and b.prjstatus in ('3', '5')) as projects
              from I_BANK_JOINT t) h
        order by h.bankjointstatus desc, h.projects desc
    </select>

    <select id="getLoginAreaCodeList" resultType="io.renren.modules.home.dto.AreaCodeLoginCountDTO">
        select (select x.dict_label
                from sys_dict_data x,
                     sys_dict_type y
                where x.dict_type_id = y.id
                  and y.dict_type = 'VIRAREACODE'
                  and x.dict_value = t.areacode) as areacode,
               nvl(a.logincount, 0)                 logincount
        from (select t.AREACODE, t.id
              from sys_dept t
              where t.pids like '%92733%'
                and t.pid != '92733'
              order by t.sort asc) t
                 left join (select count(1) logincount, u.dept_id
                            from sys_log_login t,
                                 sys_user u
                            where t.creator_name = u.username
                              and t.create_date between
                                to_date(to_char(sysdate - 7, 'yyyy-mm-dd'), 'yyyy-mm-dd') and
                                to_date(to_char(sysdate, 'yyyy-mm-dd'), 'yyyy-mm-dd')
                            group by u.dept_id) a
                           on t.id = a.dept_id
    </select>
</mapper>