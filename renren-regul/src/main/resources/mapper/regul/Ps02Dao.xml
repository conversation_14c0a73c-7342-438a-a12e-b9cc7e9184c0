<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.ps02.dao.Ps02Dao">
    <!-- 工人列表查询-->
    <select id="getListData" resultType="io.renren.modules.regul.ps02.dto.Ps02PageDTO">
        select *
            from (select t.ps0201,
                t.ps0101,
                b.name,
                (select x.corpname
                from b_cp01 x, b_cp02 y
                where x.cp0101 = y.cp0101
                and y.cp0201 = c.cp0201) as corpName,
                c.teamname,
                t.entrytime,
                t.exittime,
                a.name as personName,
                a.idcardnumber,
                a.cellphone,
                t.isteamleader,
                t.worktypecode,
                t.in_or_out,
                (select case
                when count(1) = 0 then
                0
                else
                1
                end
                from b_ps08 x
                where x.ps0201 = t.ps0201) as issigned,
                (select case
                when count(1) = 0 then
                0
                else
                1
                end
                from b_ot01 y
                where y.busisysno = t.ps0201
                and y.busitype = '10') isupload
                from b_ps02 t, b_ps01 a, b_pj01 b, b_tm01 c, r_pj01_dept d, sys_dept e
                where t.ps0101 = a.ps0101
                and t.tm0101 = c.tm0101
                and t.pj0101 = b.pj0101
                and t.pj0101 = d.pj0101
                and b.dept_id = e.id
                and d.dept_id = #{deptId}
        <if test="personName != null and personName != ''" >
            and a.name like '%'|| #{personName} ||'%'
        </if>
        <if test="name != null and name != ''" >
            and b.name like '%'|| #{name} ||'%'
        </if>
        <if test="idcardnumber != null and idcardnumber != ''" >
            and a.idcardnumber like '%'|| #{idcardnumber} ||'%'
        </if>
        <if test="prjstatus != null and prjstatus != ''" >
            and b.prjstatus = #{prjstatus}
        </if>
        <if test="inOrOut != null and inOrOut != ''" >
            and t.in_or_out = #{inOrOut}
        </if>
        ) where 1 = 1
        <if test="issigned != null and issigned != ''" >
            and issigned = #{issigned}
        </if>
        <if test="isupload != null and isupload != ''" >
            and isupload = #{isupload}
        </if>
    </select>

    <select id="selectPersonByIds" resultType="io.renren.modules.supdevicetask.dto.PersonDTO">
        select a.NAME, b.PS0201 userId, b.ISSUECARDPICURL imageUrl
        from b_ps01 a,
             b_ps02 b where b.PS0101 = a.PS0101
                        and b.PS0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="selectCountPs0101" resultType="java.lang.Integer">
        select count(0)
        from (select t.ps0101, t.pj0101
              from b_ps02 t
              union all
              select a.ps0101, a.pj0101
              from b_ps04 a) c
        where c.pj0101 = #{pj0101}
          and c.ps0101 = #{ps0101}
    </select>
    <select id="getEmpRecordListData" resultType="io.renren.modules.regul.ps02.dto.Ps02EmpRecordDTO">
        select (select b.name from b_pj01 b where b.pj0101 = t.pj0101)     projectName,
               a.name,
               a.idcardnumber                                              idCardNumber,
               t.worktypecode                                              workTypeCode,
               (select c.teamname from b_tm01 c where c.tm0101 = t.tm0101) teamName,
               t.entrytime                                                 entryTime,
               t.exittime                                                  exitTime,
               t.in_or_out                                                 inOrOut,
               t.pj0101
        from b_ps02 t,
             b_ps01 a
        where t.ps0101 = a.ps0101
          and t.ps0101 = #{ps0101}
    </select>
    <select id="getInfoById" resultType="io.renren.modules.regul.ps02.dto.Ps02DTO">
        select t.*,
               (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
               (select x.teamname from b_tm01 x where x.tm0101 = t.tm0101) as teamname
          from B_PS02 t
         where t.ps0201 = #{ps0201}
    </select>
    <select id="salaryPage" resultType="io.renren.modules.regul.pa03.dto.Pa03DTO">
        select t.pa0301,
               t.accountdate,
               t.accountnum,
               t.partaccount,
               t.partname,
               t.description,
               a.name as projectname
          from B_PA03 t, b_pj01 a
         where t.pj0101 = a.pj0101
           and t.ps0201 = #{ps0201}
         order by t.accountdate desc
    </select>
    <select id="getAttendancePageList" resultType="io.renren.modules.regul.pj01.dto.Kq02PageDTO">
        select a.KQ0201,
               a.PERSON_NAME personName,
               a.PERSON_TYPE personType,
               a.DIRECTION,
               a.ATTENDTYPE  attendType,
               a.CHECKDATE   checkDate,
               a.IMAGE_URL   imageUrl
          from B_KQ02 a
         where a.USER_ID = #{ps0201}
         and a.PERSON_TYPE = '1'
         order by a.checkdate desc
    </select>
    <select id="selectListByTm0101" resultType="io.renren.modules.regul.ps02.dto.Ps02PageDTO">
        select a.name as personName,
               a.idcardnumber,
               a.gender,
               t.worktypecode,
               a.cellphone,
               t.isteamleader,
               t.entrytime,
               t.exittime,
               t.in_or_out,
               t.ps0201,
               t.ps0101
          from B_PS02 t, b_ps01 a
         where t.ps0101 = a.ps0101
           and t.tm0101 = #{tm0101}
         order by t.isteamleader desc
    </select>

    <update id="updateInOrOutByIds">
        update B_PS02 t
        set t.IN_OR_OUT = #{type},
        <if test="type == 1">
            t.entrytime  = sysdate,
            t.exittime = null
        </if>
        <if test="type == 2">
            t.exittime  = sysdate
        </if>
        where t.PS0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="countPs02ByTm0101" resultType="java.lang.Long">
        select count(1)
        from b_ps02 t1
                 inner join b_tm01 t2
                            on t1.tm0101 = t2.tm0101
        where t1.in_or_out = '1'
          and t2.tm0101 = #{tm0101}
    </select>
</mapper>