<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.kq07.dao.BKq07Dao">

    <select id="getWorkerApplyList" resultType = "io.renren.modules.regul.kq07.dto.BKq07DTO">
        select t.*,
               t1.audit_status auditStatus,
               t1.audit_result auditResult,
               t1.audit_time auditTime,
               t1.audit_name auditName,
               t2.jobtype,
               t4.ps0101,
               t5.name projectName
        from b_kq07 t
        inner join b_kq08 t1 on t1.kq0701 = t.kq0701
        inner join b_ps04 t2 on t.ps0401 = t2.ps0401
        inner join b_ps03 t3 on t2.ps0301 = t3.ps0301
        inner join b_ps01 t4 on t3.ps0101 = t4.ps0101
        inner join b_pj01 t5 on t5.pj0101 = t.pj0101
        <where>
            <if test="params.pj0101 != null and params.pj0101 != ''">
                and t.pj0101 = #{params.pj0101}
            </if>
            <if test="params.auditStatus != null and params.auditStatus != ''">
                and t1.audit_status = #{params.auditStatus}
            </if>
            <if test="params.name != null and params.name != ''">
                and t.name like '%' || #{params.name} || '%'
            </if>
            <if test="params.projectName != null and params.projectName != ''">
                and t5.name like '%' || #{params.projectName} || '%'
            </if>
        </where>
        order by apply_status
    </select>

</mapper>