<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.pj13.dao.Pj13Dao">

    <resultMap type="io.renren.modules.regul.pj13.entity.Pj13Entity" id="pj13Map">
        <result property="id" column="ID"/>
        <result property="exitId" column="EXIT_ID"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="auditstatus" column="AUDITSTATUS"/>
        <result property="auditor" column="AUDITOR"/>
        <result property="auditdate" column="AUDITDATE"/>
        <result property="auditreason" column="AUDITREASON"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="exitType" column="EXIT_TYPE"/>
    </resultMap>

    <select id="ps02ExitPage" resultType = "io.renren.modules.regul.pj13.dto.Ps02ExitPageDTO">
        select t2.name,
        t1.ps0201,
        t2.ps0101,
        t2.idcardnumber,
        t2.gender,
        t2.cellphone,
        t1.entrytime,
        a.auditstatus,
        a.auditreason,
        a.exittime,
        a.auditor,
        a.id,
        (select teamname from b_tm01 where tm0101 = t1.tm0101) teamName,
        t4.name projectName,
        t4.prjstatus
        from b_pj13 a
        inner join b_ps02 t1 on a.exit_id = t1.ps0201
        inner join b_ps01 t2 on t1.ps0101 = t2.ps0101
        inner join r_pj01_dept t3 on t3.pj0101 = t1.pj0101
        inner join b_pj01 t4 on t4.pj0101 = t1.pj0101
        where a.exit_type = '1' and t3.dept_id = #{params.deptId}
        <if test="params.name != null and params.name != ''">
            and t2.name like '%' || #{params.name} || '%'
        </if>
        <if test="params.auditstatus != null and params.auditstatus != ''">
            and auditstatus = #{params.auditstatus}
        </if>
        <if test="params.projectName != null and params.projectName != ''">
            and t4.name like '%' || #{params.projectName} || '%'
        </if>
        <if test="params.prjstatus != null and params.prjstatus != ''">
            and t4.prjstatus = #{params.prjstatus}
        </if>
        order by a.auditstatus, a.exittime desc
    </select>

    <select id="ps04ExitPage" resultType = "io.renren.modules.regul.pj13.dto.Ps04ExitPageDTO">
        select t3.name,
        t3.idcardnumber,
        t3.gender,
        t3.cellphone,
        t1.entrytime,
        t1.ps0401,
        t3.ps0101,
        t1.jobtype jobType,
        a.auditstatus,
        a.auditreason,
        (select corpname from b_cp01 where cp0101 = t2.cp0101) corpName,
        a.auditor,
        a.exittime,
        a.id,
        t5.name projectName,
        t5.prjstatus
        from b_pj13 a
        inner join b_ps04 t1 on t1.ps0401 = a.exit_id
        inner join b_ps03 t2 on t1.ps0301 = t2.ps0301
        inner join b_ps01 t3 on t3.ps0101 = t2.ps0101
        inner join r_pj01_dept t4 on t4.pj0101 = t1.pj0101
        inner join b_pj01 t5 on t5.pj0101 = t1.pj0101
        where a.exit_type = '2' and t4.dept_id = #{params.deptId}
        <if test="params.name != null and params.name != ''">
            and t3.name like '%' || #{params.name} || '%'
        </if>
        <if test="params.auditstatus != null and params.auditstatus != ''">
            and auditstatus = #{params.auditstatus}
        </if>
        <if test="params.projectName != null and params.projectName != ''">
            and t5.name like '%' || #{params.projectName} || '%'
        </if>
        <if test="params.prjstatus != null and params.prjstatus != ''">
            and t5.prjstatus = #{params.prjstatus}
        </if>
        order by a.auditstatus, a.exittime desc
    </select>

    <select id="tm01ExitPage" resultType = "io.renren.modules.regul.pj13.dto.Tm01ExitPageDTO">
        select t1.tm0101,
        t1.teamname,
        t1.responsiblepersonname,
        t1.responsiblepersonphone,
        t1.responsiblepersonidnumber,
        t1.entrytime,
        a.auditstatus,
        a.auditreason,
        a.exittime,
        a.auditor,
        a.id,
        t3.name projectName,
        t3.prjstatus
        from b_pj13 a
        inner join b_tm01 t1 on a.exit_id = t1.tm0101
        inner join r_pj01_dept t2 on t2.pj0101 = t1.pj0101
        inner join b_pj01 t3 on t3.pj0101 = t1.pj0101
        where exit_type = '3' and t2.dept_id = #{params.deptId}
        <if test="params.name != null and params.name != ''">
            and t1.teamname like '%' || #{params.name} || '%'
        </if>
        <if test="params.auditstatus != null and params.auditstatus != ''">
            and auditstatus = #{params.auditstatus}
        </if>
        <if test="params.projectName != null and params.projectName != ''">
            and t3.name like '%' || #{params.projectName} || '%'
        </if>
        <if test="params.prjstatus != null and params.prjstatus != ''">
            and t3.prjstatus = #{params.prjstatus}
        </if>
        order by a.auditstatus, a.exittime desc
    </select>
</mapper>