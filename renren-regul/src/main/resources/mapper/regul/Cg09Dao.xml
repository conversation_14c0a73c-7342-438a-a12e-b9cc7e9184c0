<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.cg09.dao.Cg09Dao">
    <select id="getFileTreeList" resultType="io.renren.modules.regul.cg09.dto.Cg09DTO">
        select a.cg0901      as id,
               a.PID,
               a.FILE_NUMBER as fileNumber,
               a.FILE_NAME   as fileName,
               a.type,
               a.sort,
               a.whether,
               a.TREE_LEVEL  as treeLevel,
               a.FILE_MODULE as fileModule,
               a.FILE_TYPE   as fileType
        from b_cg09 a
        where a.whether = '1'
        start with a.pid = nvl(#{pid}, 0)
        connect by prior a.cg0901 = a.pid
        order by a.sort, a.CG0901
    </select>
    <select id="getTableList" resultType="io.renren.modules.regul.cg09.dto.Cg09DTO">
        select a.cg0901                                                                                       as id,
               a.PID,
               a.FILE_NUMBER                                                                                  as fileNumber,
               a.FILE_NAME                                                                                    as fileName,
               a.type,
               a.sort,
               a.whether,
               (select count(0)
                from b_ot01 b
                where b.WHETHER = '1'
                  and b.BUSISYSNO = a.CG0901
                  and b.BUSITYPE = #{fileType})                                                               as fileNum,
               a.FILE_MODULE                                                                                  as fileModule,
               a.FILE_TYPE                                                                                    as fileType,
               (select b.URL
                from b_ot01 b
                where b.WHETHER = '1'
                  and b.BUSISYSNO = a.CG0901
                  and b.BUSITYPE = #{fileType}
                  and ROWNUM = 1)                                                                             as templateUrl
        from b_cg09 a
        where a.whether = '1'
          and a.TREE_LEVEL > 2
        <if test="id != '' and id != null">
            start with a.CG0901 = #{id}
            connect by prior a.cg0901 = a.pid
        </if>
        order by a.sort, a.CG0901
    </select>
</mapper>