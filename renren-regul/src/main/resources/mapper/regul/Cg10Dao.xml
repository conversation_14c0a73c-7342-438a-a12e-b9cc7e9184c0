<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.cg10.dao.Cg10Dao">
    <resultMap type="io.renren.modules.regul.cg10.entity.Cg10Entity" id="cg10Map">
        <result property="cg1001" column="CG1001"/>
        <result property="cg0901" column="CG0901"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="fileYear" column="FILE_YEAR"/>
        <result property="state" column="STATE"/>
        <result property="remark" column="REMARK"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="reviewers" column="REVIEWERS"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="selectPageList" resultType="io.renren.modules.regul.cg10.dto.Cg10Page">
        select * from(
        select b.NAME                           as projectName,
               d.FILE_NAME                      as fileName,
               d.FILE_NUMBER                    as fileNumber,
               d.type,
               c.state,
               C.CG1001,
               c.FILE_YEAR                      as fileYear,
               c.UPDATE_DATE                    as createDate,
               (select count(0)
                from b_ot01 e
                where e.BUSISYSNO = c.CG1001
                  and e.WHETHER = '1'
                  and e.BUSITYPE = #{fileType}) as fileCount
        from R_PJ01_DEPT a
                 inner join b_pj01 b on a.PJ0101 = b.PJ0101 and a.DEPT_ID = #{deptId}
                 inner join b_cg10 c on b.PJ0101 = c.PJ0101
                 inner join b_cg09 d on c.CG0901 = d.CG0901
        <where>
            <if test="projectName != null and projectName != ''">
                b.NAME like '%' || #{projectName} || '%'
            </if>
            <if test="state != null and state != ''">
                and c.STATE = #{state}
            </if>
        </where>
        order by c.STATE, c.UPDATE_DATE desc
        )
        where fileCount > 0
    </select>
    <update id="updateByState" parameterType="io.renren.modules.regul.cg10.dto.Cg10DTO">
        update b_cg10 a
        set a.STATE=#{state},
            a.REMARK=#{remark}
        where a.CG1001 = #{cg1001}
    </update>
</mapper>