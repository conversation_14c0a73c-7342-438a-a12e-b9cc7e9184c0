<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.pj01.dao.Pj01Dao">

    <select id="pageList" resultType="io.renren.modules.regul.pj01.dto.Pj01DTO">
        select rownum as sno, h.*,
                (select count(distinct(x.user_id))
                from b_kq02_now x
                where x.pj0101 = h.pj0101
                and to_char(x.checkdate, 'yyyy-MM-dd') =
                to_char(sysdate, 'yyyy-MM-dd')
                and x.person_type = '1') as kqworkers,
                (select count(distinct(x.user_id))
                from b_kq02_now x
                where x.pj0101 = h.pj0101
                and to_char(x.checkdate, 'yyyy-MM-dd') =
                to_char(sysdate, 'yyyy-MM-dd')
                and x.person_type = '2') as kqmanagers
          from (select t.pj0101,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1593508519516733442
                           and x.dict_value = b.areacode) as areacode,
                       b.areacode as virareacode,
                       t.name,
                       t.linkman,
                       t.linkphone,
                       t.prjstatus,
                       t.create_date,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 3
                           and x.dict_value = t.prjstatus) as prjstatusDictLabel,
                       (select x.dict_label
                          from sys_dict_data x
                         where x.dict_type_id = 1597158380227162113
                           and x.dict_value = t.stupstatus) as stupstatus,
                       t.startdate,
                       (select x.corpname
                          from b_cp01 x, b_cp02 y
                         where x.cp0101 = y.cp0101
                           and y.pj0101 = t.pj0101
                           and y.corptype = '7'
                           and rownum &lt; 2) as jianli,
                       (select x.corpname
                          from b_cp01 x, b_cp02 y
                         where x.cp0101 = y.cp0101
                           and y.pj0101 = t.pj0101
                           and y.corptype = '9'
                           and rownum &lt; 2) as zongbao,
                       (select x.name
                          from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 x
                         where x.ps0101 = y.ps0101
                           and y.ps0301 = z.ps0301
                           and z.pj0101 = t.pj0101
                           and z.cp0201 = x.cp0201
                           and x.corptype = '7'
                           and z.jobtype = '1001'
                           and z.in_or_out = '1'
                           and rownum &lt; 2) as zongjian,
                       (select x.name
                          from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 x
                         where x.ps0101 = y.ps0101
                           and y.ps0301 = z.ps0301
                           and z.pj0101 = t.pj0101
                           and z.cp0201 = x.cp0201
                           and x.corptype = '9'
                           and z.jobtype = '1009'
                           and z.in_or_out = '1'
                           and rownum &lt; 2) as jingli
                  from B_PJ01 t, r_pj01_dept a, sys_dept b
                 where t.pj0101 = a.pj0101
                   and a.dept_id = #{deptId}
                   and t.dept_id = b.id
                <if test="name !=null and name !=''">
                    and t.name like '%'||#{name}||'%'
                </if>
                <if test="areacode !=null and areacode !=''">
                    and b.areacode = #{areacode}
                </if>
                <if test="prjstatus !=null and prjstatus !=''">
                    and t.prjstatus = #{prjstatus}
                </if>
                <if test="startTime != null and startTime != ''">
                    and to_Char(t.create_date, 'yyyy-MM-dd') >= #{startTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    and to_Char(t.create_date, 'yyyy-MM-dd') &lt;= #{endTime}
                </if>
                <if test="stupstatus !=null and stupstatus !=''">
                    and t.stupstatus = #{stupstatus}
                </if> order by t.create_date desc) h
    </select>

    <select id="pageLists" resultType="io.renren.modules.regul.pj01.dto.Pj01CollectDTO">
        select rownum as sno, h.*,
        nvl(j.kqs, 0) as kqworkers,
        nvl(k.kqs, 0) as kqmanagers,
        nvl(o.ps02s, 0) as workers,
        nvl(p.ps04s, 0) as managers,
        nvl(mf.managerFileNum, 0) as managerFileNum,
        nvl(wf.workerFileNum, 0) as workerFileNum,
        to_char(nvl(ROUND(mf.managerFileNum / (p.ps04s) * 100, 2),0), 'fm999999999990.09')  || '%' AS specialManagerRate,
        to_char(nvl(ROUND(wf.workerFileNum / (o.ps02s) * 100, 2),0), 'fm999999999990.09')  || '%' AS specialWorkerRate
        from (select t.pj0101,
        (select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 1593508519516733442
        and x.dict_value = b.areacode) as areacode,
        b.areacode as virareacode,
        t.name,
        t.linkman,
        t.linkphone,
        t.prjstatus,
        t.create_date,
        (select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 3
        and x.dict_value = t.prjstatus) as prjstatusDictLabel,
        (select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 1597158380227162113
        and x.dict_value = t.stupstatus) as stupstatus,
        t.startdate,
        (select x.corpname
        from b_cp01 x, b_cp02 y
        where x.cp0101 = y.cp0101
        and y.pj0101 = t.pj0101
        and y.corptype = '7'
        and rownum &lt; 2) as jianli,
        (select x.corpname
        from b_cp01 x, b_cp02 y
        where x.cp0101 = y.cp0101
        and y.pj0101 = t.pj0101
        and y.corptype = '9'
        and rownum &lt; 2) as zongbao,
        q.name as zongjian,
        w.name as jingli,
        (
        SELECT CASE
            WHEN COUNT(*) > 0 THEN
            1
            ELSE
            0
            END
            FROM b_pj01_collect c
            WHERE c.user_id = #{userId}
            AND c.pj0101 = t.pj0101
        ) as iscollect
        from B_PJ01 t left join (select *
        from (select row_number() over(partition by a2.pj0101 order by a2.pj0101) a1,
        a2.name,
        a2.pj0101
        from (select z.pj0101, x.name
        from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 m
        where x.ps0101 = y.ps0101
        and y.ps0301 = z.ps0301
        and z.cp0201 = m.cp0201
        and m.corptype = '7'
        and z.jobtype = '1001'
        and z.in_or_out = '1') a2)
        where a1 = 1) q
        on t.pj0101 = q.pj0101
        left join (select *
        from (select row_number() over(partition by a2.pj0101 order by a2.pj0101) a1,
        a2.name,
        a2.pj0101
        from (select z.pj0101, x.name
        from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 m
        where x.ps0101 = y.ps0101
        and y.ps0301 = z.ps0301
        and z.cp0201 = m.cp0201
        and m.corptype = '9'
        and z.jobtype = '1009'
        and z.in_or_out = '1') a2)
        where a1 = 1) w
        on t.pj0101 = w.pj0101, r_pj01_dept a, sys_dept b
        where t.pj0101 = a.pj0101
        and a.dept_id = #{deptId}
        and t.dept_id = b.id
        <if test="name !=null and name !=''">
            and t.name like '%'||#{name}||'%'
        </if>
        <if test="areacode !=null and areacode !=''">
            and b.areacode = #{areacode}
        </if>
        <if test="prjstatus !=null and prjstatus !=''">
            and t.prjstatus = #{prjstatus}
        </if>
        <if test="startTime != null and startTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
        <if test="collectstatus != null and collectstatus != '' and collectstatus == 1">
            and EXISTS (select 1 from b_pj01_collect c where c.user_id = #{userId} and c.pj0101 = t.pj0101)
        </if>
        <if test="collectstatus != null and collectstatus != '' and collectstatus == 0">
            and NOT EXISTS (select 1 from b_pj01_collect c where c.user_id = #{userId} and c.pj0101 = t.pj0101)
        </if>
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if> order by t.create_date desc) h
        left join (select t.pj0101, count(distinct(t.user_id)) as kqs
        from b_kq02_now t
        where to_char(t.checkdate, 'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd')
        and t.person_type = '1'
        group by t.pj0101) j
        on h.pj0101 = j.pj0101
        left join (select t.pj0101, count(distinct(t.user_id)) as kqs
        from b_kq02_now t
        where to_char(t.checkdate, 'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd')
        and t.person_type = '2'
        group by t.pj0101) k
        on h.pj0101 = k.pj0101
        left join (select t.pj0101, count(1) as ps02s
        from b_ps02 t
        where t.in_or_out = '1'
        group by t.pj0101) o
        on h.pj0101 = o.pj0101
        left join (select t.pj0101, count(1) as ps04s
        from b_ps04 t
        where t.in_or_out = '1'
        group by t.pj0101) p
        on h.pj0101 = p.pj0101
        -- 新增
        left join (select p.pj0101, count(1) as managerFileNum
        from b_ot01 t, b_ps04 p
        where t.busisysno = p.ps0401
        and t.whether = 1
        and t.busitype = '54'
        and p.in_or_out = '1'
        group by p.pj0101) mf
        on h.pj0101 = mf.pj0101
        left join (select p.pj0101, count(1) as workerFileNum
        from b_ot01 t, b_ps02 p
        where t.busisysno = p.ps0201
        and t.whether = 1
        and t.busitype = '13'
        and p.in_or_out = '1'
        group by p.pj0101) wf
        on h.pj0101 = wf.pj0101
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.regul.pj01.dto.Pj01DTO">
        select t.*,a.areacode as virareacode from b_pj01 t,sys_dept a where t.dept_id = a.id and t.pj0101 = #{pj0101}
    </select>
    <select id="loadUserProjectInfo" resultType="io.renren.modules.regul.pj01.dto.Pj01DTO">
        select *
        from B_PJ01 t
        where t.DEPT_ID = #{deptId}
    </select>
    <!--    参建单位列表分页查询SQL-->
    <select id="getCp02PageList" resultType="io.renren.modules.regul.pj01.dto.Cp02PageDTO">
        select t.cp0201,
               t.cp0101,
               a.corpname,
               a.corpcode,
               a.linkman,
               a.linkcellphone,
               t.corptype
        from b_cp02 t,
             b_cp01 a
        where t.cp0101 = a.cp0101
          and t.pj0101 = #{pj0101}
    </select>

    <select id="getTeamPageList" resultType="io.renren.modules.regul.pj01.dto.Tm01PageDTO">
        select t.responsiblepersonname,
               t.tm0101,
               t.cp0201,
               t.pj0101,
               t.teamname,
               t.entrytime,
               t.exittime,
               t.IN_OR_OUT,
               a.corptype,
               b.corpname
          from b_tm01 t, b_cp02 a, b_cp01 b
         where t.cp0201 = a.cp0201
           and a.cp0101 = b.cp0101
           and t.pj0101 = #{pj0101}
        <if test="teamname != null and teamname != ''">
            and t.teamname like '%' || #{teamname} || '%'
        </if>
        <if test="cp0201 != null and cp0201 != ''">
            and t.cp0201 = #{cp0201}
        </if>
    </select>

    <select id="getWorkerPageList" resultType="io.renren.modules.regul.pj01.dto.Ps02PageDTO">
        select a.name as personName,
               t.ps0101,
               t.ps0201,
               a.idcardnumber,
               a.cellphone,
               a.gender,
               t.worktypecode,
               b.teamname,
               t.isteamleader as isTeamLeader,
               t.in_or_out,
        -- 新增
        (
        SELECT CASE
        WHEN COUNT(*) > 0 THEN
        1
        ELSE
        0
        END
        FROM b_ot01 c
        WHERE c.busisysno = t.ps0201
        and c.busitype = '13'
        and c.whether = '1'
        ) as isUploadWorkerFile
          from b_ps02 t, b_ps01 a, b_tm01 b
         where t.ps0101 = a.ps0101
           and t.tm0101 = b.tm0101
           and t.in_or_out = '1'
           and t.pj0101 = #{pj0101}
        <if test="personName != null and personName != ''">
            and a.name like '%' || #{personName} || '%'
        </if>
        <if test="tm0101 != null and tm0101 != ''">
            and t.tm0101 =  #{tm0101}
        </if>
    </select>

    <select id="getAttendancePageList" resultType="io.renren.modules.regul.pj01.dto.Kq02PageDTO">
        select a.KQ0201,
               a.PERSON_NAME personName,
               a.PERSON_TYPE personType,
               a.DIRECTION,
               a.ATTENDTYPE  attendType,
               a.CHECKDATE   checkDate,
               a.IMAGE_URL   imageUrl
          from B_KQ02 a
         where a.pj0101 = #{pj0101}
         and a.person_type is not null
        <if test="startTime != null and startTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
         order by a.checkdate desc
    </select>

    <select id="getManagerPageList" resultType="io.renren.modules.regul.pj01.dto.Ps04PageDTO">
        select t.PS0401,
               a.ps0101,
               b.NAME,
               b.idcardnumber,
               b.cellphone,
               b.GENDER,
               t.jobtype,
               t.in_or_out,
               (select x.corpname
                  from b_cp01 x, b_cp02 y
                 where x.cp0101 = y.cp0101
                   and y.cp0201 = t.cp0201) as corpname,
               (select y.corptype
                  from b_cp01 x, b_cp02 y
                 where x.cp0101 = y.cp0101
                   and y.cp0201 = t.cp0201) as corptype,
        -- 新增
        (
        SELECT CASE
        WHEN COUNT(*) > 0 THEN
        1
        ELSE
        0
        END
        FROM b_ot01 c
        WHERE c.busisysno = t.ps0401
        and c.busitype = '54'
        and c.whether = '1'
        ) as isUploadManagerFile
          from B_PS04 t, b_ps03 a, b_ps01 b
         where t.ps0301 = a.ps0301
           and a.ps0101 = b.ps0101
           and t.in_or_out = '1'
           and t.pj0101 = #{pj0101}
        <if test="name != null and name != ''">
            and b.name like '%' || #{name} || '%'
        </if>
        <if test="cp0201 != null and cp0201 != ''">
            and t.cp0201 = #{cp0201}
        </if>
        <if test="jobtype != null and jobtype != ''">
            and t.jobtype = #{jobtype}
        </if>
    </select>
    <insert id="updateStUpPrjstatus">
        insert into zjnmg.project_status_info
          (pj0101, prj_status, status_change_date, report_status, id,busiid)
        values
          (#{pj0101}, #{prjstatus}, sysdate, '0', zjnmg.seq_project_status_info.nextval,#{pj0101})
    </insert>
    <select id="projectPage" resultType="io.renren.modules.regul.pj01.dto.Pj01DTO">
        select t.name,
               t.prjstatus,
               c.areacode as virareacode,
               t.startdate,
               t.complete_date,
               t.linkman,
               t.linkphone,
               a.corptype,
               a.entrytime,
               a.exittime,
               a.in_or_out,
               t.pj0101
          from B_PJ01 t, b_cp02 a, b_cp01 b, sys_dept c
         where t.pj0101 = a.pj0101
           and a.cp0101 = b.cp0101
           and t.dept_id = c.id
           and b.cp0101 = #{cp0101}
    </select>
    <select id="selectListByIds" resultType="io.renren.modules.regul.pj01.dto.Pj01DTO">
        select t.pj0101,
               t.name,
               t.prjstatus,
               t.areacode,
               t.startdate,
               t.linkman,
               t.linkphone
          from B_PJ01 t
         where t.pj0101 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="inOrOutPageList" resultType="io.renren.modules.regul.inorout.dto.InOrOutDTO">
        select t.pj0101,
               t.name,
               t.prjstatus,
               a.areacode,
               (select count(1) from b_tm01 x where x.pj0101 = t.pj0101) as teamnum,
               (select count(1)
                  from b_tm01 x
                 where x.pj0101 = t.pj0101
                   and x.exittime is null) as inteamnum,
               (select count(1)
                  from b_tm01 x
                 where x.pj0101 = t.pj0101
                   and x.exittime is not null) as outteamnum,
               (select count(1) from b_ps02 x where x.pj0101 = t.pj0101) as workernum,
               (select count(1)
                  from b_ps02 x
                 where x.pj0101 = t.pj0101
                   and x.exittime is null) as inworkernum,
               (select count(1)
                  from b_ps02 x
                 where x.pj0101 = t.pj0101
                   and x.exittime is not null) as outworkernum
          from B_PJ01 t, sys_dept a, r_pj01_dept b
         where t.dept_id = a.id
           and t.pj0101 = b.pj0101
           and b.dept_id = #{deptId}
        <if test="projectname != null and projectname != ''">
            and t.name like '%' || #{projectname} || '%'
        </if>
        <if test="virareacode != null and virareacode != ''">
            and a.areacode = #{virareacode}
        </if>
    </select>
    <select id="getTeamInOrOutPageList" resultType="io.renren.modules.regul.inorout.dto.TeamInOrOutDTO">
        select t.*,
               (select x.corpname
                  from b_cp01 x, b_cp02 y
                 where x.cp0101 = y.cp0101
                   and y.cp0201 = t.cp0201) as corpname,
                (select x.corptype
                  from b_cp02 x
                 where x.cp0201 = t.cp0201) as corptype,
               (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as name
          from B_TM01 t
         where t.pj0101 = #{pj0101}
    </select>
    <select id="getWorkerInOrOutPageList" resultType="io.renren.modules.regul.inorout.dto.Ps02InOrOutDTO">
        select a.name as projectname,
               b.teamname,
               c.name,
               c.gender,
               c.idcardnumber,
               c.cellphone,
               t.*
          from B_PS02 t, b_pj01 a, b_tm01 b, b_ps01 c
         where t.ps0101 = c.ps0101
           and t.pj0101 = a.pj0101
           and t.tm0101 = b.tm0101
           and t.pj0101 = #{pj0101}
    </select>
    <select id="areaStatistics" resultType="io.renren.modules.regul.statistics.dto.AreaStatisticsDTO">
        select t.dict_value as areacode,
               t.dict_label as areaname,
               (select count(1)
                  from b_pj01 x, sys_dept y
                 where x.dept_id = y.id
                   and y.areacode = t.dict_value) as projects,
               (select count(1)
                  from b_pj01 x, sys_dept y
                 where x.dept_id = y.id
                   and x.prjstatus = '3'
                   and y.areacode = t.dict_value) as zjprojects,
               (select count(1)
                  from b_cp02 x, b_pj01 y, sys_dept z
                 where x.pj0101 = y.pj0101
                   and y.dept_id = z.id
                   and z.areacode = t.dict_value) as companys,
               (select count(1)
                  from b_ps02 x, b_pj01 y, sys_dept z
                 where x.pj0101 = y.pj0101
                   and y.dept_id = z.id
                   and z.areacode = t.dict_value) as workers
          from sys_dict_data t
         where t.dict_type_id = 1593508519516733442
           and t.dict_value like #{areacode}||'%'
    </select>
    <select id="workerStatistics" resultType="io.renren.modules.regul.statistics.dto.WorkerStatisticsDTO">
        select t.worktypecode,
               t.workers,
               t.inworkers,
               t.kqworkers,
               (t.inworkers - t.kqworkers) as lateworkers,
               #{statisticsdate} as statisticsdate
          from (select t.dict_label as worktypecode,
                       (select count(1)
                          from b_ps02 x, r_pj01_dept y
                         where x.pj0101 = y.pj0101
                           and y.dept_id = #{deptId}
                           and x.worktypecode = t.dict_value) as workers,
                       (select count(1)
                          from b_ps02 x, r_pj01_dept y
                         where x.pj0101 = y.pj0101
                           and y.dept_id = #{deptId}
                           and x.in_or_out = '1'
                           and x.worktypecode = t.dict_value) as inworkers,
                       (select count(1)
                          from b_ps02 x, r_pj01_dept y, b_kq02 z
                         where x.pj0101 = y.pj0101
                           and y.dept_id = #{deptId}
                           and x.ps0201 = z.user_id
                           and to_char(z.checkdate, 'yyyy-MM-dd') = #{statisticsdate}
                           and x.worktypecode = t.dict_value) as kqworkers
                  from sys_dict_data t
                 where t.dict_type_id = 206) t
         order by t.workers desc
    </select>
    <select id="attendance1Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance1StatisticsDTO">
        SELECT
        p.name,
        t.pj0101,
        COUNT(DISTINCT CASE
        WHEN t.PERSON_TYPE = '2' THEN t.USERID
        END) AS shouldmanagers,
        COUNT(DISTINCT CASE
        WHEN t.PERSON_TYPE = '2'
        AND t.KQSTATUS = '1' THEN t.USERID
        END) AS actualmanagers,
        CASE
        WHEN COUNT(DISTINCT CASE WHEN t.PERSON_TYPE = '2' THEN t.USERID END) = 0
        THEN '0.00%'
        ELSE TO_CHAR(ROUND(
        COUNT(DISTINCT CASE
        WHEN t.PERSON_TYPE = '2' AND t.KQSTATUS = '1'
        THEN t.USERID
        END) * 100.0 /
        COUNT(DISTINCT CASE WHEN t.PERSON_TYPE = '2' THEN t.USERID END),
        2), 'FM9990.00') || '%'
        END AS managerpercents,
        COUNT(DISTINCT CASE
        WHEN t.PERSON_TYPE = '1' THEN t.USERID
        END) AS shouldworkers,
        COUNT(DISTINCT CASE
        WHEN t.PERSON_TYPE = '1'
        AND t.KQSTATUS = '1' THEN t.USERID
        END) AS actualworkers,
        CASE
        WHEN COUNT(DISTINCT CASE WHEN t.PERSON_TYPE = '1' THEN t.USERID END) = 0
        THEN '0.00%'
        ELSE TO_CHAR(ROUND(
        COUNT(DISTINCT CASE
        WHEN t.PERSON_TYPE = '1' AND t.KQSTATUS = '1'
        THEN t.USERID
        END) * 100.0 /
        COUNT(DISTINCT CASE WHEN t.PERSON_TYPE = '1' THEN t.USERID END),
        2), 'FM9990.00') || '%'
        END AS workerpercents
        FROM b_tj04 t
        LEFT JOIN b_pj01 p ON t.pj0101 = p.pj0101
        WHERE 1 = 1
        <if test="projectName !=null and projectName !=''">
            and p.name like '%'||#{projectName}||'%'
        </if>
        <if test="prjstatus !=null and prjstatus !=''">
            and p.prjstatus = #{prjstatus}
        </if>
        <choose>
            <when test="tjdate != null and tjdate != ''">
                <choose>
                    <when test="tjdate.length() == 10">
                        AND TO_CHAR(t.KQDAY, 'yyyy-MM-dd') = #{tjdate}
                    </when>
                    <when test="tjdate.length() == 7">
                        AND TO_CHAR(t.KQDAY, 'yyyy-MM') = #{tjdate}
                    </when>
                    <when test="tjdate.length() == 4">
                        AND TO_CHAR(t.KQDAY, 'yyyy') = #{tjdate}
                    </when>
                </choose>
            </when>
        </choose>
        GROUP BY p.name, t.pj0101
    </select>
    <select id="attendance2Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance2StatisticsDTO">
        select t.name,
               t.areacode,
               t.startdate,
               t.jobtype,
               t.shouldkqs,
               t.actualkqs,
               round(decode(t.shouldkqs, 0, 0, (t.actualkqs / t.shouldkqs) * 100),
                     2) || '%' as kqpercents
          from (select t.name,
                       a.areacode,
                       t.startdate,
                       b.jobtype,
                       round((sysdate - t.startdate) * 0.73, 0) shouldkqs,
                       (select count(distinct(to_char(x.checkdate, 'yyyy-MM-dd')))
                          from b_kq02 x
                         where x.user_id = b.ps0401
                           and x.checkdate > t.startdate) as actualkqs
                  from b_pj01 t, sys_dept a, b_ps04 b, r_pj01_dept c
                 where t.dept_id = a.id
                   and t.pj0101 = b.pj0101
                   and t.pj0101 = c.pj0101
                   and b.jobtype in ('1001', '1009')
                   and c.dept_id = #{deptId}
                <if test="areacode !=null and areacode !=''">
                    and a.areacode = #{areacode}
                </if>
                <if test="name !=null and name !=''">
                    and t.name like '%'||#{name}||'%'
                </if>
                   ) t
    </select>
    <select id="attendance3Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance3StatisticsDTO">
        select tj0101,
               sort,
               areacode as virareacode,
               zjprojects,
               kqprojects,
               nokqprojects,
               kqworkers,
               avgworkers,
               updatepercents,
               description,
               tjdate,
               (select x.dict_label
                  from sys_dict_data x, sys_dict_type y
                 where x.dict_type_id = y.id
                   and y.dict_type = 'VIRAREACODE'
                   and x.dict_value = t.areacode) as areacode
          from b_tj01 t
         where t.areacode like #{areacode} || '%'
           and to_char(t.tjdate, 'yyyy-MM-dd') = #{tjdate}
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if>
        <if test="stupstatus == null or stupstatus==''">
            and t.stupstatus is null
        </if>
           order by t.sort
    </select>
    <select id="attendance31Statistics" resultType="java.lang.String">
        select t.description
          from B_TJ01 t
         where t.areacode = #{areacode}
           and to_char(t.tjdate, 'yyyy-MM-dd') = #{tjdate}
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if>
        <if test="stupstatus == null or stupstatus==''">
            and t.stupstatus is null
        </if>
    </select>
    <select id="attendance32Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance32StatisticsDTO">
        SELECT
        p.name,
        c.corpcode,
        c.corpname,
        COUNT(DISTINCT CASE WHEN t.IN_OR_OUT = '1' THEN t.USERID END) AS inworkers,
        COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' THEN t.USERID END) AS kqworkers,
        CASE
        WHEN COUNT(DISTINCT CASE WHEN t.IN_OR_OUT = '1' THEN t.USERID END) = 0 THEN
        '0.00%'
        ELSE
        TO_CHAR(ROUND(
        COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' THEN t.USERID END) * 100.0 /
        COUNT(DISTINCT CASE WHEN t.IN_OR_OUT = '1' THEN t.USERID END), 2),
        'FM990.00') || '%'
        END AS updatepercents,
        p.prjstatus
        FROM
        b_tj04 t
        LEFT JOIN
        b_pj01 p ON t.pj0101 = p.pj0101
        LEFT JOIN
        b_cp02 cp2 ON t.pj0101 = cp2.pj0101 AND cp2.corptype = '9'
        LEFT JOIN
        b_cp01 c ON cp2.cp0101 = c.cp0101
        WHERE
        1 = 1
        <if test="name !=null and name !=''">
            AND p.name LIKE '%'||#{name}||'%'
        </if>
        <if test="prjstatus !=null and prjstatus !=''">
            AND p.prjstatus = #{prjstatus}
        </if>
        <choose>
            <when test="tjdate != null and tjdate != ''">
                <choose>
                    <when test="tjdate.length() == 10">
                        AND TO_CHAR(t.kqday, 'yyyy-MM-dd') = #{tjdate}
                    </when>
                    <when test="tjdate.length() == 7">
                        AND TO_CHAR(t.kqday, 'yyyy-MM') = #{tjdate}
                    </when>
                    <when test="tjdate.length() == 4">
                        AND TO_CHAR(t.kqday, 'yyyy') = #{tjdate}
                    </when>
                </choose>
            </when>
        </choose>
        GROUP BY
        p.name, c.corpcode, c.corpname, p.prjstatus
        <if test="iskq !=null and iskq !='' and iskq == 1">
            HAVING COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' THEN t.USERID END) > 0
        </if>
        <if test="iskq !=null and iskq !='' and iskq == 0">
            HAVING COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' THEN t.USERID END) = 0
        </if>
    </select>
    <select id="attendance4Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance4StatisticsDTO">
        select tj0201,
               sort,
               areacode as virareacode,
               zjprojects,
               kqprojects,
               inmanagers,
               kqmanagers,
               synthesizepercent,
               zongbaopercent,
               jinglipercent,
               jianlipercent,
               zongjianpercent,
               jingandjianpercent,
               jianshepercent,
               qitapercent,
               description,
               tjdate,
               (select x.dict_label
                  from sys_dict_data x, sys_dict_type y
                 where x.dict_type_id = y.id
                   and y.dict_type = 'VIRAREACODE'
                   and x.dict_value = t.areacode) as areacode
          from b_tj02 t
         where t.areacode like #{areacode} || '%'
           and to_char(t.tjdate, 'yyyy-MM-dd') = #{tjdate}
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if>
        <if test="stupstatus == null or stupstatus==''">
            and t.stupstatus is null
        </if>
            order by t.sort
    </select>
    <select id="attendance41Statistics" resultType="java.lang.String">
        select t.description
          from B_TJ02 t
         where t.areacode = #{areacode}
           and to_char(t.tjdate, 'yyyy-MM-dd') = #{tjdate}
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if>
        <if test="stupstatus == null or stupstatus==''">
            and t.stupstatus is null
        </if>
    </select>
    <select id="attendance42Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance42StatisticsDTO">
        SELECT
        p.name AS name,
        c.corpname AS corpname,
        COUNT(DISTINCT t.USERID) AS inmanagers,
        COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' THEN t.USERID END) AS kqmanagers,
        CASE
        WHEN COUNT(DISTINCT t.USERID) = 0 THEN '0.00%'
        ELSE TO_CHAR(ROUND(COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' THEN t.USERID END) * 100.0 / COUNT(DISTINCT t.USERID), 2), 'FM990.00') || '%'
        END AS synthesizepercent,
        COUNT(DISTINCT CASE WHEN cp2.corptype = '9' THEN t.USERID END) AS zonginmanagers,
        COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype = '9' THEN t.USERID END) AS zongkqmanagers,
        CASE
        WHEN COUNT(DISTINCT CASE WHEN cp2.corptype = '9' THEN t.USERID END) = 0 THEN '0.00%'
        ELSE TO_CHAR(ROUND(COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype = '9' THEN t.USERID END) * 100.0 / COUNT(DISTINCT CASE WHEN cp2.corptype = '9' THEN t.USERID END), 2), 'FM990.00') || '%'
        END AS zongpercent,
        COUNT(DISTINCT CASE WHEN cp2.corptype = '7' THEN t.USERID END) AS jianinmanagers,
        COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype = '7' THEN t.USERID END) AS jiankqmanagers,
        CASE
        WHEN COUNT(DISTINCT CASE WHEN cp2.corptype = '7' THEN t.USERID END) = 0 THEN '0.00%'
        ELSE TO_CHAR(ROUND(COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype = '7' THEN t.USERID END) * 100.0 / COUNT(DISTINCT CASE WHEN cp2.corptype = '7' THEN t.USERID END), 2), 'FM990.00') || '%'
        END AS jianpercent,
        COUNT(DISTINCT CASE WHEN cp2.corptype &lt;> '9' AND cp2.corptype &lt;> '8' AND cp2.corptype &lt;> '7' THEN t.USERID END) AS qitainmanagers,
        COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype &lt;> '9' AND cp2.corptype &lt;> '8' AND cp2.corptype &lt;> '7' THEN t.USERID END) AS qitakqmanagers,
        CASE
        WHEN COUNT(DISTINCT CASE WHEN cp2.corptype &lt;> '9' AND cp2.corptype &lt;> '8' AND cp2.corptype &lt;> '7' THEN t.USERID END) = 0 THEN '0.00%'
        ELSE TO_CHAR(ROUND(COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype &lt;> '9' AND cp2.corptype &lt;> '8' AND cp2.corptype &lt;> '7' THEN t.USERID END) * 100.0 / COUNT(DISTINCT CASE WHEN cp2.corptype &lt;> '9' AND cp2.corptype &lt;> '8' AND cp2.corptype &lt;> '7' THEN t.USERID END), 2), 'FM990.00') || '%'
        END AS qitapercent,
        COUNT(DISTINCT CASE WHEN cp2.corptype = '8' THEN t.USERID END) AS sheinmanagers,
        COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype = '8' THEN t.USERID END) AS shekqmanagers,
        CASE
        WHEN COUNT(DISTINCT CASE WHEN cp2.corptype = '8' THEN t.USERID END) = 0 THEN '0.00%'
        ELSE TO_CHAR(ROUND(COUNT(DISTINCT CASE WHEN t.KQSTATUS = '1' AND cp2.corptype = '8' THEN t.USERID END) * 100.0 / COUNT(DISTINCT CASE WHEN cp2.corptype = '8' THEN t.USERID END), 2), 'FM990.00') || '%'
        END AS shepercent
        FROM
        B_TJ04 t
        LEFT JOIN
        B_PJ01 p ON t.PJ0101 = p.PJ0101
        LEFT JOIN
        B_CP02 cp2 ON t.PJ0101 = cp2.PJ0101 AND cp2.corptype = '9'
        LEFT JOIN
        B_CP01 c ON cp2.CP0101 = c.CP0101
        WHERE
        1 = 1
        <choose>
            <when test="tjdate != null and tjdate != ''">
                <choose>
                    <when test="tjdate.length() == 10">
                        AND TO_CHAR(t.KQDAY, 'yyyy-MM-dd') = #{tjdate}
                    </when>
                    <when test="tjdate.length() == 7">
                        AND TO_CHAR(t.KQDAY, 'yyyy-MM') = #{tjdate}
                    </when>
                    <when test="tjdate.length() == 4">
                        AND TO_CHAR(t.KQDAY, 'yyyy') = #{tjdate}
                    </when>
                </choose>
            </when>
        </choose>
        <if test="name !=null and name !=''">
            AND p.name LIKE '%' || #{name} || '%'
        </if>
        <if test="prjstatus !=null and prjstatus !=''">
            AND p.prjstatus = #{prjstatus}
        </if>
        GROUP BY
        p.name,
        c.corpname
    </select>
    <select id="generate" statementType="CALLABLE">
        {call PC_TJ_ATTENDANCE(#{Cpj0101,mode=IN,jdbcType=VARCHAR},
                            #{Ckqmonth,mode=IN,jdbcType=VARCHAR})}
    </select>
    <select id="attendance43Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance43StatisticsDTO">
        select rownum as sno,g.* from (
        select h.*,
        round(decode(days, 0, 0, (nvl(kqs, 0) / days) * 100), 2) || '%' as arrivepercent
        from (select t.ps0401 as user_id,
        b.name,
        q.name projectname,
        substr(b.idcardnumber,0,4)||'************'||substr(b.idcardnumber,17,2) as idcardnumber,
        (select x.corpname from b_cp01 x where x.cp0101 = a.cp0101) as corpname,
        (select x.dict_label
        from sys_dict_data x, sys_dict_type y
        where x.dict_type_id = y.id
        and y.dict_type = 'JOBTYPE'
        and x.dict_value = t.jobtype) as jobtype,
        (select count(1)
        from b_tj04 x
        where x.pj0101 = t.pj0101
        and x.userid = t.ps0401
        and x.person_type = '2'
        and x.kqstatus = '1'
        and to_char(x.kqday, 'yyyy-MM-dd') &gt;= #{startdate}
        and to_char(x.kqday, 'yyyy-MM-dd') &lt;= #{enddate}) as kqs,
        to_date(#{enddate}, 'yyyy-MM-dd') -
        to_date(#{startdate}, 'yyyy-MM-dd')+1 as days
        from B_PS04 t, b_ps03 a, b_ps01 b,b_pj01 q
        where t.ps0301 = a.ps0301
        and a.ps0101 = b.ps0101
        and t.pj0101 = q.pj0101
        <if test="pj0101 !=null and pj0101 !=''">
            and t.pj0101 = #{pj0101}
        </if>
        and t.in_or_out = '1') h
        where 1=1
        <if test="name !=null and name !=''">
            and h.name like '%'||#{name}||'%'
        </if>
        <if test="projectname !=null and projectname !=''">
            and h.projectname like '%'||#{projectname}||'%'
        </if>
        order by h.kqs desc
        ) g
    </select>
    <select id="attendance43CPStatistics" resultType="io.renren.modules.regul.statistics.dto.Attendance43StatisticsDTO">
        select rownum as sno,g.* from (
        select h.*,
        round(decode(days, 0, 0, (nvl(kqs, 0) / days) * 100), 2) || '%' as arrivepercent
        from (select t.ps0401 as user_id,c.name as projectname,
        b.name,substr(b.idcardnumber,0,4)||'************'||substr(b.idcardnumber,17,2) as idcardnumber,
        (select x.corpname from b_cp01 x where x.cp0101 = a.cp0101) as corpname,
        (select x.dict_label
        from sys_dict_data x, sys_dict_type y
        where x.dict_type_id = y.id
        and y.dict_type = 'CORPTYPE'
        and x.dict_value = e.corptype) as corptype,
        (select x.dict_label
        from sys_dict_data x, sys_dict_type y
        where x.dict_type_id = y.id
        and y.dict_type = 'JOBTYPE'
        and x.dict_value = t.jobtype) as jobtype,
        (select count(1)
        from b_tj04 x
        where x.pj0101 = t.pj0101
        and x.userid = t.ps0401
        and x.person_type = '2'
        and x.kqstatus = '1'
        and to_char(x.kqday, 'yyyy-MM-dd') &gt;= #{startdate}
        and to_char(x.kqday, 'yyyy-MM-dd') &lt;= #{enddate}) as kqs,
        (select count(*) from b_pj01_operation a where a.pj0101 = c.pj0101
        and to_char(a.OPERATIONDAY, 'yyyy-MM-dd') &gt;= #{startdate}
        and to_char(a.OPERATIONDAY, 'yyyy-MM-dd') &lt;= #{enddate}) as days,
        c.create_date
        from B_PS04 t, b_ps03 a, b_ps01 b,b_pj01 c,sys_dept d,b_cp02 e
        where t.ps0301 = a.ps0301
          and t.pj0101=c.pj0101
          and c.dept_id=d.id
          and t.cp0201=e.cp0201
          and e.corptype in ('7','9','8')
          and c.prjstatus='3'
        and a.ps0101 = b.ps0101 and t.jobtype in ('1001','1009','1011')
        and t.in_or_out = '1') h
        where 1=1 and h.kqs &lt; 10
        <if test="name !=null and name !=''">
            and h.name like '%'||#{name}||'%'
        </if>
        order by h.corpname,h.kqs desc
        ) g
    </select>
    <select id="attendance44Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance44StatisticsDTO">
        select rownum as sno,g.* from (
        select h.*,
        round(decode(days, 0, 0, (nvl(kqs, 0) / days) * 100), 2) || '%' as arrivepercent
        from (select t.ps0401 as user_id,
        b.name,substr(b.idcardnumber,0,4)||'************'||substr(b.idcardnumber,17,2) as idcardnumber,
        (select x.corpname from b_cp01 x where x.cp0101 = a.cp0101) as corpname,
        (select x.dict_label
        from sys_dict_data x, sys_dict_type y
        where x.dict_type_id = y.id
        and y.dict_type = 'JOBTYPE'
        and x.dict_value = t.jobtype) as jobtype,
        (select count(1)
        from b_tj04 x
        where x.pj0101 = t.pj0101
        and x.userid = t.ps0401
        and x.person_type = '2'
        and x.kqstatus = '1'
        and to_char(x.kqday, 'yyyy-MM') &gt;= #{startdate}
        and to_char(x.kqday, 'yyyy-MM') &lt;= #{enddate}) as kqs,
        LAST_DAY(to_date(#{enddate}, 'yyyy-MM')) -
        to_date(#{startdate}, 'yyyy-MM')+1 as days
        from B_PS04 t, b_ps03 a, b_ps01 b
        where t.ps0301 = a.ps0301
        and a.ps0101 = b.ps0101
        and t.pj0101 = #{pj0101}
        and t.in_or_out = '1') h
        where 1=1
        <if test="name !=null and name !=''">
            and h.name like '%'||#{name}||'%'
        </if>
        order by h.kqs desc
        ) g
    </select>
    <select id="attendance44SJStatistics" resultType="io.renren.modules.regul.statistics.dto.Attendance44StatisticsDTO">
        select rownum as sno,g.* from (
        select h.*,
        round(decode(days, 0, 0, (nvl(kqs, 0) / days) * 100), 2) || '%' as arrivepercent
        from (select t.ps0401 as user_id,c.name as projectname,
        b.name,substr(b.idcardnumber,0,4)||'************'||substr(b.idcardnumber,17,2) as idcardnumber,
        (select x.corpname from b_cp01 x where x.cp0101 = a.cp0101) as corpname,
        (select x.dict_label
        from sys_dict_data x, sys_dict_type y
        where x.dict_type_id = y.id
        and y.dict_type = 'JOBTYPE'
        and x.dict_value = t.jobtype) as jobtype,
        (select count(1)
        from b_tj04 x
        where x.pj0101 = t.pj0101
        and x.userid = t.ps0401
        and x.person_type = '2'
        and x.kqstatus = '1'
        and to_char(x.kqday, 'yyyy-MM') &gt;= #{startdate}
        and to_char(x.kqday, 'yyyy-MM') &lt;= #{enddate}) as kqs,
        LAST_DAY(to_date(#{enddate}, 'yyyy-MM')) -
        to_date(#{startdate}, 'yyyy-MM')+1 as days
        from B_PS04 t, b_ps03 a, b_ps01 b,b_pj01 c,sys_dept d,b_cp02 e
        where t.ps0301 = a.ps0301 and t.pj0101=c.pj0101 and c.dept_id=d.id and t.cp0201=e.cp0201 and e.corptype in ('7','9') and c.prjstatus='3'
        and a.ps0101 = b.ps0101 and t.jobtype in ('1001','1009') and d.areacode='511571'
        and t.in_or_out = '1') h
        where 1=1
        <if test="name !=null and name !=''">
            and h.name like '%'||#{name}||'%'
        </if>
        order by h.projectname,h.kqs desc
        ) g
    </select>
    <select id="getAttendanceDayList" resultType="io.renren.modules.regul.statistics.dto.AttendanceDayDTO">
        select substr(t.day, 4, 2) as day,
               case
                 when h.kqstatus = '1' then
                  '√'
                 when h.kqstatus = '2' then
                  '△'
                 else
                  ''
               end as iskq
          from sys_days t
          left join (select to_char(x.kqday, 'MM-dd') as kqday, x.kqstatus
                       from b_tj04 x
                      where x.userid = #{userId}
                        and x.person_type = '2'
                        and to_char(x.kqday, 'yyyy-MM') = substr(#{startdate}, 0, 7)) h
            on t.day = h.kqday
         where t.day &lt;= substr(#{enddate}, 6, 5)
           and t.day &gt;= substr(#{startdate}, 6, 5)
           order by t.day
    </select>
    <select id="getAttendanceMonthList" resultType="io.renren.modules.regul.statistics.dto.AttendanceMonthDTO">
        select t.month, h.kqs
          from sys_months t
          left join (select to_char(x.kqday, 'yyyy-MM') as kqmonth, count(1) as kqs
                       from b_tj04 x
                      where x.userid = #{userId}
                        and x.person_type = '2'
                        and x.kqstatus = '1'
                        and to_char(x.kqday, 'yyyy-MM') &gt;= #{startdate}
                        and to_char(x.kqday, 'yyyy-MM') &lt;= #{enddate}
                      group by to_char(x.kqday, 'yyyy-MM')) h
            on t.month = h.kqmonth
         where t.month &lt;= #{enddate}
           and t.month &gt;= #{startdate}
           order by t.month
    </select>
    <select id="attendance5Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance5StatisticsDTO">
        select tj0301,
               zjprojects,
               nojingliprojects,
               nokqjingliprojects,
               nozongjianprojects,
               nokqzongjianprojects,
               description,
               tjdate,
               (select x.dict_label
                  from sys_dict_data x, sys_dict_type y
                 where x.dict_type_id = y.id
                   and y.dict_type = 'VIRAREACODE'
                   and x.dict_value = t.areacode) as areacode
          from b_tj03 t
         where t.areacode like #{areacode} || '%'
           and to_char(t.tjdate, 'yyyy-MM-dd') = #{tjdate}
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if>
        <if test="stupstatus == null or stupstatus==''">
            and t.stupstatus is null
        </if>
           order by t.sort
    </select>
    <select id="attendance51Statistics" resultType="java.lang.String">
        select t.description
          from B_TJ03 t
         where t.areacode = #{areacode}
           and to_char(t.tjdate, 'yyyy-MM-dd') = #{tjdate}
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if>
        <if test="stupstatus == null or stupstatus==''">
            and t.stupstatus is null
        </if>
    </select>
    <select id="attendance52Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance52StatisticsDTO">
        select id,
                name,
                tjdate
                from B_TJ03_INFO t
        where 1 = 1
        <if test="tjdate !=null and tjdate !=''">
            and to_char(t.tjdate, 'yyyy-MM-dd') = #{tjdate}
        </if>
        <if test="name !=null and name !=''">
            and t.name like '%'||#{name}||'%'
        </if>
    </select>
    <select id="salary1Statistics" resultType="io.renren.modules.regul.statistics.dto.Salary1StatisticsDTO">
        select t.*
          from (select t.name,
                       a.areacode,
                       t.linkman,
                       t.linkphone,
                       t.startdate,
                       t.address,
                       (select count(1)
                          from b_ps02 x
                         where x.pj0101 = t.pj0101
                           and x.in_or_out = '1') as workers
                  from B_PJ01 t, sys_dept a, r_pj01_dept b
                 where t.dept_id = a.id
                   and t.pj0101 = b.pj0101
                   and t.pj0101 in
                       (select x.pj0101
                          from b_pa03 x
                         where x.accountdate &lt; trunc(add_months(sysdate, -1), 'mm')
                         group by x.pj0101)
                   and b.dept_id = #{deptId}
                <if test="areacode !=null and areacode !=''">
                    and a.areacode = #{areacode}
                </if>
                <if test="name !=null and name !=''">
                    and t.name like '%'||#{name}||'%'
                </if>
                   ) t
         order by t.workers desc
    </select>
    <select id="salary2Statistics" resultType="io.renren.modules.regul.statistics.dto.Salary2StatisticsDTO">
        select t.dict_label as areacode,
               decode(#{quarter},
                      '1',
                      '一季度',
                      '2',
                      '二季度',
                      '3',
                      '三季度',
                      '4',
                      '四季度') as quarter,
               fun_get_quartersalary(t.dict_value, #{quarter}, '1') as projects,
               fun_get_quartersalary(t.dict_value, #{quarter}, '2') as salarysum,
               fun_get_quartersalary(t.dict_value, #{quarter}, '3') as noprojects
          from sys_dict_data t
         where t.dict_type_id = 1593508519516733442
           and t.dict_value like #{areacode}||'%'
    </select>
    <select id="salary3Statistics" resultType="io.renren.modules.regul.statistics.dto.Salary3StatisticsDTO">
        select t.corpname,
               t.corpcode,
               t.legalman,
               t.linkman,
               t.linkcellphone,
               t.projects,
               (t.projects - t.inprojects) as noprojects
          from (select t.corpname,
                       t.corpcode,
                       t.legalman,
                       t.linkman,
                       t.linkcellphone,
                       (select count(count(1))
                          from b_cp02 x
                         where x.cp0101 = t.cp0101
                         group by x.pj0101) as projects,
                       (select count(count(1))
                          from b_pa02 x, b_cp02 y
                         where x.pj0101 = y.pj0101
                           and y.cp0101 = t.cp0101
                           and x.accountdate &gt; trunc(add_months(sysdate, -1), 'mm')
                         group by x.pj0101) as inprojects
                  from b_cp01 t, b_cp02 a, b_pj01 b, sys_dept c, r_pj01_dept d
                 where t.cp0101 = a.cp0101
                   and a.pj0101 = b.pj0101
                   and a.corptype = '9'
                   and b.dept_id = c.id
                   and b.pj0101 = d.pj0101
                   and d.dept_id = #{deptId}
                <if test="areacode !=null and areacode !=''">
                    and c.areacode = #{areacode}
                </if>
                <if test="corpname !=null and corpname !=''">
                    and t.corpname like '%'||#{corpname}||'%'
                </if>
                   ) t order by (t.projects - t.inprojects) desc
    </select>
    <select id="complaint1Statistics" resultType="io.renren.modules.regul.statistics.dto.ComplaintStatisticsDTO">
        select t.areacode, t.complaints, t.replys
          from (select t.dict_label as areacode,
                       (select count(1)
                          from b_jg07 x, b_pj01 y, sys_dept z
                         where x.pj0101 = y.pj0101
                           and y.dept_id = z.id
                           and z.areacode = t.dict_value
                           and x.complaintsubject = '1') complaints,
                       (select count(1)
                          from b_jg07 x, b_pj01 y, sys_dept z
                         where x.pj0101 = y.pj0101
                           and y.dept_id = z.id
                           and z.areacode = t.dict_value
                           and x.complaintsubject = '1'
                           and x.complaintstatus = '1') replys
                  from sys_dict_data t
                 where t.dict_type_id = 1593508519516733442
                   and t.dict_value like #{areacode}||'%') t
         order by t.complaints desc
    </select>
    <select id="complaint2Statistics" resultType="io.renren.modules.regul.statistics.dto.ComplaintStatisticsDTO">
        select t.areacode, t.complaints, t.replys
                  from (select t.dict_label as areacode,
                               (select count(1)
                                  from b_jg07 x, b_pj01 y, sys_dept z
                                 where x.pj0101 = y.pj0101
                                   and y.dept_id = z.id
                                   and z.areacode = t.dict_value
                                   and x.complaintsubject = '2') complaints,
                               (select count(1)
                                  from b_jg07 x, b_pj01 y, sys_dept z
                                 where x.pj0101 = y.pj0101
                                   and y.dept_id = z.id
                                   and z.areacode = t.dict_value
                                   and x.complaintsubject = '2'
                                   and x.complaintstatus = '1') replys
                          from sys_dict_data t
                         where t.dict_type_id = 1593508519516733442
                           and t.dict_value like #{areacode}||'%') t
                 order by t.complaints desc
    </select>
    <select id="getProjectListByCorp" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select t.pj0101 as value, t.name as label
          from B_PJ01 t, b_cp02 a
         where t.pj0101 = a.pj0101
           and a.cp0101 = #{creditno}
    </select>
    <select id="getProjectListByTeam" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select t.pj0101 as value, t.name as label
          from B_PJ01 t, b_tm01 a
         where t.pj0101 = a.pj0101
           and a.tm0101 = #{creditno}
    </select>
    <select id="getProjectListByPerson" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select t.pj0101 as value, t.name as label
          from B_PJ01 t, b_ps02 a
         where t.pj0101 = a.pj0101
           and a.ps0101 = #{creditno}
    </select>
    <select id="getProjectList" resultType="io.renren.modules.admin.sys.entity.DictDTO">
        select t.pj0101 as value, t.name as label from B_PJ01 t
    </select>
    <select id="salaryPageList" resultType="io.renren.modules.regul.pj01.dto.Pa03PageDTO">
        select t.pa0301,
               t.pa0201,
               a.ps0201,
               a.ps0101,
               t.accountdate,
               t.accountnum,
               t.partaccount,
               b.name
          from B_PA03 t, b_ps02 a, b_ps01 b
         where t.ps0201 = a.ps0201
           and a.ps0101 = b.ps0101
           and t.pj0101 = #{pj0101}
        <if test="startTime != null and startTime != ''">
            and to_Char(t.accountdate, 'yyyy-MM-dd') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(t.accountdate, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
         order by t.accountdate desc
    </select>
    <select id="guaranteePageList" resultType="io.renren.modules.regul.pj01.dto.Cp05PageDTO">
        select t.*, a.corpname
          from B_CP05 t, b_cp01 a
         where t.cp0101 = a.cp0101
           and t.pj0101 = #{pj0101}
    </select>

    <select id="exportPj01Info" resultType="io.renren.modules.regul.pj01.dto.Pj01ExportInfoDTO">
        select rownum as sno, h.*,
        nvl(j.kqs, 0) as kqworkers,
        nvl(k.kqs, 0) as kqmanagers,
        nvl(o.ps02s, 0) as workers,
        nvl(p.ps04s, 0) as managers
        from (select t.pj0101,
        (select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 1593508519516733442
        and x.dict_value = b.areacode) as areacode,
        b.areacode as virareacode,
        t.name,
        t.linkman,
        t.linkphone,
        t.prjstatus,
        t.create_date,
        (select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 3
        and x.dict_value = t.prjstatus) as prjstatusDictLabel,
        (select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 1597158380227162113
        and x.dict_value = t.stupstatus) as stupstatus,
        t.startdate,
        t.complete_date,
        (select x.corpname
        from b_cp01 x, b_cp02 y
        where x.cp0101 = y.cp0101
        and y.pj0101 = t.pj0101
        and y.corptype = '7'
        and rownum &lt; 2) as jianli,
        (select x.corpname
        from b_cp01 x, b_cp02 y
        where x.cp0101 = y.cp0101
        and y.pj0101 = t.pj0101
        and y.corptype = '9'
        and rownum &lt; 2) as zongbao,
        q.name as zongjian,
        w.name as jingli
        from B_PJ01 t left join (select *
        from (select row_number() over(partition by a2.pj0101 order by a2.pj0101) a1,
        a2.name,
        a2.pj0101
        from (select z.pj0101, x.name
        from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 m
        where x.ps0101 = y.ps0101
        and y.ps0301 = z.ps0301
        and z.cp0201 = m.cp0201
        and m.corptype = '7'
        and z.jobtype = '1001'
        and z.in_or_out = '1') a2)
        where a1 = 1) q
        on t.pj0101 = q.pj0101
        left join (select *
        from (select row_number() over(partition by a2.pj0101 order by a2.pj0101) a1,
        a2.name,
        a2.pj0101
        from (select z.pj0101, x.name
        from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 m
        where x.ps0101 = y.ps0101
        and y.ps0301 = z.ps0301
        and z.cp0201 = m.cp0201
        and m.corptype = '9'
        and z.jobtype = '1009'
        and z.in_or_out = '1') a2)
        where a1 = 1) w
        on t.pj0101 = w.pj0101, r_pj01_dept a, sys_dept b
        where t.pj0101 = a.pj0101
        and a.dept_id = #{deptId}
        and t.dept_id = b.id
        <if test="name !=null and name !=''">
            and t.name like '%'||#{name}||'%'
        </if>
        <if test="areacode !=null and areacode !=''">
            and b.areacode = #{areacode}
        </if>
        <if test="prjstatus !=null and prjstatus !=''">
            and t.prjstatus = #{prjstatus}
        </if>
        <if test="startTime != null and startTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(t.create_date, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
        <if test="collectstatus != null and collectstatus != '' and collectstatus == 1">
            and EXISTS (select 1 from b_pj01_collect c where c.user_id = #{userId} and c.pj0101 = t.pj0101)
        </if>
        <if test="collectstatus != null and collectstatus != '' and collectstatus == 0">
            and NOT EXISTS (select 1 from b_pj01_collect c where c.user_id = #{userId} and c.pj0101 = t.pj0101)
        </if>
        <if test="stupstatus !=null and stupstatus !=''">
            and t.stupstatus = #{stupstatus}
        </if> order by t.create_date desc) h
        left join (select t.pj0101, count(distinct(t.user_id)) as kqs
        from b_kq02_now t
        where to_char(t.checkdate, 'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd')
        and t.person_type = '1'
        group by t.pj0101) j
        on h.pj0101 = j.pj0101
        left join (select t.pj0101, count(distinct(t.user_id)) as kqs
        from b_kq02_now t
        where to_char(t.checkdate, 'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd')
        and t.person_type = '2'
        group by t.pj0101) k
        on h.pj0101 = k.pj0101
        left join (select t.pj0101, count(1) as ps02s
        from b_ps02 t
        where t.in_or_out = '1'
        group by t.pj0101) o
        on h.pj0101 = o.pj0101
        left join (select t.pj0101, count(1) as ps04s
        from b_ps04 t
        where t.in_or_out = '1'
        group by t.pj0101) p
        on h.pj0101 = p.pj0101
    </select>

    <select id="getCityStateSysDictData" resultType="java.lang.String">
        select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 1593508519516733442 order by x.SORT
    </select>

    <select id="projectDateAttendanceInfoDTOList"
            resultType="io.renren.modules.regul.statistics.dto.ProjectDateAttendanceInfoDTO">

        select g.name,
               g.areacode,
               g.synthesizepercent,
               g.jingli,
               g.jinglipercent,
               g.zongjian,
               g.zongjianpercent
        from (select h.*,
                     round(decode(2,
                                  0,
                                  0,
                                  (nvl((h.jinglipercent2 + h.zongjianpercent2), 0) / 2) * 100),
                           2) || '%' as synthesizepercent
              from (select t.name,
                           (select x.dict_label
                            from sys_dict_data x
                            where x.dict_type_id = 1593508519516733442
                              and x.dict_value = t.areacode) as areacode,
                           (select x.name
                            from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 x
                            where x.ps0101 = y.ps0101
                              and y.ps0301 = z.ps0301
                              and z.pj0101 = t.pj0101
                              and z.cp0201 = x.cp0201
                              and x.corptype = '9'
                              and z.jobtype = '1009'
                              and z.in_or_out = '1'
                              and rownum &lt; 2) as jingli,
                           case
                               when t.jinglipercent = '0%' then
                                   '否'
                               when t.jinglipercent = '请假' then
                                   '请假'
                               else
                                   '是'
                               end as jinglipercent,
                           case
                               when t.jinglipercent = '0%' then
                                   0
                               when t.jinglipercent = '请假' then
                                   0
                               else
                                   1
                               end as jinglipercent2,
                           (select x.name
                            from b_ps01 x, b_ps03 y, b_ps04 z, b_cp02 x
                            where x.ps0101 = y.ps0101
                              and y.ps0301 = z.ps0301
                              and z.pj0101 = t.pj0101
                              and z.cp0201 = x.cp0201
                              and x.corptype = '7'
                              and z.jobtype = '1001'
                              and z.in_or_out = '1'
                              and rownum &lt; 2) as zongjian,
                           case
                               when t.zongjianpercent = '0%' then
                                   '否'
                               when t.zongjianpercent = '请假' then
                                   '请假'
                               else
                                   '是'
                               end as zongjianpercent,
                           case
                               when t.zongjianpercent = '0%' then
                                   0
                               when t.zongjianpercent = '请假' then
                                   0
                               else
                                   1
                               end as zongjianpercent2,
                           t.tjdate
                    from B_TJ02_INFO t
                    where to_char(t.tjdate, 'yyyy-MM-dd') = #{today}) h) g
        where g.jinglipercent in ('否', '请假')
           or g.zongjianpercent in ('否', '请假')
        order by g.areacode,
                 g.synthesizepercent,
                 g.jinglipercent,
                 g.zongjianpercent
    </select>
    <select id="getProjectSupplement" resultType="java.lang.Long">
        select count(1)
          from B_PJ01 t
         where t.pj0101 = #{pj0101}
           and (t.category is null
            or t.constructtype is null
            or t.investtype is null
            or t.linkman is null
            or t.linkphone is null
            or t.buildingarea is null
            or t.invest is null
            or t.scale is null
            or t.startdate is null
            or t.address is null)
    </select>

    <select id="getAttendanceKeyPositionsStatistics"
            resultType="io.renren.modules.regul.statistics.dto.AttendanceKeyPositionsStatisticsDTO">

        select a.dict_label      as areacodename,
               t.ZJPROJECTS      as procount,
               t.JINGLIPERCENT,
               t.ZONGJIANPERCENT
        from (select x.dict_label, x.dict_value, x.SORT
              from sys_dict_data x, sys_dict_type y
              where x.dict_type_id = y.id
                and y.dict_type = 'VIRAREACODE'
                and x.dict_value like '%' || #{areaCode} || '%'
                and x.status = '1'
              order by x.SORT asc) a,
             (select t.areacode, t.zjprojects, t.JINGLIPERCENT, t.ZONGJIANPERCENT
              from B_TJ02 t
              where to_char(t.tjdate, 'yyyy-MM-dd') = #{today}
                 <choose>
                      <when test="isupload != null and isupload != ''">
                          and t.stupstatus is not null
                      </when>
                      <otherwise>
                         and t.stupstatus is null
                      </otherwise>
                  </choose>
               ) t
        where t.areacode = a.dict_value
        order by a.sort asc
    </select>
    <select id="attendance45Description" resultType="java.lang.String">
        select '人员姓名：' || b.name || '——岗位：' ||
        (select x.dict_label
        from sys_dict_data x
        where x.dict_type_id = 1390482614346858498
        and x.dict_value = t.jobtype) || '——到岗天数：' ||
        (select count(1)
        from b_tj04 y
        where y.userid = t.ps0401
        and y.person_type = '2'
        and y.in_or_out = '1'
        and y.kqstatus = '1'
        and to_char(y.kqday, 'yyyy-MM') &gt;= #{startdate}
        and to_char(y.kqday, 'yyyy-MM') &lt;= #{enddate}) as description
        from b_ps04 t, b_ps03 a, b_ps01 b
        where t.ps0301 = a.ps0301
        and a.ps0101 = b.ps0101
        and t.ps0401 = #{userId}
    </select>
    <select id="attendance45Statistics" resultType="io.renren.modules.regul.statistics.dto.Attendance45StatisticsDTO">
        select h.*,
               round(decode(days, 0, 0, (nvl(kqdays, 0) / days) * 100), 2) || '%' as arrivepercent
          from (select t.*,
                       (select count(1)
                          from b_tj04 x
                         where x.userid = #{userId}
                           and x.kqstatus = '1'
                           and x.in_or_out = '1'
                           and x.person_type = '2'
                           and to_char(x.kqday, 'yyyy-MM') = t.month) as kqdays,
                       TO_CHAR(TRUNC(LAST_DAY(to_date(t.month, 'yyyy-MM'))), 'DD') AS days
                  from SYS_MONTHS t
                 where t.month &gt;= #{startdate}
                   and t.month &lt;= #{enddate}) h
         order by h.id
    </select>

    <select id="projectProcess" resultType="io.renren.modules.regul.pj01.dto.Pj01StatusProcessDTO">
        select t.prjstatus as status, t.update_date as process_date
        from b_pj09 t
        where t.pj0101 = #{pj0101}
        order by t.pj0901 asc
    </select>

    <select id="getWorkDay" resultType="java.lang.String">
        select count(*) from b_pj01_operation t where t.pj0101 = #{pj0101}
        and to_char(t.OPERATIONDAY, 'yyyy-MM') &gt;= #{startdate}
        and to_char(t.OPERATIONDAY, 'yyyy-MM') &lt;= #{enddate}
    </select>

    <select id="getAttendanceDay" resultType="java.lang.String">
<!--        select count(*)-->
<!--        from (select count(to_char(t.kqday, 'yyyy-MM-dd')) a, count(1)-->
<!--        from b_tj04 t-->
<!--        where t.pj0101 = #{params.pj0101}-->
<!--        AND t.PERSON_TYPE = #{personType}-->
<!--        and t.kqstatus = '1'-->
<!--        and to_char(t.kqday, 'yyyy-MM') &gt;= #{params.startdate}-->
<!--        and to_char(t.kqday, 'yyyy-MM') &lt;= #{params.enddate}-->
<!--        group by to_char(t.kqday, 'yyyy-MM-dd'))-->

<!--        select count(1)-->
<!--        from (SELECT TO_CHAR(t.OPERATIONDAY, 'yyyy-MM-dd') AS OPERATIONDAY-->
<!--        FROM b_pj01_operation t-->
<!--        WHERE t.pj0101 = #{params.pj0101}-->
<!--        AND TO_CHAR(t.OPERATIONDAY, 'yyyy-MM') &gt;= #{params.startdate}-->
<!--        AND TO_CHAR(t.OPERATIONDAY, 'yyyy-MM') &lt;= #{params.enddate}) t-->
<!--        left join (select count(to_char(t.kqday, 'yyyy-MM-dd')) a,-->
<!--        count(1),-->
<!--        to_char(t.kqday, 'yyyy-MM-dd') kday-->
<!--        from b_tj04 t-->
<!--        where t.pj0101 = #{params.pj0101}-->
<!--        and t.PERSON_TYPE = #{personType}-->
<!--        and t.kqstatus = '1'-->
<!--        group by to_char(t.kqday, 'yyyy-MM-dd')) z-->
<!--        on t.operationday = z.kday-->
        select count(*)
        from (SELECT TO_CHAR(t.OPERATIONDAY, 'yyyy-MM-dd') AS OPERATIONDAY
        FROM b_pj01_operation t
        WHERE t.pj0101 = #{params.pj0101}
        AND TO_CHAR(t.OPERATIONDAY, 'yyyy-MM') &gt;= #{params.startdate}
        AND TO_CHAR(t.OPERATIONDAY, 'yyyy-MM') &lt;= #{params.enddate}) t,
        (select count(to_char(t.kqday, 'yyyy-MM-dd')) a,
        count(1),
        to_char(t.kqday, 'yyyy-MM-dd') kday
        from b_tj04 t
        where t.pj0101 = #{params.pj0101}
        and t.PERSON_TYPE = #{personType}
        and t.kqstatus = '1'
        group by to_char(t.kqday, 'yyyy-MM-dd')) z
        where t.operationday = z.kday
    </select>

    <select id="projectKeyPositionsPersonList" resultType="io.renren.modules.regul.pj01.dto.Pj01KeyPositionsDTO">

        <foreach collection="list" item="item" separator="union all">
        select t.ps0401, b.name, b.idcardnumber, t.entrytime, t.jobtype, t.in_or_out, t.exittime
        from b_ps01 b, b_ps03 p, b_ps04 t, b_cp02 c
        where b.ps0101 = p.ps0101
          and p.ps0301 = t.ps0301
          and c.cp0201 = t.cp0201
          and t.jobtype = #{item.jobtype}
          and c.corptype = #{item.corptype}
          and t.pj0101 = #{pj0101}
        </foreach>
    </select>

    <select id="getKeyPositionsAttendanceMonthList"
            resultType="io.renren.modules.regul.pj01.dto.Pj01AttendanceMonthDTO">
        select g.*,
               g.kqs || '|' ||
               to_char(round(decode(g.days, 0, 0, (nvl(g.kqs, 0) / g.days) * 100), 2),'fm9999999990.00') || '%' as arrivepercent
        from (select t.month,
                     case
                         when t.month = to_char(sysdate, 'yyyy-MM') then
                             to_char(sysdate, 'dd')
                         else
                             t.days
                         end as days,
                     nvl(h.kqs, 0) as kqs
              from sys_months t
                       left join (select to_char(x.kqday, 'yyyy-MM') as kqmonth,
                                         count(1) as kqs
                                  from b_tj04 x
                                  where x.userid = #{ps0401}
                                    and x.person_type = '2'
                                    and x.kqstatus = '1'
                                    and to_char(x.kqday, 'yyyy-MM') &gt;= #{params.startdate}
                                    and to_char(x.kqday, 'yyyy-MM') &lt;= #{params.enddate}
                                  group by to_char(x.kqday, 'yyyy-MM')) h
                                 on t.month = h.kqmonth
              where t.month &lt;= #{params.enddate}
                and t.month &gt;= #{params.startdate}
              order by t.month) g
    </select>

    <select id="projectKeyPositionsRate" resultType="io.renren.modules.regul.pj01.dto.Pj01KeyPositionsDTO">
        select h.*,
               to_char(round(decode(days, 0, 0, (nvl(kqs, 0) / days) * 100), 2),'fm9999999990.00') || '%' as arrivepercent
        from (select (select count(1)
                      from b_tj04 x
                      where x.pj0101 = t.pj0101
                        and x.userid = t.ps0401
                        and x.person_type = '2'
                        and x.kqstatus = '1'
                        and to_char(x.kqday, 'yyyy-MM') &gt;= #{params.startdate}
                        and to_char(x.kqday, 'yyyy-MM') &lt;= #{params.enddate}) as kqs,
                     case
                         when to_char(sysdate, 'yyyy-MM') = #{params.enddate} then
                             trunc(sysdate - to_date(#{params.startdate}, 'yyyy-MM')) + 1
                         else
                                     LAST_DAY(to_date(#{params.enddate}, 'yyyy-MM')) -
                                     to_date(#{params.startdate}, 'yyyy-MM') + 1
                         end as days,
                      t.ps0401
              from B_PS04 t
              where t.ps0401 in
        <foreach item="item" collection="lists" separator="," open="(" close=")" index="">
            #{item}
        </foreach> ) h
        order by h.kqs desc
    </select>

    <select id="getWorkerAndManagerTotalDay" resultType="java.lang.String">
<!--        select count(count(*)) as totalNum-->
<!--        from (select to_char(t.kqday, 'yyyy-MM-dd') as kqday,-->
<!--        t.person_type,-->
<!--        count(to_char(t.kqday, 'yyyy-MM-dd')) asum-->
<!--        from b_tj04 t-->
<!--        where t.pj0101 = #{pj0101}-->
<!--        and t.kqstatus = '1'-->
<!--        and to_char(t.kqday, 'yyyy-MM') &gt;= #{startdate}-->
<!--        and to_char(t.kqday, 'yyyy-MM') &lt;= #{enddate}-->
<!--        group by t.PERSON_TYPE, to_char(t.kqday, 'yyyy-MM-dd')-->
<!--        order by to_char(t.kqday, 'yyyy-MM-dd')) t-->
<!--        group by t.kqday-->
<!--        having count(*) > 1-->

        select count(count(1)) totalNum
        from (select *
        from (SELECT TO_CHAR(t.OPERATIONDAY, 'yyyy-MM-dd') AS OPERATIONDAY
        FROM b_pj01_operation t
        WHERE t.pj0101 = #{pj0101}
        AND TO_CHAR(t.OPERATIONDAY, 'yyyy-MM') &gt;= #{startdate}
        AND TO_CHAR(t.OPERATIONDAY, 'yyyy-MM') &lt;= #{enddate}) a
        left join (select to_char(t.kqday, 'yyyy-MM-dd') as kqday,
        t.person_type,
        count(to_char(t.kqday, 'yyyy-MM-dd')) asum
        from b_tj04 t
        where t.pj0101 = #{pj0101}
        and t.kqstatus = '1'
        and to_char(t.kqday, 'yyyy-MM') &gt;= #{startdate}
        and to_char(t.kqday, 'yyyy-MM') &lt;= #{enddate}
        group by t.PERSON_TYPE, to_char(t.kqday, 'yyyy-MM-dd')
        order by to_char(t.kqday, 'yyyy-MM-dd')) z
        on a.operationday = z.kqday) t
        where t.kqday is not null
        and t.person_type is not null
        and t.asum is not null
        group by t.kqday
        having count(*) > 1
    </select>

    <select id="selectTj03InfoDetail" resultType="io.renren.modules.regul.statistics.dto.Tj03InfoDetailDTO">
        select * from b_tj03_info_detail
        where tj03_id = #{id}
    </select>

</mapper>
