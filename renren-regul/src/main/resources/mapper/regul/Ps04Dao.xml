<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.ps04.dao.Ps04Dao">
    <select id="getListData" resultType="io.renren.modules.regul.ps04.dto.Ps04PageDTO">
        select d.name,
               d.ps0101,
               d.idcardnumber,
               d.gender,
               a.name as projectname,
               c.corpcode,
               c.corpname,
               d.cellphone,
               t.ps0401,
               t.jobtype
        from b_ps01 d,
             b_ps03 s,
             b_ps04 t,
             b_pj01 a,
             R_PJ01_DEPT b,
             b_cp01 c
        where t.pj0101 = a.pj0101
          and t.ps0301 = s.ps0301
          and s.ps0101 = d.ps0101
          and s.cp0101 = c.cp0101
          and a.PJ0101 = b.PJ0101
          and b.DEPT_ID = #{deptId}
        <!--        and b.areacode like #{areacode} || '%'-->
        <if test="prjstatus != null and prjstatus != ''">
            and a.prjstatus = #{prjstatus}
        </if>
        <if test="inOrOut != null and inOrOut != ''">
            and t.in_or_out = #{inOrOut}
        </if>
        <if test="name != null and name != ''">
            and d.name like '%' || #{name} || '%'
        </if>
        <if test="corpname != null and corpname != ''">
            and c.corpname like '%' || #{corpname} || '%'
        </if>
        <if test="idcardnumber != null and idcardnumber != ''">
            and d.idcardnumber = #{idcardnumber}
        </if>
        <if test="jobtype != null and jobtype != ''">
            and t.jobtype = #{jobtype}
        </if>
        <if test="projectname != null and projectname != ''">
            and a.name like '%' || #{projectname} || '%'
        </if>
    </select>

    <select id="getManagerInfo" resultType="io.renren.modules.regul.ps04.dto.Ps04DTO">
        select (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
               d.corpcode,
               d.corpname,
               c.corptype,
               b.jobtype,
               b.entrytime,
               b.exittime,
               b.in_or_out,
               t.ps0301,
               b.ps0401
        from b_ps03 t,
             b_ps01 a,
             b_ps04 b,
             b_cp02 c,
             b_cp01 d
        where t.pj0101 = b.pj0101
          and t.ps0301 = b.ps0301
          and t.ps0101 = a.ps0101
          and b.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and a.ps0101 = #{ps0101}
    </select>
    <select id="empRecordPage" resultType="io.renren.modules.regul.ps04.dto.Ps04DTO">
        select b.name,
               b.idcardnumber,
               (select x.name from b_pj01 x where x.pj0101 = t.pj0101) as projectname,
               d.corpname,
               d.corpcode,
               c.corptype,
               t.jobtype,
               t.entrytime,
               t.exittime,
               t.in_or_out,
               t.pj0101,
               c.cp0101
        from b_ps04 t,
             b_ps03 a,
             b_ps01 b,
             b_cp02 c,
             b_cp01 d
        where t.ps0301 = a.ps0301
          and a.ps0101 = b.ps0101
          and t.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and b.ps0101 = #{ps0101}
    </select>
    <select id="selectInfoByPs03AndPj01" resultType="io.renren.modules.regul.ps04.entity.Ps04Entity">
        select t.*
        from B_PS04 t
        where t.pj0101 = #{pj0101}
          and t.ps0301 = #{ps0301}
          and t.in_or_out = '1'
    </select>
    <select id="selectListByIds" resultType="io.renren.modules.regul.ps04.dto.Ps04DTO">
        select t.*,
               b.ps0101,
               b.name,
               b.cellphone,
               c.name as projectname,
               d.corpname,
               d.corpcode,
               e.corptype
        from B_PS04 t,
             b_ps03 a,
             b_ps01 b,
             b_pj01 c,
             b_cp01 d,
             b_cp02 e
        where t.ps0301 = a.ps0301
          and a.ps0101 = b.ps0101
          and t.pj0101 = c.pj0101
          and t.cp0201 = e.cp0201
          and e.cp0101 = d.cp0101
          and t.ps0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="getAttendancePageList" resultType="io.renren.modules.regul.pj01.dto.Kq02PageDTO">
        select a.KQ0201,
               a.PERSON_NAME personName,
               a.PERSON_TYPE personType,
               a.DIRECTION,
               a.ATTENDTYPE  attendType,
               a.CHECKDATE   checkDate,
               a.IMAGE_URL   imageUrl
        from B_KQ02 a
        where a.USER_ID = #{ps0401}
          and a.PERSON_TYPE = '2'
        order by a.checkdate desc
    </select>
    <select id="confirmPageList" resultType="io.renren.modules.regul.ps04.dto.Ps04ConfirmDTO">
        select t.ps0401,
               b.name,
               b.idcardnumber,
               t.jobtype,
               d.corpname,
               d.corpcode,
               c.corptype,
               e.name as projectname,
               e.prjstatus,
               t.create_date,
               t.isconfirm,
               t.confirmresult,
               t.confirmor,
               t.confirm_date
        from B_PS04 t,
             b_ps03 a,
             b_ps01 b,
             b_cp02 c,
             b_cp01 d,
             b_pj01 e,
             b_ps13 f
        where t.ps0301 = a.ps0301
          and a.ps0101 = b.ps0101
          and t.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and t.pj0101 = e.pj0101
          and t.in_or_out = '1'
          and t.jobtype = f.jobtype
          and c.corptype = f.corptype
        <if test="name != null and name != ''">
            and b.name like '%' || #{name} || '%'
        </if>
        <if test="projectname != null and projectname != ''">
            and e.name like '%' || #{projectname} || '%'
        </if>
        <if test="idcardnumber != null and idcardnumber != ''">
            and b.idcardnumber = #{idcardnumber}
        </if>
        <if test="isconfirm != null and isconfirm != ''">
            and t.isconfirm = #{isconfirm}
        </if>
        <if test="prjstatus != null and prjstatus != ''">
            and e.prjstatus = #{prjstatus}
        </if>
        order by t.isconfirm, t.create_date desc
    </select>
    <select id="selectKeyjobList" resultType="io.renren.modules.regul.ps04.dto.Ps04PageDTO">
        select t.ps0401,
               b.name,
               b.idcardnumber,
               t.jobtype,
               d.corpname,
               c.corptype,
               t.isconfirm,
               t.confirmresult,
               t.confirm_date
        from B_PS04 t,
             b_ps03 a,
             b_ps01 b,
             b_cp02 c,
             b_cp01 d
        where t.ps0301 = a.ps0301
          and a.ps0101 = b.ps0101
          and t.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and t.in_or_out = '1'
          and t.jobtype = '1009'
          and c.corptype = '9'
          and t.pj0101 = #{pj0101}
          and rownum &lt; 2
        union all
        select t.ps0401,
               b.name,
               b.idcardnumber,
               t.jobtype,
               d.corpname,
               c.corptype,
               t.isconfirm,
               t.confirmresult,
               t.confirm_date
        from B_PS04 t,
             b_ps03 a,
             b_ps01 b,
             b_cp02 c,
             b_cp01 d
        where t.ps0301 = a.ps0301
          and a.ps0101 = b.ps0101
          and t.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and t.in_or_out = '1'
          and t.jobtype = '1001'
          and c.corptype = '7'
          and t.pj0101 = #{pj0101}
          and rownum &lt; 2
    </select>

    <select id="selectPersonByIds" resultType="io.renren.modules.supdevicetask.dto.PersonDTO">
        select a.NAME, b.PS0401 userId, b.PHOTO imageUrl
        from b_ps01 a,
             b_ps04 b,
             b_ps03 c where a.ps0101 = c.ps0101
                        and c.ps0301 = b.ps0301
                        and b.PS0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

    <update id="exitPs03ByIds">
        update B_PS03 t
        set t.pj0101 = null
        where t.ps0301 in (select a.ps0301
                           from b_ps04 a where a.ps0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>)
    </update>

    <update id="updateInOrOutByIds">
        update B_PS04 t
        set t.IN_OR_OUT = #{type},
        <if test="type == 1">
            t.entrytime = sysdate,
            t.exittime  = null
        </if>
        <if test="type == 2">
            t.exittime  = sysdate
        </if>
        where t.PS0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>
</mapper>