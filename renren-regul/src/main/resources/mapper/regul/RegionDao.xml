<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.regul.region.dao.RegionDao">
    <select id="pageList" resultType="io.renren.modules.regul.region.dto.RegionDTO">
        select t.*, a.name, b.areacode, a.prjstatus
            from B_PJ01_REGION t, b_pj01 a, sys_dept b, r_pj01_dept c
            where t.pj0101 = a.pj0101
            and t.pj0101 = c.pj0101
            and a.dept_id = b.id
            and c.dept_id = #{deptId}
        <if test="name != null and name != ''">
            and a.name like '%'||#{name}||'%'
        </if>
        <if test="auditstatus != null and auditstatus != ''">
            and t.auditstatus = #{auditstatus}
        </if>
        <if test="areacode != null and areacode != ''">
            and b.areacode = #{areacode}
        </if>
        <if test="prjstatus != null and prjstatus != ''">
            and a.prjstatus = #{prjstatus}
        </if>
        order by t.auditstatus,t.create_date desc
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.regul.region.dto.RegionDTO">
        select t.*, a.name
          from b_pj01_region t, b_pj01 a
         where t.pj0101 = a.pj0101
           and t.id = #{id}
    </select>
</mapper>
